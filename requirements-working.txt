# Essential web framework
fastapi>=0.100.0
uvicorn[standard]>=0.23.0
pydantic>=2.0.0
python-multipart>=0.0.6

# CLI and UI
rich>=13.0.0
click>=8.1.0

# Async and file handling
aiofiles>=0.8.0
aiohttp>=3.8.0

# Document processing
python-docx>=0.8.11
PyPDF2>=3.0.0
beautifulsoup4>=4.12.0

# Graph and data processing
networkx>=3.1
numpy>=1.21.0

# LLM and AI
openai>=1.0.0
httpx>=0.24.0
requests>=2.31.0

# Web templates
jinja2>=3.1.0

# Utilities
loguru>=0.7.0
python-dotenv>=1.0.0
pyyaml>=6.0

# Security
bleach>=6.0.0

# Optional: Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
