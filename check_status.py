#!/usr/bin/env python3
"""Status check script for Knowledge Graph Builder."""

import requests
import json
import sys
from pathlib import Path

def check_web_service():
    """Check if web service is running."""
    try:
        response = requests.get("http://localhost:8001/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Web Service: {data.get('status', 'unknown')} (v{data.get('version', 'unknown')})")
            return True
        else:
            print(f"❌ Web Service: HTTP {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Web Service: Not accessible ({e})")
        return False

def check_api_endpoints():
    """Check API endpoints."""
    endpoints = [
        ("/health", "Health Check"),
        ("/api/statistics", "Statistics API"),
    ]
    
    print("\n🔍 API Endpoints:")
    for endpoint, description in endpoints:
        try:
            response = requests.get(f"http://localhost:8001{endpoint}", timeout=5)
            if response.status_code == 200:
                print(f"  ✅ {description}: OK")
            else:
                print(f"  ❌ {description}: HTTP {response.status_code}")
        except requests.exceptions.RequestException:
            print(f"  ❌ {description}: Not accessible")

def check_file_processing():
    """Test file processing via API."""
    print("\n📄 Testing File Processing:")
    
    # Check if test files exist
    test_files = ["test_documents/sample1.txt", "test_documents/sample2.txt"]
    
    for file_path in test_files:
        if Path(file_path).exists():
            print(f"  ✅ Test file: {file_path}")
        else:
            print(f"  ❌ Test file missing: {file_path}")
            return False
    
    # Test file upload (simulation)
    try:
        # We'll just check if the endpoint exists
        # Actual file upload would require multipart form data
        response = requests.post("http://localhost:8001/api/process", 
                               json={"test": "check"}, timeout=5)
        # We expect this to fail with 422 (validation error) which means endpoint exists
        if response.status_code in [400, 422]:
            print("  ✅ File processing endpoint: Available")
            return True
        else:
            print(f"  ❌ File processing endpoint: Unexpected response {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"  ❌ File processing endpoint: Not accessible ({e})")
        return False

def check_output_files():
    """Check if output files were generated."""
    print("\n📁 Output Files:")
    
    output_dir = Path("output")
    if output_dir.exists():
        print(f"  ✅ Output directory: {output_dir}")
        
        # Check for generated files
        json_file = output_dir / "knowledge_graph.json"
        stats_file = output_dir / "statistics.json"
        
        if json_file.exists():
            print(f"  ✅ Knowledge graph: {json_file}")
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    stats = data.get("statistics", {})
                    print(f"    📊 Entities: {stats.get('total_entities', 0)}")
                    print(f"    📊 Relations: {stats.get('total_relations', 0)}")
            except Exception as e:
                print(f"    ⚠️ Could not read JSON: {e}")
        else:
            print(f"  ❌ Knowledge graph file missing: {json_file}")
        
        if stats_file.exists():
            print(f"  ✅ Statistics file: {stats_file}")
        else:
            print(f"  ❌ Statistics file missing: {stats_file}")
    else:
        print(f"  ❌ Output directory missing: {output_dir}")

def check_dependencies():
    """Check if all required dependencies are available."""
    print("\n📦 Dependencies:")
    
    required_modules = [
        ("rich", "rich"),
        ("click", "click"),
        ("fastapi", "fastapi"),
        ("uvicorn", "uvicorn"),
        ("pydantic", "pydantic"),
        ("loguru", "loguru"),
        ("jinja2", "jinja2"),
        ("aiofiles", "aiofiles"),
        ("httpx", "httpx"),
        ("networkx", "networkx"),
        ("beautifulsoup4", "bs4"),
        ("bleach", "bleach"),
        ("numpy", "numpy"),
        ("requests", "requests")
    ]

    missing = []
    for display_name, import_name in required_modules:
        try:
            __import__(import_name)
            print(f"  ✅ {display_name}")
        except ImportError:
            print(f"  ❌ {display_name}")
            missing.append(display_name)
    
    if missing:
        print(f"\n⚠️ Missing dependencies: {', '.join(missing)}")
        print("Run: python quick_fix.py")
        return False
    
    return True

def main():
    """Main status check function."""
    print("🔍 Knowledge Graph Builder - Status Check")
    print("=" * 50)
    
    all_good = True
    
    # Check dependencies
    if not check_dependencies():
        all_good = False
    
    # Check web service
    if not check_web_service():
        all_good = False
    
    # Check API endpoints
    check_api_endpoints()
    
    # Check file processing
    if not check_file_processing():
        all_good = False
    
    # Check output files
    check_output_files()
    
    print("\n" + "=" * 50)
    
    if all_good:
        print("🎉 All systems operational!")
        print("\n📋 Quick Start:")
        print("1. Web Interface: http://localhost:8001")
        print("2. CLI Processing: python test_cli.py")
        print("3. Upload files via web interface")
        print("4. View results and download JSON")
        return 0
    else:
        print("⚠️ Some issues detected!")
        print("\n🔧 Troubleshooting:")
        print("1. Run: python quick_fix.py")
        print("2. Start web server: python web_simple.py")
        print("3. Check firewall/antivirus settings")
        return 1

if __name__ == "__main__":
    sys.exit(main())
