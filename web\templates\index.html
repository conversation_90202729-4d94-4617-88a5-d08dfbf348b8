{% extends "base.html" %}

{% block title %}知识图谱构建器 - 首页{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- Hero Section -->
        <div class="hero-section bg-gradient-primary text-white rounded-3 p-5 mb-5">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-3">知识图谱构建器</h1>
                    <p class="lead mb-4">
                        从文本文档自动构建知识图谱，支持多种文档格式，使用先进的大语言模型进行实体关系提取，
                        生成可视化的知识网络。
                    </p>
                    <div class="d-flex gap-3">
                        <a href="/upload" class="btn btn-light btn-lg">
                            <i class="fas fa-upload me-2"></i>开始上传
                        </a>
                        <a href="/graph" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-sitemap me-2"></i>查看图谱
                        </a>
                    </div>
                </div>
                <div class="col-lg-4 text-center">
                    <i class="fas fa-project-diagram" style="font-size: 8rem; opacity: 0.3;"></i>
                </div>
            </div>
        </div>

        <!-- Features Section -->
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="text-center mb-5">功能特性</h2>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-body text-center">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-file-alt fa-3x text-primary"></i>
                        </div>
                        <h5 class="card-title">多格式支持</h5>
                        <p class="card-text">
                            支持TXT、DOCX、PDF等多种文档格式，自动解析文档内容并进行语义分割。
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-body text-center">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-brain fa-3x text-success"></i>
                        </div>
                        <h5 class="card-title">智能提取</h5>
                        <p class="card-text">
                            使用先进的大语言模型进行实体和关系提取，支持并行处理，提高处理效率。
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-body text-center">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-eye fa-3x text-info"></i>
                        </div>
                        <h5 class="card-title">可视化展示</h5>
                        <p class="card-text">
                            交互式图形可视化界面，支持搜索、过滤和导航，直观展示知识网络结构。
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Section -->
        <div class="row mb-5" id="stats-section" style="display: none;">
            <div class="col-12">
                <h3 class="text-center mb-4">当前图谱统计</h3>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-cube fa-2x mb-2"></i>
                        <h4 id="total-entities">0</h4>
                        <p class="mb-0">实体总数</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-link fa-2x mb-2"></i>
                        <h4 id="total-relations">0</h4>
                        <p class="mb-0">关系总数</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-tags fa-2x mb-2"></i>
                        <h4 id="entity-types">0</h4>
                        <p class="mb-0">实体类型</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-project-diagram fa-2x mb-2"></i>
                        <h4 id="relation-types">0</h4>
                        <p class="mb-0">关系类型</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row">
            <div class="col-12">
                <h3 class="text-center mb-4">快速操作</h3>
            </div>
            
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-upload text-primary me-2"></i>
                            上传文档
                        </h5>
                        <p class="card-text">
                            上传您的文档文件，系统将自动解析并构建知识图谱。
                        </p>
                        <a href="/upload" class="btn btn-primary">
                            <i class="fas fa-arrow-right me-1"></i>开始上传
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-search text-success me-2"></i>
                            搜索探索
                        </h5>
                        <p class="card-text">
                            在已构建的知识图谱中搜索实体和关系，探索知识网络。
                        </p>
                        <a href="/search" class="btn btn-success">
                            <i class="fas fa-arrow-right me-1"></i>开始搜索
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Load graph statistics
    function loadGraphStats() {
        fetch('/api/graph/stats')
            .then(response => {
                if (response.ok) {
                    return response.json();
                }
                throw new Error('No graph available');
            })
            .then(data => {
                // Show statistics section
                document.getElementById('stats-section').style.display = 'block';
                
                // Update statistics
                document.getElementById('total-entities').textContent = data.total_entities;
                document.getElementById('total-relations').textContent = data.total_relations;
                document.getElementById('entity-types').textContent = Object.keys(data.entity_types).length;
                document.getElementById('relation-types').textContent = Object.keys(data.relation_types).length;
            })
            .catch(error => {
                // Hide statistics section if no graph
                document.getElementById('stats-section').style.display = 'none';
            });
    }

    // Load statistics on page load
    document.addEventListener('DOMContentLoaded', loadGraphStats);
    
    // Reload statistics every 10 seconds
    setInterval(loadGraphStats, 10000);
</script>
{% endblock %}
