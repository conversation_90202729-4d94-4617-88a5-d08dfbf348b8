{% extends "base.html" %}

{% block title %}Dashboard - Knowledge Graph Builder{% endblock %}

{% block page_title %}Dashboard{% endblock %}

{% block header_actions %}
<div class="btn-group" role="group">
    <a href="/upload" class="btn btn-primary">
        <i class="fas fa-upload me-1"></i>
        Upload Documents
    </a>
    <a href="/search" class="btn btn-outline-primary">
        <i class="fas fa-search me-1"></i>
        Search
    </a>
</div>
{% endblock %}

{% block content %}
<!-- Welcome Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-project-diagram text-primary me-2"></i>
                    Welcome to Knowledge Graph Builder
                </h5>
                <p class="card-text">
                    Build comprehensive knowledge graphs from your text documents using advanced LLM processing.
                    Upload documents, extract entities and relationships, and explore your data through interactive visualizations.
                </p>
                <div class="row">
                    <div class="col-md-4">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-file-alt text-info me-2"></i>
                            <span>Support for TXT, DOCX, PDF files</span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-brain text-success me-2"></i>
                            <span>AI-powered entity extraction</span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-sitemap text-warning me-2"></i>
                            <span>Interactive graph visualization</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-file-alt fa-2x text-primary mb-2"></i>
                <h5 class="card-title" id="total-documents">-</h5>
                <p class="card-text text-muted">Documents Processed</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-users fa-2x text-success mb-2"></i>
                <h5 class="card-title" id="total-entities">-</h5>
                <p class="card-text text-muted">Entities Extracted</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-link fa-2x text-info mb-2"></i>
                <h5 class="card-title" id="total-relations">-</h5>
                <p class="card-text text-muted">Relationships Found</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-tasks fa-2x text-warning mb-2"></i>
                <h5 class="card-title" id="active-jobs">-</h5>
                <p class="card-text text-muted">Active Jobs</p>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-rocket me-2"></i>
                    Quick Start
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="/upload" class="btn btn-primary">
                        <i class="fas fa-upload me-2"></i>
                        Upload New Documents
                    </a>
                    <a href="/search" class="btn btn-outline-primary">
                        <i class="fas fa-search me-2"></i>
                        Search Knowledge Graph
                    </a>
                    <a href="/graph" class="btn btn-outline-secondary">
                        <i class="fas fa-sitemap me-2"></i>
                        Visualize Graph
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    Recent Activity
                </h6>
            </div>
            <div class="card-body">
                <div id="recent-activity">
                    <div class="text-center text-muted">
                        <i class="fas fa-spinner fa-spin me-2"></i>
                        Loading recent activity...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Processing Jobs -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-tasks me-2"></i>
                    Recent Processing Jobs
                </h6>
                <a href="/jobs" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                <div id="recent-jobs">
                    <div class="text-center text-muted">
                        <i class="fas fa-spinner fa-spin me-2"></i>
                        Loading recent jobs...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Status -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-server me-2"></i>
                    System Status
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <div id="api-status" class="status-indicator mb-2">
                                <i class="fas fa-circle text-success"></i>
                            </div>
                            <small class="text-muted">API Service</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div id="search-status" class="status-indicator mb-2">
                                <i class="fas fa-circle text-warning"></i>
                            </div>
                            <small class="text-muted">Search Index</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    System Information
                </h6>
            </div>
            <div class="card-body">
                <small class="text-muted">
                    <div><strong>Version:</strong> 0.1.0</div>
                    <div><strong>Framework:</strong> PocketFlow</div>
                    <div><strong>LLM Models:</strong> qwen2.5-32b-instruct-int4, doubao-seed-1.6</div>
                    <div><strong>Supported Formats:</strong> TXT, DOCX, PDF, CHAT</div>
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load dashboard data
    loadDashboardData();
    
    // Refresh data every 30 seconds
    setInterval(loadDashboardData, 30000);
});

async function loadDashboardData() {
    try {
        // Load statistics (placeholder - implement actual API endpoints)
        await loadStatistics();
        await loadRecentActivity();
        await loadRecentJobs();
        await checkSystemStatus();
    } catch (error) {
        console.error('Failed to load dashboard data:', error);
    }
}

async function loadStatistics() {
    // Placeholder implementation
    // In a real implementation, these would come from API endpoints
    document.getElementById('total-documents').textContent = '0';
    document.getElementById('total-entities').textContent = '0';
    document.getElementById('total-relations').textContent = '0';
    document.getElementById('active-jobs').textContent = '0';
}

async function loadRecentActivity() {
    const container = document.getElementById('recent-activity');
    
    // Placeholder implementation
    container.innerHTML = `
        <div class="text-center text-muted">
            <i class="fas fa-inbox me-2"></i>
            No recent activity
        </div>
    `;
}

async function loadRecentJobs() {
    const container = document.getElementById('recent-jobs');
    
    // Placeholder implementation
    container.innerHTML = `
        <div class="text-center text-muted">
            <i class="fas fa-inbox me-2"></i>
            No recent jobs
        </div>
    `;
}

async function checkSystemStatus() {
    try {
        // Check API status
        const response = await fetch('/health');
        const apiStatus = document.getElementById('api-status');
        
        if (response.ok) {
            apiStatus.innerHTML = '<i class="fas fa-circle text-success"></i>';
        } else {
            apiStatus.innerHTML = '<i class="fas fa-circle text-danger"></i>';
        }
    } catch (error) {
        const apiStatus = document.getElementById('api-status');
        apiStatus.innerHTML = '<i class="fas fa-circle text-danger"></i>';
    }
    
    // Check search status (placeholder)
    const searchStatus = document.getElementById('search-status');
    searchStatus.innerHTML = '<i class="fas fa-circle text-warning"></i>';
}
</script>
{% endblock %}
