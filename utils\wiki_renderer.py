"""
Wiki renderer for generating HTML documentation from knowledge graph
"""

import os
import json
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
import networkx as nx
from jinja2 import Environment, FileSystemLoader, Template
from loguru import logger


class WikiRenderer:
    """HTML Wiki renderer for knowledge graphs"""
    
    def __init__(self, template_dir: str = "web/templates", 
                 static_dir: str = "web/static",
                 output_dir: str = "output/wiki"):
        self.template_dir = Path(template_dir)
        self.static_dir = Path(static_dir)
        self.output_dir = Path(output_dir)
        
        # Create directories if they don't exist
        self.template_dir.mkdir(parents=True, exist_ok=True)
        self.static_dir.mkdir(parents=True, exist_ok=True)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize Jinja2 environment
        self.env = Environment(
            loader=FileSystemLoader(str(self.template_dir)),
            autoescape=True
        )
        
        # Add custom filters
        self.env.filters['format_tags'] = self._format_tags
        self.env.filters['truncate_text'] = self._truncate_text
        self.env.filters['entity_url'] = self._entity_url
        self.env.filters['relation_color'] = self._relation_color
        
        # Create default templates if they don't exist
        self._create_default_templates()
        self._create_default_static_files()
    
    def _create_default_templates(self):
        """Create default HTML templates"""
        
        # Base template
        base_template = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}知识图谱{% endblock %}</title>
    <link rel="stylesheet" href="static/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <h1><a href="index.html">知识图谱</a></h1>
            <div class="nav-links">
                <a href="index.html">首页</a>
                <a href="entities.html">实体</a>
                <a href="relations.html">关系</a>
                <a href="graph.html">图谱</a>
                <a href="search.html">搜索</a>
            </div>
        </div>
    </nav>
    
    <main class="main-content">
        {% block content %}{% endblock %}
    </main>
    
    <footer class="footer">
        <p>Generated on {{ generation_time }} by Knowledge Graph Builder</p>
    </footer>
    
    <script src="static/script.js"></script>
    {% block scripts %}{% endblock %}
</body>
</html>"""
        
        # Index template
        index_template = """{% extends "base.html" %}

{% block title %}知识图谱 - 首页{% endblock %}

{% block content %}
<div class="hero">
    <h1>知识图谱</h1>
    <p>探索实体之间的关系和连接</p>
</div>

<div class="stats-grid">
    <div class="stat-card">
        <i class="fas fa-cube"></i>
        <h3>{{ stats.total_entities }}</h3>
        <p>实体总数</p>
    </div>
    <div class="stat-card">
        <i class="fas fa-link"></i>
        <h3>{{ stats.total_relations }}</h3>
        <p>关系总数</p>
    </div>
    <div class="stat-card">
        <i class="fas fa-tags"></i>
        <h3>{{ stats.entity_types|length }}</h3>
        <p>实体类型</p>
    </div>
    <div class="stat-card">
        <i class="fas fa-file"></i>
        <h3>{{ stats.source_files }}</h3>
        <p>源文件</p>
    </div>
</div>

<div class="content-grid">
    <section class="recent-entities">
        <h2>最新实体</h2>
        <div class="entity-list">
            {% for entity in recent_entities %}
            <div class="entity-card">
                <h3><a href="{{ entity.name|entity_url }}">{{ entity.name }}</a></h3>
                <span class="entity-type">{{ entity.type }}</span>
                <p>{{ entity.description|truncate_text(100) }}</p>
                <div class="entity-tags">
                    {{ entity.tags|format_tags }}
                </div>
            </div>
            {% endfor %}
        </div>
    </section>
    
    <section class="entity-types">
        <h2>实体类型分布</h2>
        <div class="type-list">
            {% for type_name, count in stats.entity_types.items() %}
            <div class="type-item">
                <span class="type-name">{{ type_name }}</span>
                <span class="type-count">{{ count }}</span>
                <div class="type-bar">
                    <div class="type-progress" style="width: {{ (count / stats.max_type_count * 100)|round }}%"></div>
                </div>
            </div>
            {% endfor %}
        </div>
    </section>
</div>
{% endblock %}"""
        
        # Entity template
        entity_template = """{% extends "base.html" %}

{% block title %}{{ entity.name }} - 实体详情{% endblock %}

{% block content %}
<div class="entity-detail">
    <header class="entity-header">
        <h1>{{ entity.name }}</h1>
        <span class="entity-type {{ entity.type|lower }}">{{ entity.type }}</span>
    </header>
    
    <div class="entity-content">
        <section class="entity-info">
            <h2>基本信息</h2>
            <div class="info-grid">
                <div class="info-item">
                    <label>名称:</label>
                    <span>{{ entity.name }}</span>
                </div>
                <div class="info-item">
                    <label>类型:</label>
                    <span>{{ entity.type }}</span>
                </div>
                {% if entity.alternatives %}
                <div class="info-item">
                    <label>别名:</label>
                    <span>{{ entity.alternatives|join(', ') }}</span>
                </div>
                {% endif %}
                <div class="info-item">
                    <label>置信度:</label>
                    <span>{{ (entity.confidence * 100)|round }}%</span>
                </div>
            </div>
            
            {% if entity.description %}
            <div class="entity-description">
                <h3>描述</h3>
                <p>{{ entity.description }}</p>
            </div>
            {% endif %}
            
            {% if entity.tags %}
            <div class="entity-tags">
                <h3>标签</h3>
                {{ entity.tags|format_tags }}
            </div>
            {% endif %}
        </section>
        
        <section class="entity-relations">
            <h2>关系</h2>
            
            {% if outgoing_relations %}
            <div class="relations-section">
                <h3>出度关系</h3>
                <div class="relation-list">
                    {% for relation in outgoing_relations %}
                    <div class="relation-item">
                        <span class="relation-type {{ relation.relation_type|relation_color }}">{{ relation.relation_type }}</span>
                        <a href="{{ relation.target_name|entity_url }}" class="target-entity">{{ relation.target_name }}</a>
                        {% if relation.description %}
                        <p class="relation-desc">{{ relation.description }}</p>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
            
            {% if incoming_relations %}
            <div class="relations-section">
                <h3>入度关系</h3>
                <div class="relation-list">
                    {% for relation in incoming_relations %}
                    <div class="relation-item">
                        <a href="{{ relation.source_name|entity_url }}" class="source-entity">{{ relation.source_name }}</a>
                        <span class="relation-type {{ relation.relation_type|relation_color }}">{{ relation.relation_type }}</span>
                        <span class="target-entity">{{ entity.name }}</span>
                        {% if relation.description %}
                        <p class="relation-desc">{{ relation.description }}</p>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </section>
    </div>
</div>
{% endblock %}"""
        
        # Save templates
        templates = {
            'base.html': base_template,
            'index.html': index_template,
            'entity.html': entity_template
        }
        
        for filename, content in templates.items():
            template_path = self.template_dir / filename
            if not template_path.exists():
                with open(template_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                logger.info(f"Created template: {template_path}")
    
    def _create_default_static_files(self):
        """Create default CSS and JS files"""
        
        # CSS
        css_content = """/* Knowledge Graph Wiki Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.navbar {
    background: #2c3e50;
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-container h1 a {
    color: white;
    text-decoration: none;
}

.nav-links a {
    color: white;
    text-decoration: none;
    margin-left: 2rem;
    transition: color 0.3s;
}

.nav-links a:hover {
    color: #3498db;
}

.main-content {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 2rem;
}

.hero {
    text-align: center;
    padding: 3rem 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    margin-bottom: 2rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.stat-card i {
    font-size: 2rem;
    color: #3498db;
    margin-bottom: 0.5rem;
}

.stat-card h3 {
    font-size: 2rem;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.entity-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.entity-type {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    text-transform: uppercase;
    background: #e9ecef;
    color: #495057;
}

.entity-tags {
    margin-top: 1rem;
}

.tag {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    margin: 0.25rem 0.25rem 0 0;
    background: #007bff;
    color: white;
    border-radius: 4px;
    font-size: 0.8rem;
}

.footer {
    background: #2c3e50;
    color: white;
    text-align: center;
    padding: 1rem;
    margin-top: 3rem;
}

/* Entity detail styles */
.entity-detail {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.entity-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.entity-content {
    padding: 2rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}

.info-item label {
    font-weight: bold;
    color: #666;
}

.relation-list {
    display: grid;
    gap: 1rem;
}

.relation-item {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 6px;
    border-left: 4px solid #007bff;
}

.relation-type {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
    margin-right: 0.5rem;
}

/* Responsive design */
@media (max-width: 768px) {
    .nav-container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav-links a {
        margin: 0 1rem;
    }
    
    .main-content {
        padding: 0 1rem;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
}"""
        
        # JavaScript
        js_content = """// Knowledge Graph Wiki JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth scrolling
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            document.querySelector(this.getAttribute('href')).scrollIntoView({
                behavior: 'smooth'
            });
        });
    });
    
    // Add search functionality
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const query = this.value.toLowerCase();
            const entities = document.querySelectorAll('.entity-card');
            
            entities.forEach(entity => {
                const text = entity.textContent.toLowerCase();
                if (text.includes(query)) {
                    entity.style.display = 'block';
                } else {
                    entity.style.display = 'none';
                }
            });
        });
    }
    
    // Add tooltips
    const tooltips = document.querySelectorAll('[data-tooltip]');
    tooltips.forEach(element => {
        element.addEventListener('mouseenter', function() {
            const tooltip = document.createElement('div');
            tooltip.className = 'tooltip';
            tooltip.textContent = this.getAttribute('data-tooltip');
            document.body.appendChild(tooltip);
            
            const rect = this.getBoundingClientRect();
            tooltip.style.left = rect.left + 'px';
            tooltip.style.top = (rect.top - tooltip.offsetHeight - 5) + 'px';
        });
        
        element.addEventListener('mouseleave', function() {
            const tooltip = document.querySelector('.tooltip');
            if (tooltip) {
                tooltip.remove();
            }
        });
    });
});"""
        
        # Save static files
        css_path = self.static_dir / 'style.css'
        js_path = self.static_dir / 'script.js'
        
        if not css_path.exists():
            with open(css_path, 'w', encoding='utf-8') as f:
                f.write(css_content)
            logger.info(f"Created CSS file: {css_path}")
        
        if not js_path.exists():
            with open(js_path, 'w', encoding='utf-8') as f:
                f.write(js_content)
            logger.info(f"Created JS file: {js_path}")
    
    def _format_tags(self, tags: List[str]) -> str:
        """Format tags as HTML"""
        if not tags:
            return ""
        
        tag_html = []
        for tag in tags:
            tag_html.append(f'<span class="tag">{tag}</span>')
        
        return ' '.join(tag_html)
    
    def _truncate_text(self, text: str, length: int = 100) -> str:
        """Truncate text to specified length"""
        if len(text) <= length:
            return text
        return text[:length] + "..."
    
    def _entity_url(self, entity_name: str) -> str:
        """Generate URL for entity"""
        # Simple URL generation (can be improved)
        safe_name = entity_name.replace(' ', '_').replace('/', '_')
        return f"entity_{safe_name}.html"
    
    def _relation_color(self, relation_type: str) -> str:
        """Get color class for relation type"""
        color_map = {
            '属于': 'blue',
            '位于': 'green',
            '工作于': 'orange',
            '创建': 'purple',
            '拥有': 'red',
            '关联': 'gray'
        }
        return color_map.get(relation_type, 'gray')

    def generate_index_page(self, graph: nx.DiGraph, stats: Dict[str, Any]) -> str:
        """Generate index page HTML"""

        template = self.env.get_template('index.html')

        # Get recent entities (top 10 by confidence)
        entities_data = []
        for node_id, node_data in graph.nodes(data=True):
            entities_data.append(node_data)

        # Sort by confidence and take top 10
        recent_entities = sorted(entities_data, key=lambda x: x.get('confidence', 0), reverse=True)[:10]

        return template.render(
            stats=stats,
            recent_entities=recent_entities,
            generation_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )

    def generate_entity_page(self, entity_id: str, graph: nx.DiGraph) -> str:
        """Generate entity detail page HTML"""

        if entity_id not in graph.nodes:
            raise ValueError(f"Entity {entity_id} not found in graph")

        template = self.env.get_template('entity.html')
        entity_data = graph.nodes[entity_id]

        # Get outgoing relations
        outgoing_relations = []
        for _, target, edge_data in graph.out_edges(entity_id, data=True):
            target_data = graph.nodes[target]
            outgoing_relations.append({
                'target_name': target_data.get('name', target),
                'relation_type': edge_data.get('relation_type', ''),
                'description': edge_data.get('description', ''),
                'confidence': edge_data.get('confidence', 0)
            })

        # Get incoming relations
        incoming_relations = []
        for source, _, edge_data in graph.in_edges(entity_id, data=True):
            source_data = graph.nodes[source]
            incoming_relations.append({
                'source_name': source_data.get('name', source),
                'relation_type': edge_data.get('relation_type', ''),
                'description': edge_data.get('description', ''),
                'confidence': edge_data.get('confidence', 0)
            })

        return template.render(
            entity=entity_data,
            outgoing_relations=outgoing_relations,
            incoming_relations=incoming_relations,
            generation_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )

    def generate_entities_list_page(self, graph: nx.DiGraph) -> str:
        """Generate entities list page HTML"""

        # Create a simple template for entities list
        entities_template = """{% extends "base.html" %}

{% block title %}实体列表{% endblock %}

{% block content %}
<div class="page-header">
    <h1>实体列表</h1>
    <p>共 {{ entities|length }} 个实体</p>
</div>

<div class="search-box">
    <input type="text" id="search-input" placeholder="搜索实体..." class="search-input">
</div>

<div class="entities-grid">
    {% for entity in entities %}
    <div class="entity-card">
        <h3><a href="{{ entity.name|entity_url }}">{{ entity.name }}</a></h3>
        <span class="entity-type {{ entity.type|lower }}">{{ entity.type }}</span>
        <p>{{ entity.description|truncate_text(150) }}</p>
        {% if entity.tags %}
        <div class="entity-tags">
            {{ entity.tags|format_tags }}
        </div>
        {% endif %}
    </div>
    {% endfor %}
</div>
{% endblock %}"""

        template = self.env.from_string(entities_template)

        # Get all entities
        entities = []
        for node_id, node_data in graph.nodes(data=True):
            entities.append(node_data)

        # Sort by name
        entities.sort(key=lambda x: x.get('name', ''))

        return template.render(
            entities=entities,
            generation_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )

    def calculate_graph_statistics(self, graph: nx.DiGraph) -> Dict[str, Any]:
        """Calculate graph statistics"""

        # Entity type distribution
        entity_types = {}
        for node_id, node_data in graph.nodes(data=True):
            entity_type = node_data.get('type', 'unknown')
            entity_types[entity_type] = entity_types.get(entity_type, 0) + 1

        # Relation type distribution
        relation_types = {}
        for source, target, edge_data in graph.edges(data=True):
            relation_type = edge_data.get('relation_type', 'unknown')
            relation_types[relation_type] = relation_types.get(relation_type, 0) + 1

        stats = {
            'total_entities': graph.number_of_nodes(),
            'total_relations': graph.number_of_edges(),
            'entity_types': entity_types,
            'relation_types': relation_types,
            'max_type_count': max(entity_types.values()) if entity_types else 0,
            'source_files': 1,  # This should be passed from the main process
            'density': nx.density(graph),
            'is_connected': nx.is_weakly_connected(graph) if graph.number_of_nodes() > 0 else False
        }

        return stats

    def generate_wiki_site(self, graph: nx.DiGraph, output_dir: Optional[Path] = None) -> Dict[str, str]:
        """Generate complete wiki site"""

        if output_dir is None:
            output_dir = self.output_dir
        else:
            output_dir = Path(output_dir)

        output_dir.mkdir(parents=True, exist_ok=True)

        # Copy static files
        import shutil
        static_output_dir = output_dir / 'static'
        if self.static_dir.exists():
            shutil.copytree(self.static_dir, static_output_dir, dirs_exist_ok=True)

        generated_files = {}

        # Calculate statistics
        stats = self.calculate_graph_statistics(graph)

        # Generate index page
        index_html = self.generate_index_page(graph, stats)
        index_path = output_dir / 'index.html'
        with open(index_path, 'w', encoding='utf-8') as f:
            f.write(index_html)
        generated_files['index'] = str(index_path)

        # Generate entities list page
        entities_html = self.generate_entities_list_page(graph)
        entities_path = output_dir / 'entities.html'
        with open(entities_path, 'w', encoding='utf-8') as f:
            f.write(entities_html)
        generated_files['entities'] = str(entities_path)

        # Generate individual entity pages
        entity_files = {}
        for node_id, node_data in graph.nodes(data=True):
            try:
                entity_html = self.generate_entity_page(node_id, graph)
                entity_filename = self._entity_url(node_data.get('name', node_id))
                entity_path = output_dir / entity_filename

                with open(entity_path, 'w', encoding='utf-8') as f:
                    f.write(entity_html)

                entity_files[node_id] = str(entity_path)

            except Exception as e:
                logger.error(f"Failed to generate page for entity {node_id}: {e}")

        generated_files['entity_pages'] = entity_files

        # Generate graph visualization page (placeholder)
        graph_html = self._generate_graph_page(graph)
        graph_path = output_dir / 'graph.html'
        with open(graph_path, 'w', encoding='utf-8') as f:
            f.write(graph_html)
        generated_files['graph'] = str(graph_path)

        logger.info(f"Generated wiki site with {len(entity_files)} entity pages at {output_dir}")

        return generated_files

    def _generate_graph_page(self, graph: nx.DiGraph) -> str:
        """Generate graph visualization page"""

        graph_template = """{% extends "base.html" %}

{% block title %}图谱可视化{% endblock %}

{% block content %}
<div class="page-header">
    <h1>知识图谱可视化</h1>
    <p>交互式图谱展示</p>
</div>

<div id="graph-container" style="width: 100%; height: 600px; border: 1px solid #ddd;">
    <p>图谱可视化功能正在开发中...</p>
    <p>节点数: {{ nodes_count }}</p>
    <p>边数: {{ edges_count }}</p>
</div>

<script>
// Graph data
const graphData = {{ graph_json|safe }};

// TODO: Implement graph visualization using D3.js or vis.js
console.log('Graph data:', graphData);
</script>
{% endblock %}"""

        template = self.env.from_string(graph_template)

        # Convert graph to JSON format
        graph_json = self._graph_to_json(graph)

        return template.render(
            nodes_count=graph.number_of_nodes(),
            edges_count=graph.number_of_edges(),
            graph_json=json.dumps(graph_json),
            generation_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )

    def _graph_to_json(self, graph: nx.DiGraph) -> Dict[str, Any]:
        """Convert NetworkX graph to JSON format"""

        nodes = []
        for node_id, node_data in graph.nodes(data=True):
            nodes.append({
                'id': node_id,
                'name': node_data.get('name', node_id),
                'type': node_data.get('type', 'unknown'),
                'description': node_data.get('description', ''),
                'tags': node_data.get('tags', [])
            })

        edges = []
        for source, target, edge_data in graph.edges(data=True):
            edges.append({
                'source': source,
                'target': target,
                'relation_type': edge_data.get('relation_type', ''),
                'description': edge_data.get('description', ''),
                'weight': edge_data.get('weight', 1)
            })

        return {
            'nodes': nodes,
            'edges': edges
        }
