"""Wiki renderer for generating HTML documentation from knowledge graphs."""

import os
import json
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

import networkx as nx
from jinja2 import Environment, FileSystemLoader, Template
from loguru import logger

from .graph_ops import Entity, Relation


class WikiRenderer:
    """Renderer for creating static HTML wiki pages from knowledge graphs."""
    
    def __init__(self, template_dir: Optional[str] = None):
        """
        Initialize wiki renderer.
        
        Args:
            template_dir: Directory containing Jinja2 templates
        """
        if template_dir and Path(template_dir).exists():
            self.env = Environment(loader=FileSystemLoader(template_dir))
        else:
            # Use built-in templates
            self.env = Environment(loader=FileSystemLoader(self._get_builtin_template_dir()))
        
        # Add custom filters
        self.env.filters['format_tags'] = self._format_tags
        self.env.filters['truncate_description'] = self._truncate_description
        self.env.filters['entity_type_class'] = self._entity_type_class
    
    def _get_builtin_template_dir(self) -> str:
        """Get path to built-in templates."""
        current_dir = Path(__file__).parent
        template_dir = current_dir / "templates"
        template_dir.mkdir(exist_ok=True)
        
        # Create default templates if they don't exist
        self._create_default_templates(template_dir)
        
        return str(template_dir)
    
    def _create_default_templates(self, template_dir: Path):
        """Create default HTML templates."""
        
        # Base template
        base_template = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Knowledge Graph Wiki{% endblock %}</title>
    <style>
        {% include 'styles.css' %}
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <h1><a href="index.html">Knowledge Graph Wiki</a></h1>
            <div class="nav-links">
                <a href="index.html">Home</a>
                <a href="entities.html">All Entities</a>
                <a href="graph.html">Graph View</a>
            </div>
        </div>
    </nav>
    
    <main class="main-content">
        {% block content %}{% endblock %}
    </main>
    
    <footer class="footer">
        <p>Generated on {{ generation_time }} by Knowledge Graph Builder</p>
    </footer>
    
    <script>
        {% include 'scripts.js' %}
    </script>
</body>
</html>'''
        
        # Entity template
        entity_template = '''{% extends "base.html" %}

{% block title %}{{ entity.canonical_name }} - Knowledge Graph Wiki{% endblock %}

{% block content %}
<div class="entity-page">
    <div class="entity-header">
        <h1>{{ entity.canonical_name }}</h1>
        <span class="entity-type {{ entity.entity_type | entity_type_class }}">
            {{ entity.entity_type }}
        </span>
    </div>
    
    {% if entity.description %}
    <div class="entity-description">
        <h2>Description</h2>
        <p>{{ entity.description }}</p>
    </div>
    {% endif %}
    
    {% if entity.alternatives %}
    <div class="entity-alternatives">
        <h3>Alternative Names</h3>
        <ul>
        {% for alt in entity.alternatives %}
            <li>{{ alt }}</li>
        {% endfor %}
        </ul>
    </div>
    {% endif %}
    
    {% if entity.tags %}
    <div class="entity-tags">
        <h3>Tags</h3>
        <div class="tags">
            {{ entity.tags | format_tags }}
        </div>
    </div>
    {% endif %}
    
    {% if relationships %}
    <div class="entity-relationships">
        <h2>Relationships</h2>
        <div class="relationships-grid">
            {% for rel in relationships %}
            <div class="relationship-card">
                <div class="relationship-type">{{ rel.relation_type }}</div>
                <div class="relationship-target">
                    <a href="{{ rel.target_id }}.html">{{ rel.target_name }}</a>
                </div>
                {% if rel.description %}
                <div class="relationship-description">{{ rel.description }}</div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}'''
        
        # Index template
        index_template = '''{% extends "base.html" %}

{% block content %}
<div class="index-page">
    <div class="hero">
        <h1>Knowledge Graph Wiki</h1>
        <p>Explore the knowledge graph built from your documents</p>
    </div>
    
    <div class="stats">
        <div class="stat-card">
            <h3>{{ stats.total_entities }}</h3>
            <p>Total Entities</p>
        </div>
        <div class="stat-card">
            <h3>{{ stats.total_relations }}</h3>
            <p>Total Relations</p>
        </div>
        <div class="stat-card">
            <h3>{{ stats.entity_types | length }}</h3>
            <p>Entity Types</p>
        </div>
    </div>
    
    <div class="recent-entities">
        <h2>Featured Entities</h2>
        <div class="entity-grid">
            {% for entity in featured_entities %}
            <div class="entity-card">
                <h3><a href="{{ entity.id }}.html">{{ entity.canonical_name }}</a></h3>
                <span class="entity-type {{ entity.entity_type | entity_type_class }}">
                    {{ entity.entity_type }}
                </span>
                <p>{{ entity.description | truncate_description(100) }}</p>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endblock %}'''
        
        # Save templates
        templates = {
            'base.html': base_template,
            'entity.html': entity_template,
            'index.html': index_template
        }
        
        for filename, content in templates.items():
            template_path = template_dir / filename
            if not template_path.exists():
                template_path.write_text(content, encoding='utf-8')
    
    def _format_tags(self, tags: List[str]) -> str:
        """Format tags as HTML."""
        if not tags:
            return ""
        
        tag_html = []
        for tag in tags:
            tag_html.append(f'<span class="tag">{tag}</span>')
        
        return ' '.join(tag_html)
    
    def _truncate_description(self, text: str, length: int = 150) -> str:
        """Truncate description to specified length."""
        if len(text) <= length:
            return text
        return text[:length].rsplit(' ', 1)[0] + '...'
    
    def _entity_type_class(self, entity_type: str) -> str:
        """Get CSS class for entity type."""
        type_classes = {
            'person': 'type-person',
            'organization': 'type-organization',
            'location': 'type-location',
            'concept': 'type-concept',
            'event': 'type-event',
            'product': 'type-product'
        }
        return type_classes.get(entity_type.lower(), 'type-default')
    
    def render_entity_page(
        self,
        entity: Entity,
        entity_id: str,
        relationships: List[Dict[str, Any]],
        output_path: Path
    ) -> str:
        """
        Render individual entity page.
        
        Args:
            entity: Entity to render
            entity_id: Entity ID
            relationships: List of relationships for this entity
            output_path: Output file path
            
        Returns:
            Generated HTML content
        """
        template = self.env.get_template('entity.html')
        
        html_content = template.render(
            entity=entity,
            entity_id=entity_id,
            relationships=relationships,
            generation_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )
        
        # Write to file
        output_path.write_text(html_content, encoding='utf-8')
        logger.debug(f"Generated entity page: {output_path}")
        
        return html_content

    def render_index_page(
        self,
        graph: nx.MultiDiGraph,
        output_path: Path,
        featured_count: int = 10
    ) -> str:
        """
        Render index page with overview and featured entities.

        Args:
            graph: Knowledge graph
            output_path: Output file path
            featured_count: Number of featured entities to show

        Returns:
            Generated HTML content
        """
        template = self.env.get_template('index.html')

        # Calculate statistics
        total_entities = graph.number_of_nodes()
        total_relations = graph.number_of_edges()

        entity_types = set()
        featured_entities = []

        for node_id, node_data in graph.nodes(data=True):
            entity = Entity.from_dict(node_data)
            entity_types.add(entity.entity_type)

            if len(featured_entities) < featured_count:
                featured_entities.append({
                    'id': node_id,
                    'canonical_name': entity.canonical_name,
                    'entity_type': entity.entity_type,
                    'description': entity.description
                })

        stats = {
            'total_entities': total_entities,
            'total_relations': total_relations,
            'entity_types': list(entity_types)
        }

        html_content = template.render(
            stats=stats,
            featured_entities=featured_entities,
            generation_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )

        # Write to file
        output_path.write_text(html_content, encoding='utf-8')
        logger.debug(f"Generated index page: {output_path}")

        return html_content

    def render_all_entities_page(
        self,
        graph: nx.MultiDiGraph,
        output_path: Path
    ) -> str:
        """
        Render page with all entities listed.

        Args:
            graph: Knowledge graph
            output_path: Output file path

        Returns:
            Generated HTML content
        """
        # Create entities list template if it doesn't exist
        entities_template = '''{% extends "base.html" %}

{% block title %}All Entities - Knowledge Graph Wiki{% endblock %}

{% block content %}
<div class="entities-page">
    <h1>All Entities</h1>

    <div class="entity-filters">
        <label for="type-filter">Filter by type:</label>
        <select id="type-filter">
            <option value="">All Types</option>
            {% for entity_type in entity_types %}
            <option value="{{ entity_type }}">{{ entity_type }}</option>
            {% endfor %}
        </select>
    </div>

    <div class="entities-list">
        {% for entity in entities %}
        <div class="entity-item" data-type="{{ entity.entity_type }}">
            <h3><a href="{{ entity.id }}.html">{{ entity.canonical_name }}</a></h3>
            <span class="entity-type {{ entity.entity_type | entity_type_class }}">
                {{ entity.entity_type }}
            </span>
            <p>{{ entity.description | truncate_description(150) }}</p>
        </div>
        {% endfor %}
    </div>
</div>

<script>
document.getElementById('type-filter').addEventListener('change', function() {
    const selectedType = this.value;
    const entities = document.querySelectorAll('.entity-item');

    entities.forEach(entity => {
        if (selectedType === '' || entity.dataset.type === selectedType) {
            entity.style.display = 'block';
        } else {
            entity.style.display = 'none';
        }
    });
});
</script>
{% endblock %}'''

        # Save template if it doesn't exist
        template_path = Path(self._get_builtin_template_dir()) / 'entities.html'
        if not template_path.exists():
            template_path.write_text(entities_template, encoding='utf-8')

        template = self.env.get_template('entities.html')

        # Prepare entities data
        entities = []
        entity_types = set()

        for node_id, node_data in graph.nodes(data=True):
            entity = Entity.from_dict(node_data)
            entity_types.add(entity.entity_type)

            entities.append({
                'id': node_id,
                'canonical_name': entity.canonical_name,
                'entity_type': entity.entity_type,
                'description': entity.description
            })

        # Sort entities by name
        entities.sort(key=lambda x: x['canonical_name'])

        html_content = template.render(
            entities=entities,
            entity_types=sorted(entity_types),
            generation_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )

        # Write to file
        output_path.write_text(html_content, encoding='utf-8')
        logger.debug(f"Generated entities page: {output_path}")

        return html_content

    def generate_wiki_site(
        self,
        graph: nx.MultiDiGraph,
        output_dir: Path,
        include_css: bool = True,
        include_js: bool = True
    ) -> Dict[str, str]:
        """
        Generate complete wiki site from knowledge graph.

        Args:
            graph: Knowledge graph
            output_dir: Output directory
            include_css: Whether to include CSS files
            include_js: Whether to include JavaScript files

        Returns:
            Dictionary mapping page names to file paths
        """
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        generated_pages = {}

        # Generate index page
        index_path = output_dir / "index.html"
        self.render_index_page(graph, index_path)
        generated_pages["index"] = str(index_path)

        # Generate all entities page
        entities_path = output_dir / "entities.html"
        self.render_all_entities_page(graph, entities_path)
        generated_pages["entities"] = str(entities_path)

        # Generate individual entity pages
        for node_id, node_data in graph.nodes(data=True):
            entity = Entity.from_dict(node_data)

            # Get relationships for this entity
            relationships = []
            for source, target, edge_data in graph.edges(data=True):
                if source == node_id:
                    target_data = graph.nodes[target]
                    target_entity = Entity.from_dict(target_data)
                    relation = Relation.from_dict(edge_data)

                    relationships.append({
                        'relation_type': relation.relation_type,
                        'target_id': target,
                        'target_name': target_entity.canonical_name,
                        'description': relation.description
                    })

            entity_path = output_dir / f"{node_id}.html"
            self.render_entity_page(entity, node_id, relationships, entity_path)
            generated_pages[f"entity_{node_id}"] = str(entity_path)

        # Generate CSS and JS files
        if include_css:
            self._generate_css_file(output_dir)

        if include_js:
            self._generate_js_file(output_dir)

        logger.info(f"Generated wiki site with {len(generated_pages)} pages in {output_dir}")
        return generated_pages

    def _generate_css_file(self, output_dir: Path):
        """Generate CSS file for the wiki."""
        css_content = '''
/* Knowledge Graph Wiki Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.navbar {
    background: #2c3e50;
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-container h1 a {
    color: white;
    text-decoration: none;
}

.nav-links a {
    color: white;
    text-decoration: none;
    margin-left: 2rem;
    transition: color 0.3s;
}

.nav-links a:hover {
    color: #3498db;
}

.main-content {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 2rem;
    min-height: calc(100vh - 200px);
}

.entity-card, .stat-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s;
}

.entity-card:hover {
    transform: translateY(-2px);
}

.entity-type {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    margin-left: 1rem;
}

.type-person { background: #e3f2fd; color: #1976d2; }
.type-organization { background: #f3e5f5; color: #7b1fa2; }
.type-location { background: #e8f5e8; color: #388e3c; }
.type-concept { background: #fff3e0; color: #f57c00; }
.type-event { background: #fce4ec; color: #c2185b; }
.type-default { background: #f5f5f5; color: #666; }

.tags .tag {
    display: inline-block;
    background: #e9ecef;
    color: #495057;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    margin-right: 0.5rem;
    margin-bottom: 0.25rem;
}

.relationships-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.relationship-card {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 1rem;
}

.relationship-type {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.footer {
    background: #2c3e50;
    color: white;
    text-align: center;
    padding: 2rem 0;
    margin-top: 4rem;
}

.stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 2rem 0;
}

.stat-card {
    text-align: center;
}

.stat-card h3 {
    font-size: 2rem;
    color: #3498db;
    margin-bottom: 0.5rem;
}

.entity-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

@media (max-width: 768px) {
    .nav-container {
        flex-direction: column;
        gap: 1rem;
    }

    .nav-links a {
        margin: 0 1rem;
    }

    .main-content {
        padding: 0 1rem;
    }
}
'''

        css_path = output_dir / "styles.css"
        css_path.write_text(css_content, encoding='utf-8')

    def _generate_js_file(self, output_dir: Path):
        """Generate JavaScript file for the wiki."""
        js_content = '''
// Knowledge Graph Wiki Scripts
document.addEventListener('DOMContentLoaded', function() {
    // Add search functionality
    addSearchFunctionality();

    // Add smooth scrolling
    addSmoothScrolling();
});

function addSearchFunctionality() {
    // Simple search implementation
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const query = this.value.toLowerCase();
            const entities = document.querySelectorAll('.entity-card, .entity-item');

            entities.forEach(entity => {
                const text = entity.textContent.toLowerCase();
                if (text.includes(query)) {
                    entity.style.display = 'block';
                } else {
                    entity.style.display = 'none';
                }
            });
        });
    }
}

function addSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });
}
'''

        js_path = output_dir / "scripts.js"
        js_path.write_text(js_content, encoding='utf-8')
