"""Async task manager for rate-limited execution and progress tracking."""

import asyncio
import time
from typing import Dict, List, Any, Optional, Callable, Awaitable
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict

from loguru import logger


class TaskStatus(Enum):
    """Task execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class TaskResult:
    """Result of task execution."""
    task_id: str
    status: TaskStatus
    result: Any = None
    error: Optional[Exception] = None
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    retry_count: int = 0
    
    @property
    def duration(self) -> Optional[float]:
        """Get task duration in seconds."""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return None


@dataclass
class TaskProgress:
    """Progress tracking for task execution."""
    total_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    running_tasks: int = 0
    start_time: float = field(default_factory=time.time)
    
    @property
    def completion_rate(self) -> float:
        """Get completion rate as percentage."""
        if self.total_tasks == 0:
            return 0.0
        return (self.completed_tasks / self.total_tasks) * 100
    
    @property
    def estimated_time_remaining(self) -> Optional[float]:
        """Estimate remaining time in seconds."""
        if self.completed_tasks == 0:
            return None
        
        elapsed = time.time() - self.start_time
        rate = self.completed_tasks / elapsed
        remaining_tasks = self.total_tasks - self.completed_tasks
        
        if rate > 0:
            return remaining_tasks / rate
        return None


class AsyncTaskManager:
    """Manager for executing async tasks with rate limiting and progress tracking."""
    
    def __init__(
        self,
        max_concurrent: int = 5,
        rate_limit: Optional[float] = None,
        retry_attempts: int = 3,
        retry_delay: float = 1.0
    ):
        """
        Initialize task manager.
        
        Args:
            max_concurrent: Maximum number of concurrent tasks
            rate_limit: Rate limit in tasks per second (None for no limit)
            retry_attempts: Number of retry attempts for failed tasks
            retry_delay: Delay between retries in seconds
        """
        self.max_concurrent = max_concurrent
        self.rate_limit = rate_limit
        self.retry_attempts = retry_attempts
        self.retry_delay = retry_delay
        
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.rate_limiter = asyncio.Semaphore(1) if rate_limit else None
        self.last_execution_time = 0.0
        
        self.tasks: Dict[str, TaskResult] = {}
        self.progress = TaskProgress()
        self.progress_callbacks: List[Callable[[TaskProgress], None]] = []
    
    def add_progress_callback(self, callback: Callable[[TaskProgress], None]):
        """Add progress callback function."""
        self.progress_callbacks.append(callback)
    
    def _notify_progress(self):
        """Notify all progress callbacks."""
        for callback in self.progress_callbacks:
            try:
                callback(self.progress)
            except Exception as e:
                logger.error(f"Progress callback failed: {e}")
    
    async def _rate_limit_wait(self):
        """Wait for rate limiting if enabled."""
        if not self.rate_limit:
            return
        
        if self.rate_limiter:
            async with self.rate_limiter:
                current_time = time.time()
                time_since_last = current_time - self.last_execution_time
                min_interval = 1.0 / self.rate_limit
                
                if time_since_last < min_interval:
                    wait_time = min_interval - time_since_last
                    await asyncio.sleep(wait_time)
                
                self.last_execution_time = time.time()
    
    async def _execute_task_with_retry(
        self,
        task_id: str,
        coro: Awaitable[Any]
    ) -> TaskResult:
        """
        Execute task with retry logic.
        
        Args:
            task_id: Unique task identifier
            coro: Coroutine to execute
            
        Returns:
            TaskResult with execution results
        """
        task_result = TaskResult(task_id=task_id, status=TaskStatus.PENDING)
        self.tasks[task_id] = task_result
        
        for attempt in range(self.retry_attempts + 1):
            try:
                # Wait for rate limiting
                await self._rate_limit_wait()
                
                # Update status
                task_result.status = TaskStatus.RUNNING
                task_result.start_time = time.time()
                task_result.retry_count = attempt
                
                self.progress.running_tasks += 1
                self._notify_progress()
                
                # Execute task
                result = await coro
                
                # Task completed successfully
                task_result.status = TaskStatus.COMPLETED
                task_result.result = result
                task_result.end_time = time.time()
                
                self.progress.running_tasks -= 1
                self.progress.completed_tasks += 1
                self._notify_progress()
                
                logger.debug(f"Task {task_id} completed successfully (attempt {attempt + 1})")
                return task_result
                
            except asyncio.CancelledError:
                task_result.status = TaskStatus.CANCELLED
                task_result.end_time = time.time()
                
                self.progress.running_tasks -= 1
                self.progress.failed_tasks += 1
                self._notify_progress()
                
                logger.info(f"Task {task_id} was cancelled")
                raise
                
            except Exception as e:
                task_result.error = e
                task_result.end_time = time.time()
                
                self.progress.running_tasks -= 1
                
                if attempt < self.retry_attempts:
                    logger.warning(f"Task {task_id} failed (attempt {attempt + 1}), retrying: {e}")
                    await asyncio.sleep(self.retry_delay * (2 ** attempt))  # Exponential backoff
                else:
                    task_result.status = TaskStatus.FAILED
                    self.progress.failed_tasks += 1
                    self._notify_progress()
                    
                    logger.error(f"Task {task_id} failed after {attempt + 1} attempts: {e}")
                    return task_result
        
        return task_result
    
    async def execute_tasks(
        self,
        tasks: List[Callable[[], Awaitable[Any]]],
        task_ids: Optional[List[str]] = None
    ) -> List[TaskResult]:
        """
        Execute multiple tasks concurrently with rate limiting.
        
        Args:
            tasks: List of coroutine factories
            task_ids: Optional list of task IDs (auto-generated if None)
            
        Returns:
            List of TaskResult objects
        """
        if not tasks:
            return []
        
        # Generate task IDs if not provided
        if task_ids is None:
            task_ids = [f"task_{i}" for i in range(len(tasks))]
        elif len(task_ids) != len(tasks):
            raise ValueError("Number of task IDs must match number of tasks")
        
        # Initialize progress
        self.progress = TaskProgress(total_tasks=len(tasks))
        self._notify_progress()
        
        # Create semaphore-wrapped tasks
        async def wrapped_task(task_id: str, task_factory: Callable[[], Awaitable[Any]]) -> TaskResult:
            async with self.semaphore:
                coro = task_factory()
                return await self._execute_task_with_retry(task_id, coro)
        
        # Execute all tasks
        task_coroutines = [
            wrapped_task(task_id, task_factory)
            for task_id, task_factory in zip(task_ids, tasks)
        ]
        
        results = await asyncio.gather(*task_coroutines, return_exceptions=True)
        
        # Handle any exceptions that weren't caught
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                task_result = TaskResult(
                    task_id=task_ids[i],
                    status=TaskStatus.FAILED,
                    error=result
                )
                final_results.append(task_result)
            else:
                final_results.append(result)
        
        logger.info(f"Completed {len(tasks)} tasks: "
                   f"{self.progress.completed_tasks} successful, "
                   f"{self.progress.failed_tasks} failed")
        
        return final_results
    
    async def execute_batch_with_callback(
        self,
        tasks: List[Callable[[], Awaitable[Any]]],
        batch_size: int = 10,
        batch_callback: Optional[Callable[[List[TaskResult]], Awaitable[None]]] = None,
        task_ids: Optional[List[str]] = None
    ) -> List[TaskResult]:
        """
        Execute tasks in batches with optional callback after each batch.
        
        Args:
            tasks: List of coroutine factories
            batch_size: Size of each batch
            batch_callback: Optional callback to run after each batch
            task_ids: Optional list of task IDs
            
        Returns:
            List of all TaskResult objects
        """
        all_results = []
        
        # Generate task IDs if not provided
        if task_ids is None:
            task_ids = [f"task_{i}" for i in range(len(tasks))]
        
        # Process tasks in batches
        for i in range(0, len(tasks), batch_size):
            batch_tasks = tasks[i:i + batch_size]
            batch_ids = task_ids[i:i + batch_size]
            
            logger.info(f"Processing batch {i // batch_size + 1}: "
                       f"tasks {i + 1}-{min(i + batch_size, len(tasks))}")
            
            # Execute batch
            batch_results = await self.execute_tasks(batch_tasks, batch_ids)
            all_results.extend(batch_results)
            
            # Run batch callback if provided
            if batch_callback:
                try:
                    await batch_callback(batch_results)
                except Exception as e:
                    logger.error(f"Batch callback failed: {e}")
        
        return all_results
    
    def get_task_status(self, task_id: str) -> Optional[TaskResult]:
        """Get status of a specific task."""
        return self.tasks.get(task_id)
    
    def get_progress(self) -> TaskProgress:
        """Get current progress information."""
        return self.progress
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get execution statistics."""
        completed_tasks = [t for t in self.tasks.values() if t.status == TaskStatus.COMPLETED]
        failed_tasks = [t for t in self.tasks.values() if t.status == TaskStatus.FAILED]
        
        durations = [t.duration for t in completed_tasks if t.duration is not None]
        
        stats = {
            'total_tasks': len(self.tasks),
            'completed_tasks': len(completed_tasks),
            'failed_tasks': len(failed_tasks),
            'success_rate': len(completed_tasks) / len(self.tasks) if self.tasks else 0,
            'average_duration': sum(durations) / len(durations) if durations else 0,
            'total_retries': sum(t.retry_count for t in self.tasks.values()),
            'error_summary': defaultdict(int)
        }
        
        # Count error types
        for task in failed_tasks:
            if task.error:
                error_type = type(task.error).__name__
                stats['error_summary'][error_type] += 1
        
        return stats
