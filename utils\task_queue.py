"""
Async task manager with rate limiting and retry mechanism
"""

import asyncio
import time
from typing import Any, Callable, Dict, List, Optional, Union
from dataclasses import dataclass, field
from enum import Enum
from loguru import logger
import traceback


class TaskStatus(Enum):
    """Task status enumeration"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class TaskResult:
    """Task execution result"""
    task_id: str
    status: TaskStatus
    result: Any = None
    error: Optional[Exception] = None
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    retry_count: int = 0
    
    @property
    def duration(self) -> Optional[float]:
        """Get task duration in seconds"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return None


@dataclass
class Task:
    """Task definition"""
    id: str
    func: Callable
    args: tuple = field(default_factory=tuple)
    kwargs: dict = field(default_factory=dict)
    priority: int = 0
    max_retries: int = 3
    retry_delay: float = 1.0
    timeout: Optional[float] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


class AsyncTaskManager:
    """Async task manager with rate limiting and retry mechanism"""
    
    def __init__(self, max_concurrent: int = 5, rate_limit: Optional[float] = None):
        self.max_concurrent = max_concurrent
        self.rate_limit = rate_limit  # requests per second
        
        # Task management
        self.pending_tasks: List[Task] = []
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.completed_tasks: Dict[str, TaskResult] = {}
        
        # Rate limiting
        self.last_request_time = 0.0
        self.request_times: List[float] = []
        
        # Synchronization
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.shutdown_event = asyncio.Event()
        
        # Statistics
        self.stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'cancelled_tasks': 0
        }
    
    def add_task(self, task_id: str, func: Callable, *args, 
                priority: int = 0, max_retries: int = 3, 
                retry_delay: float = 1.0, timeout: Optional[float] = None,
                **kwargs) -> str:
        """Add task to queue"""
        
        task = Task(
            id=task_id,
            func=func,
            args=args,
            kwargs=kwargs,
            priority=priority,
            max_retries=max_retries,
            retry_delay=retry_delay,
            timeout=timeout
        )
        
        # Insert task based on priority (higher priority first)
        inserted = False
        for i, existing_task in enumerate(self.pending_tasks):
            if task.priority > existing_task.priority:
                self.pending_tasks.insert(i, task)
                inserted = True
                break
        
        if not inserted:
            self.pending_tasks.append(task)
        
        self.stats['total_tasks'] += 1
        logger.debug(f"Added task {task_id} with priority {priority}")
        
        return task_id
    
    def add_batch_tasks(self, tasks: List[Dict[str, Any]]) -> List[str]:
        """Add multiple tasks at once"""
        task_ids = []
        
        for task_data in tasks:
            task_id = self.add_task(**task_data)
            task_ids.append(task_id)
        
        return task_ids
    
    async def _rate_limit_check(self):
        """Check and enforce rate limiting"""
        if not self.rate_limit:
            return
        
        now = time.time()
        
        # Remove old request times (older than 1 second)
        self.request_times = [t for t in self.request_times if now - t < 1.0]
        
        # Check if we need to wait
        if len(self.request_times) >= self.rate_limit:
            sleep_time = 1.0 - (now - self.request_times[0])
            if sleep_time > 0:
                logger.debug(f"Rate limiting: sleeping for {sleep_time:.2f} seconds")
                await asyncio.sleep(sleep_time)
        
        self.request_times.append(now)
    
    async def _execute_task(self, task: Task) -> TaskResult:
        """Execute a single task with retry logic"""
        
        result = TaskResult(
            task_id=task.id,
            status=TaskStatus.RUNNING,
            start_time=time.time()
        )
        
        for attempt in range(task.max_retries + 1):
            try:
                # Rate limiting
                await self._rate_limit_check()
                
                # Execute task
                if asyncio.iscoroutinefunction(task.func):
                    if task.timeout:
                        task_result = await asyncio.wait_for(
                            task.func(*task.args, **task.kwargs),
                            timeout=task.timeout
                        )
                    else:
                        task_result = await task.func(*task.args, **task.kwargs)
                else:
                    # Run sync function in thread pool
                    loop = asyncio.get_event_loop()
                    if task.timeout:
                        task_result = await asyncio.wait_for(
                            loop.run_in_executor(None, task.func, *task.args),
                            timeout=task.timeout
                        )
                    else:
                        task_result = await loop.run_in_executor(None, task.func, *task.args)
                
                # Success
                result.status = TaskStatus.COMPLETED
                result.result = task_result
                result.end_time = time.time()
                
                logger.debug(f"Task {task.id} completed successfully in {result.duration:.2f}s")
                return result
                
            except asyncio.CancelledError:
                result.status = TaskStatus.CANCELLED
                result.end_time = time.time()
                logger.info(f"Task {task.id} was cancelled")
                return result
                
            except Exception as e:
                result.retry_count = attempt
                result.error = e
                
                if attempt < task.max_retries:
                    logger.warning(f"Task {task.id} failed (attempt {attempt + 1}/{task.max_retries + 1}): {e}")
                    await asyncio.sleep(task.retry_delay * (2 ** attempt))  # Exponential backoff
                else:
                    result.status = TaskStatus.FAILED
                    result.end_time = time.time()
                    logger.error(f"Task {task.id} failed after {task.max_retries + 1} attempts: {e}")
                    logger.debug(f"Task {task.id} traceback: {traceback.format_exc()}")
                    return result
        
        return result
    
    async def _worker(self):
        """Worker coroutine that processes tasks"""
        
        while not self.shutdown_event.is_set():
            # Get next task
            if not self.pending_tasks:
                await asyncio.sleep(0.1)
                continue
            
            task = self.pending_tasks.pop(0)
            
            # Acquire semaphore
            async with self.semaphore:
                # Create and start task
                task_coroutine = self._execute_task(task)
                running_task = asyncio.create_task(task_coroutine)
                self.running_tasks[task.id] = running_task
                
                try:
                    result = await running_task
                    
                    # Store result
                    self.completed_tasks[task.id] = result
                    
                    # Update statistics
                    if result.status == TaskStatus.COMPLETED:
                        self.stats['completed_tasks'] += 1
                    elif result.status == TaskStatus.FAILED:
                        self.stats['failed_tasks'] += 1
                    elif result.status == TaskStatus.CANCELLED:
                        self.stats['cancelled_tasks'] += 1
                    
                finally:
                    # Remove from running tasks
                    self.running_tasks.pop(task.id, None)
    
    async def start(self, num_workers: Optional[int] = None):
        """Start the task manager"""
        
        if num_workers is None:
            num_workers = min(self.max_concurrent, 10)
        
        logger.info(f"Starting task manager with {num_workers} workers")
        
        # Start worker coroutines
        workers = [asyncio.create_task(self._worker()) for _ in range(num_workers)]
        
        try:
            await asyncio.gather(*workers)
        except asyncio.CancelledError:
            logger.info("Task manager workers cancelled")
        finally:
            # Cancel any remaining running tasks
            for task in self.running_tasks.values():
                task.cancel()
            
            if self.running_tasks:
                await asyncio.gather(*self.running_tasks.values(), return_exceptions=True)
    
    async def stop(self):
        """Stop the task manager"""
        logger.info("Stopping task manager")
        self.shutdown_event.set()
    
    async def wait_for_completion(self, timeout: Optional[float] = None):
        """Wait for all tasks to complete"""
        
        start_time = time.time()
        
        while (self.pending_tasks or self.running_tasks):
            if timeout and (time.time() - start_time) > timeout:
                logger.warning("Timeout waiting for task completion")
                break
            
            await asyncio.sleep(0.1)
        
        logger.info("All tasks completed")
    
    def get_task_result(self, task_id: str) -> Optional[TaskResult]:
        """Get result for a specific task"""
        return self.completed_tasks.get(task_id)
    
    def get_task_status(self, task_id: str) -> TaskStatus:
        """Get status for a specific task"""
        if task_id in self.completed_tasks:
            return self.completed_tasks[task_id].status
        elif task_id in self.running_tasks:
            return TaskStatus.RUNNING
        else:
            # Check if in pending
            for task in self.pending_tasks:
                if task.id == task_id:
                    return TaskStatus.PENDING
            return TaskStatus.FAILED  # Not found
    
    def cancel_task(self, task_id: str) -> bool:
        """Cancel a specific task"""
        
        # Cancel if running
        if task_id in self.running_tasks:
            self.running_tasks[task_id].cancel()
            return True
        
        # Remove if pending
        for i, task in enumerate(self.pending_tasks):
            if task.id == task_id:
                self.pending_tasks.pop(i)
                return True
        
        return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get task manager statistics"""
        return {
            **self.stats,
            'pending_tasks': len(self.pending_tasks),
            'running_tasks': len(self.running_tasks),
            'completed_tasks_stored': len(self.completed_tasks)
        }
    
    def clear_completed_tasks(self):
        """Clear completed task results to free memory"""
        self.completed_tasks.clear()
        logger.debug("Cleared completed task results")
