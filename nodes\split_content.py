"""
Content splitting node
Handles semantic-aware text segmentation for large documents
"""

from typing import Dict, Any, List, Optional
from loguru import logger

from .base_node import BaseNode
from utils.file_utils import ContentSplitter


class SplitContentNode(BaseNode):
    """Node for splitting long content into semantic chunks"""
    
    def __init__(self, node_id: Optional[str] = None,
                 chunk_size: int = 2000,
                 chunk_overlap: int = 200,
                 min_chunk_size: int = 100):
        super().__init__(node_id=node_id or "SplitContent")
        
        self.content_splitter = ContentSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap
        )
        self.min_chunk_size = min_chunk_size
    
    def prep(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare content for splitting"""
        
        content = shared.get('parsed_content', '')
        metadata = shared.get('content_metadata', {})
        
        if not content:
            raise ValueError("No content to split")
        
        return {
            'content': content,
            'metadata': metadata,
            'splitter': self.content_splitter
        }
    
    def exec(self, prep_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Split content into semantic chunks"""
        
        content = prep_result['content']
        metadata = prep_result['metadata']
        splitter = prep_result['splitter']
        
        logger.debug(f"Splitting content: {len(content)} characters")
        
        # Check if content needs splitting
        if len(content) <= splitter.chunk_size:
            logger.debug("Content is small, no splitting needed")
            return [{
                'text': content,
                'start_pos': 0,
                'end_pos': len(content),
                'chunk_id': 0,
                'metadata': metadata
            }]
        
        # Split content
        segments = splitter.split_by_semantic_boundaries(content)
        
        # Filter out very small segments
        filtered_segments = []
        for segment in segments:
            if len(segment['text'].strip()) >= self.min_chunk_size:
                # Add metadata to each segment
                segment['metadata'] = metadata.copy()
                segment['metadata']['is_segment'] = True
                segment['metadata']['total_segments'] = len(segments)
                filtered_segments.append(segment)
            else:
                logger.debug(f"Filtering out small segment: {len(segment['text'])} chars")
        
        logger.info(f"Split content into {len(filtered_segments)} segments")
        
        return filtered_segments
    
    def post(self, shared: Dict[str, Any], prep_result: Dict[str, Any], 
             exec_result: List[Dict[str, Any]]) -> str:
        """Store segments in shared context"""
        
        # Store segments
        shared['content_segments'] = exec_result
        
        # Update metadata
        shared['segmentation_stats'] = {
            'total_segments': len(exec_result),
            'average_segment_length': sum(len(seg['text']) for seg in exec_result) / len(exec_result) if exec_result else 0,
            'original_length': len(prep_result['content'])
        }
        
        logger.debug(f"Stored {len(exec_result)} content segments")
        
        # Determine next step
        if len(exec_result) > 1:
            return "process_segments_parallel"
        else:
            return "process_segments_single"


class SplitByParagraphsNode(BaseNode):
    """Node for splitting content by paragraphs"""
    
    def __init__(self, node_id: Optional[str] = None,
                 min_paragraph_length: int = 50):
        super().__init__(node_id=node_id or "SplitByParagraphs")
        self.min_paragraph_length = min_paragraph_length
    
    def prep(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare content for paragraph splitting"""
        
        content = shared.get('parsed_content', '')
        metadata = shared.get('content_metadata', {})
        
        return {
            'content': content,
            'metadata': metadata
        }
    
    def exec(self, prep_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Split content by paragraphs"""
        
        content = prep_result['content']
        metadata = prep_result['metadata']
        
        # Split by double newlines (paragraphs)
        paragraphs = content.split('\n\n')
        
        segments = []
        current_pos = 0
        
        for i, paragraph in enumerate(paragraphs):
            paragraph = paragraph.strip()
            
            if len(paragraph) >= self.min_paragraph_length:
                segment = {
                    'text': paragraph,
                    'start_pos': current_pos,
                    'end_pos': current_pos + len(paragraph),
                    'chunk_id': i,
                    'metadata': metadata.copy()
                }
                segment['metadata']['split_method'] = 'paragraph'
                segments.append(segment)
            
            current_pos += len(paragraph) + 2  # +2 for \n\n
        
        logger.info(f"Split content into {len(segments)} paragraphs")
        
        return segments
    
    def post(self, shared: Dict[str, Any], prep_result: Dict[str, Any], 
             exec_result: List[Dict[str, Any]]) -> str:
        """Store paragraph segments"""
        
        shared['content_segments'] = exec_result
        return "default"


class SplitBySentencesNode(BaseNode):
    """Node for splitting content by sentences"""
    
    def __init__(self, node_id: Optional[str] = None,
                 sentences_per_chunk: int = 5,
                 min_sentence_length: int = 20):
        super().__init__(node_id=node_id or "SplitBySentences")
        self.sentences_per_chunk = sentences_per_chunk
        self.min_sentence_length = min_sentence_length
    
    def prep(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare content for sentence splitting"""
        
        content = shared.get('parsed_content', '')
        metadata = shared.get('content_metadata', {})
        
        return {
            'content': content,
            'metadata': metadata
        }
    
    def exec(self, prep_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Split content by sentences"""
        
        content = prep_result['content']
        metadata = prep_result['metadata']
        
        # Simple sentence splitting (can be improved with NLTK)
        import re
        
        # Split by sentence endings
        sentence_endings = r'[.!?。！？]+'
        sentences = re.split(sentence_endings, content)
        
        # Filter and clean sentences
        clean_sentences = []
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) >= self.min_sentence_length:
                clean_sentences.append(sentence)
        
        # Group sentences into chunks
        segments = []
        current_pos = 0
        
        for i in range(0, len(clean_sentences), self.sentences_per_chunk):
            chunk_sentences = clean_sentences[i:i + self.sentences_per_chunk]
            chunk_text = '. '.join(chunk_sentences) + '.'
            
            segment = {
                'text': chunk_text,
                'start_pos': current_pos,
                'end_pos': current_pos + len(chunk_text),
                'chunk_id': i // self.sentences_per_chunk,
                'metadata': metadata.copy()
            }
            segment['metadata']['split_method'] = 'sentence'
            segment['metadata']['sentences_count'] = len(chunk_sentences)
            segments.append(segment)
            
            current_pos += len(chunk_text) + 1
        
        logger.info(f"Split content into {len(segments)} sentence chunks")
        
        return segments
    
    def post(self, shared: Dict[str, Any], prep_result: Dict[str, Any], 
             exec_result: List[Dict[str, Any]]) -> str:
        """Store sentence segments"""
        
        shared['content_segments'] = exec_result
        return "default"


class AdaptiveSplitNode(BaseNode):
    """Node that chooses splitting strategy based on content characteristics"""
    
    def __init__(self, node_id: Optional[str] = None,
                 chunk_size: int = 2000,
                 chunk_overlap: int = 200):
        super().__init__(node_id=node_id or "AdaptiveSplit")
        
        self.semantic_splitter = ContentSplitter(chunk_size, chunk_overlap)
        self.paragraph_splitter = SplitByParagraphsNode()
        self.sentence_splitter = SplitBySentencesNode()
    
    def prep(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze content to choose splitting strategy"""
        
        content = shared.get('parsed_content', '')
        metadata = shared.get('content_metadata', {})
        
        if not content:
            raise ValueError("No content to split")
        
        # Analyze content characteristics
        analysis = self._analyze_content(content)
        
        return {
            'content': content,
            'metadata': metadata,
            'analysis': analysis
        }
    
    def _analyze_content(self, content: str) -> Dict[str, Any]:
        """Analyze content to determine best splitting strategy"""
        
        content_length = len(content)
        
        # Count structural elements
        paragraph_count = content.count('\n\n')
        sentence_count = len([s for s in content.split('.') if s.strip()])
        
        # Calculate ratios
        avg_paragraph_length = content_length / max(paragraph_count, 1)
        avg_sentence_length = content_length / max(sentence_count, 1)
        
        analysis = {
            'content_length': content_length,
            'paragraph_count': paragraph_count,
            'sentence_count': sentence_count,
            'avg_paragraph_length': avg_paragraph_length,
            'avg_sentence_length': avg_sentence_length
        }
        
        # Determine strategy
        if content_length <= 2000:
            analysis['strategy'] = 'no_split'
        elif paragraph_count > 5 and avg_paragraph_length < 1000:
            analysis['strategy'] = 'paragraph'
        elif sentence_count > 10 and avg_sentence_length < 200:
            analysis['strategy'] = 'sentence'
        else:
            analysis['strategy'] = 'semantic'
        
        logger.debug(f"Content analysis: {analysis}")
        
        return analysis
    
    def exec(self, prep_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Execute adaptive splitting"""
        
        content = prep_result['content']
        metadata = prep_result['metadata']
        analysis = prep_result['analysis']
        strategy = analysis['strategy']
        
        logger.info(f"Using {strategy} splitting strategy")
        
        if strategy == 'no_split':
            return [{
                'text': content,
                'start_pos': 0,
                'end_pos': len(content),
                'chunk_id': 0,
                'metadata': metadata
            }]
        
        elif strategy == 'paragraph':
            # Use paragraph splitting
            shared_temp = {'parsed_content': content, 'content_metadata': metadata}
            prep_result_para = self.paragraph_splitter.prep(shared_temp)
            return self.paragraph_splitter.exec(prep_result_para)
        
        elif strategy == 'sentence':
            # Use sentence splitting
            shared_temp = {'parsed_content': content, 'content_metadata': metadata}
            prep_result_sent = self.sentence_splitter.prep(shared_temp)
            return self.sentence_splitter.exec(prep_result_sent)
        
        else:  # semantic
            # Use semantic splitting
            segments = self.semantic_splitter.split_by_semantic_boundaries(content)
            
            # Add metadata
            for segment in segments:
                segment['metadata'] = metadata.copy()
                segment['metadata']['split_method'] = 'semantic'
            
            return segments
    
    def post(self, shared: Dict[str, Any], prep_result: Dict[str, Any], 
             exec_result: List[Dict[str, Any]]) -> str:
        """Store adaptive segments"""
        
        shared['content_segments'] = exec_result
        shared['split_strategy'] = prep_result['analysis']['strategy']
        
        logger.info(f"Adaptive splitting completed: {len(exec_result)} segments using {prep_result['analysis']['strategy']} strategy")
        
        return "default"


def create_split_node(strategy: str = "adaptive", **kwargs) -> BaseNode:
    """Factory function to create appropriate split node"""
    
    if strategy == "semantic":
        return SplitContentNode(**kwargs)
    elif strategy == "paragraph":
        return SplitByParagraphsNode(**kwargs)
    elif strategy == "sentence":
        return SplitBySentencesNode(**kwargs)
    elif strategy == "adaptive":
        return AdaptiveSplitNode(**kwargs)
    else:
        raise ValueError(f"Unknown split strategy: {strategy}")
