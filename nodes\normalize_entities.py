"""
Entity normalization nodes
Handles entity deduplication, canonical form selection, and quality enhancement
"""

import asyncio
from typing import Dict, Any, List, Optional, Tuple
from loguru import logger

from .base_node import AsyncNode, CacheNode
from utils.graph_ops import GraphOperations, Entity, Relation
from utils.llm_client import LLMClient


class NormalizeEntitiesNode(AsyncNode):
    """Node for normalizing and deduplicating entities"""
    
    def __init__(self, node_id: Optional[str] = None,
                 similarity_threshold: float = 0.85,
                 merge_strategy: str = "weighted_priority"):
        super().__init__(
            node_id=node_id or "NormalizeEntities",
            timeout=300
        )
        
        self.graph_ops = GraphOperations(
            similarity_threshold=similarity_threshold,
            merge_strategy=merge_strategy
        )
    
    def prep(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare entities for normalization"""
        
        # Get segment results
        segment_results = shared.get('segment_results', [])
        if not segment_results:
            raise ValueError("No segment results provided for normalization")
        
        # Collect all entities and relations
        all_entities = []
        all_relations = []
        
        for result in segment_results:
            if result.get('processing_successful', False):
                all_entities.extend(result.get('entities', []))
                all_relations.extend(result.get('relations', []))
        
        # Get source weight (from file metadata)
        source_weight = shared.get('content_metadata', {}).get('source_weight', 1)
        
        return {
            'entities': all_entities,
            'relations': all_relations,
            'source_weight': source_weight,
            'graph_ops': self.graph_ops
        }
    
    async def exec_async(self, prep_result: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize entities and relations"""
        
        entities = prep_result['entities']
        relations = prep_result['relations']
        source_weight = prep_result['source_weight']
        graph_ops = prep_result['graph_ops']
        
        logger.info(f"Normalizing {len(entities)} entities and {len(relations)} relations")
        
        # Normalize entities
        normalized_entities = graph_ops.normalize_entities(entities, source_weight)
        
        # Create entity map for relation normalization
        entity_map = {entity.canonical_name: entity for entity in normalized_entities}
        
        # Also map by original names and alternatives
        for entity in normalized_entities:
            entity_map[entity.name] = entity
            for alt in entity.alternatives:
                entity_map[alt] = entity
        
        # Normalize relations
        normalized_relations = graph_ops.normalize_relations(relations, entity_map)
        
        logger.info(f"Normalization completed: {len(normalized_entities)} entities, "
                   f"{len(normalized_relations)} relations")
        
        return {
            'normalized_entities': normalized_entities,
            'normalized_relations': normalized_relations,
            'original_entity_count': len(entities),
            'original_relation_count': len(relations)
        }
    
    def post(self, shared: Dict[str, Any], prep_result: Dict[str, Any], 
             exec_result: Dict[str, Any]) -> str:
        """Store normalized entities and relations"""
        
        # Store normalized results
        shared['normalized_entities'] = exec_result['normalized_entities']
        shared['normalized_relations'] = exec_result['normalized_relations']
        
        # Store normalization statistics
        shared['normalization_stats'] = {
            'original_entities': exec_result['original_entity_count'],
            'normalized_entities': len(exec_result['normalized_entities']),
            'original_relations': exec_result['original_relation_count'],
            'normalized_relations': len(exec_result['normalized_relations']),
            'entity_reduction_ratio': 1 - (len(exec_result['normalized_entities']) / max(exec_result['original_entity_count'], 1)),
            'relation_reduction_ratio': 1 - (len(exec_result['normalized_relations']) / max(exec_result['original_relation_count'], 1))
        }
        
        logger.info(f"Entity reduction: {exec_result['original_entity_count']} -> {len(exec_result['normalized_entities'])}")
        logger.info(f"Relation reduction: {exec_result['original_relation_count']} -> {len(exec_result['normalized_relations'])}")
        
        return "default"


class EnhanceEntityDescriptionsNode(AsyncNode):
    """Node for enhancing entity descriptions using LLM"""
    
    def __init__(self, node_id: Optional[str] = None,
                 llm_settings: Optional[Dict[str, Any]] = None,
                 min_description_length: int = 50,
                 batch_size: int = 5):
        super().__init__(
            node_id=node_id or "EnhanceEntityDescriptions",
            timeout=600
        )
        
        self.llm_settings = llm_settings or {}
        self.min_description_length = min_description_length
        self.batch_size = batch_size
    
    def prep(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare entities for description enhancement"""
        
        normalized_entities = shared.get('normalized_entities', [])
        if not normalized_entities:
            raise ValueError("No normalized entities provided")
        
        # Find entities that need description enhancement
        entities_to_enhance = []
        
        for entity in normalized_entities:
            description = entity.description if hasattr(entity, 'description') else ''
            if len(description) < self.min_description_length:
                entities_to_enhance.append(entity)
        
        logger.info(f"Found {len(entities_to_enhance)} entities needing description enhancement")
        
        return {
            'entities_to_enhance': entities_to_enhance,
            'all_entities': normalized_entities,
            'llm_settings': self.llm_settings
        }
    
    async def exec_async(self, prep_result: Dict[str, Any]) -> List[Entity]:
        """Enhance entity descriptions"""
        
        entities_to_enhance = prep_result['entities_to_enhance']
        all_entities = prep_result['all_entities']
        llm_settings = prep_result['llm_settings']
        
        if not entities_to_enhance:
            logger.info("No entities need description enhancement")
            return all_entities
        
        # Create LLM client
        async with LLMClient(
            base_url=llm_settings.get('base_url', ''),
            api_key=llm_settings.get('api_key', ''),
            models=llm_settings.get('models', {}),
            max_tokens=llm_settings.get('max_tokens', 4000),
            temperature=llm_settings.get('temperature', 0.1)
        ) as client:
            
            # Process entities in batches
            enhanced_entities = all_entities.copy()
            
            for i in range(0, len(entities_to_enhance), self.batch_size):
                batch = entities_to_enhance[i:i + self.batch_size]
                
                for entity in batch:
                    try:
                        # Get context for entity
                        context = self._get_entity_context(entity, all_entities)
                        
                        # Enhance description
                        enhanced_description = await client.enhance_entity_description(
                            entity_name=entity.name,
                            entity_type=entity.type,
                            context=context
                        )
                        
                        # Update entity in the list
                        for j, e in enumerate(enhanced_entities):
                            if e.id == entity.id:
                                enhanced_entities[j].description = enhanced_description
                                break
                        
                        logger.debug(f"Enhanced description for {entity.name}")
                        
                    except Exception as e:
                        logger.warning(f"Failed to enhance description for {entity.name}: {e}")
                        continue
                
                # Small delay between batches
                await asyncio.sleep(0.5)
        
        return enhanced_entities
    
    def _get_entity_context(self, entity: Entity, all_entities: List[Entity]) -> str:
        """Get context for entity from related entities"""
        
        context_parts = []
        
        # Look for entities of similar type
        similar_entities = [e for e in all_entities if e.type == entity.type and e.id != entity.id]
        if similar_entities:
            context_parts.append(f"Similar {entity.type} entities: {', '.join([e.name for e in similar_entities[:3]])}")
        
        # Add entity tags as context
        if hasattr(entity, 'tags') and entity.tags:
            context_parts.append(f"Tags: {', '.join(entity.tags)}")
        
        # Add alternatives as context
        if hasattr(entity, 'alternatives') and entity.alternatives:
            context_parts.append(f"Also known as: {', '.join(entity.alternatives[:3])}")
        
        return "; ".join(context_parts)
    
    def post(self, shared: Dict[str, Any], prep_result: Dict[str, Any], 
             exec_result: List[Entity]) -> str:
        """Store enhanced entities"""
        
        shared['normalized_entities'] = exec_result
        
        enhanced_count = len(prep_result['entities_to_enhance'])
        logger.info(f"Enhanced descriptions for {enhanced_count} entities")
        
        return "default"


class GenerateEntityTagsNode(CacheNode):
    """Node for generating tags for entities using LLM"""
    
    def __init__(self, node_id: Optional[str] = None,
                 llm_settings: Optional[Dict[str, Any]] = None,
                 min_tags_count: int = 2,
                 batch_size: int = 5):
        super().__init__(
            node_id=node_id or "GenerateEntityTags",
            use_cache=True,
            cache_ttl=3600,  # 1 hour cache
            timeout=600
        )
        
        self.llm_settings = llm_settings or {}
        self.min_tags_count = min_tags_count
        self.batch_size = batch_size
    
    def prep(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare entities for tag generation"""
        
        normalized_entities = shared.get('normalized_entities', [])
        if not normalized_entities:
            raise ValueError("No normalized entities provided")
        
        # Find entities that need tags
        entities_to_tag = []
        
        for entity in normalized_entities:
            tags = entity.tags if hasattr(entity, 'tags') else []
            if len(tags) < self.min_tags_count:
                entities_to_tag.append(entity)
        
        logger.info(f"Found {len(entities_to_tag)} entities needing tags")
        
        return {
            'entities_to_tag': entities_to_tag,
            'all_entities': normalized_entities,
            'llm_settings': self.llm_settings
        }
    
    def get_cache_key(self, prep_result: Dict[str, Any]) -> str:
        """Generate cache key for tag generation"""
        entities_to_tag = prep_result['entities_to_tag']
        entity_names = [entity.name for entity in entities_to_tag]
        return f"tags_{hash(tuple(sorted(entity_names)))}"
    
    def exec_cached(self, prep_result: Dict[str, Any]) -> List[Entity]:
        """Generate tags for entities (cached)"""
        
        # This will be called by the async wrapper
        return asyncio.run(self._exec_async_impl(prep_result))
    
    async def _exec_async_impl(self, prep_result: Dict[str, Any]) -> List[Entity]:
        """Async implementation of tag generation"""
        
        entities_to_tag = prep_result['entities_to_tag']
        all_entities = prep_result['all_entities']
        llm_settings = prep_result['llm_settings']
        
        if not entities_to_tag:
            logger.info("No entities need tag generation")
            return all_entities
        
        # Create LLM client
        async with LLMClient(
            base_url=llm_settings.get('base_url', ''),
            api_key=llm_settings.get('api_key', ''),
            models=llm_settings.get('models', {}),
            max_tokens=llm_settings.get('max_tokens', 4000),
            temperature=llm_settings.get('temperature', 0.1)
        ) as client:
            
            # Process entities in batches
            tagged_entities = all_entities.copy()
            
            for i in range(0, len(entities_to_tag), self.batch_size):
                batch = entities_to_tag[i:i + self.batch_size]
                
                for entity in batch:
                    try:
                        # Generate tags
                        generated_tags = await client.generate_entity_tags(
                            entity_name=entity.name,
                            entity_type=entity.type,
                            description=entity.description
                        )
                        
                        # Update entity in the list
                        for j, e in enumerate(tagged_entities):
                            if e.id == entity.id:
                                # Merge with existing tags
                                existing_tags = e.tags if hasattr(e, 'tags') else []
                                all_tags = list(set(existing_tags + generated_tags))
                                tagged_entities[j].tags = all_tags
                                break
                        
                        logger.debug(f"Generated {len(generated_tags)} tags for {entity.name}")
                        
                    except Exception as e:
                        logger.warning(f"Failed to generate tags for {entity.name}: {e}")
                        continue
                
                # Small delay between batches
                await asyncio.sleep(0.5)
        
        return tagged_entities
    
    def post(self, shared: Dict[str, Any], prep_result: Dict[str, Any], 
             exec_result: List[Entity]) -> str:
        """Store entities with generated tags"""
        
        shared['normalized_entities'] = exec_result
        
        tagged_count = len(prep_result['entities_to_tag'])
        logger.info(f"Generated tags for {tagged_count} entities")
        
        return "default"


class MergeSegmentResultsNode(AsyncNode):
    """Node for merging results from multiple segments"""
    
    def __init__(self, node_id: Optional[str] = None):
        super().__init__(node_id=node_id or "MergeSegmentResults")
    
    def prep(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare segment results for merging"""
        
        segment_results = shared.get('segment_results', [])
        if not segment_results:
            raise ValueError("No segment results to merge")
        
        return {
            'segment_results': segment_results
        }
    
    async def exec_async(self, prep_result: Dict[str, Any]) -> Dict[str, Any]:
        """Merge segment results"""
        
        segment_results = prep_result['segment_results']
        
        all_entities = []
        all_relations = []
        successful_segments = 0
        failed_segments = 0
        
        for result in segment_results:
            if result.get('processing_successful', False):
                successful_segments += 1
                all_entities.extend(result.get('entities', []))
                all_relations.extend(result.get('relations', []))
            else:
                failed_segments += 1
        
        logger.info(f"Merged results: {successful_segments} successful segments, "
                   f"{failed_segments} failed segments")
        logger.info(f"Total entities: {len(all_entities)}, relations: {len(all_relations)}")
        
        return {
            'entities': all_entities,
            'relations': all_relations,
            'successful_segments': successful_segments,
            'failed_segments': failed_segments
        }
    
    def post(self, shared: Dict[str, Any], prep_result: Dict[str, Any], 
             exec_result: Dict[str, Any]) -> str:
        """Store merged results"""
        
        shared['merged_entities'] = exec_result['entities']
        shared['merged_relations'] = exec_result['relations']
        shared['merge_stats'] = {
            'successful_segments': exec_result['successful_segments'],
            'failed_segments': exec_result['failed_segments'],
            'total_entities': len(exec_result['entities']),
            'total_relations': len(exec_result['relations'])
        }
        
        return "default"
