{"mode": "merge", "documents": [{"file_path": "test_documents\\sample1.txt", "file_name": "sample1.txt", "content": "Dr. <PERSON> is a renowned researcher at Stanford University. She specializes in artificial intelligence and machine learning. Her recent work on neural networks has been published in Nature journal.\n\n<PERSON> collaborates with Prof. <PERSON> from MIT on quantum computing projects. They are working together on developing new algorithms for quantum machine learning.\n\nStanford University is located in California and is known for its cutting-edge research in technology. The university has partnerships with many tech companies including Google and Microsoft.\n\nMIT, located in Massachusetts, is another leading institution in technology research. The collaboration between Stanford and MIT has resulted in several breakthrough discoveries in the field of quantum computing.\n\nGoogle has been investing heavily in quantum computing research and has hired several researchers from both Stanford and MIT. Microsoft is also competing in this space with their quantum development kit.\n\nThe field of artificial intelligence is rapidly evolving, with new discoveries being made every day. Machine learning, a subset of AI, has applications in various industries including healthcare, finance, and autonomous vehicles.\n", "segments": [{"text": "Dr. <PERSON> is a renowned researcher at Stanford University. She specializes in artificial intelligence and machine learning. Her recent work on neural networks has been published in Nature journal.\n\n<PERSON> collaborates with Prof. <PERSON> from MIT on quantum computing projects. They are working together on developing new algorithms for quantum machine learning.\n\nStanford University is located in California and is known for its cutting-edge research in technology. The university has partnerships with many tech companies including Google and Microsoft.\n\nMIT, located in Massachusetts, is another leading institution in technology research. The collaboration between Stanford and MIT has resulted in several breakthrough discoveries in the field of quantum computing.\n\nGoogle has been investing heavily in quantum computing research and has hired several researchers from both Stanford and MIT. Microsoft is also competing in this space with their quantum development kit.\n\nThe field of artificial intelligence is rapidly evolving, with new discoveries being made every day. Machine learning, a subset of AI, has applications in various industries including healthcare, finance, and autonomous vehicles.\n", "start": 0, "end": 1214, "segment_id": 0}], "metadata": {"size": 1214, "format": ".txt"}, "processed_segments": [{"text": "Dr. <PERSON> is a renowned researcher at Stanford University. She specializes in artificial intelligence and machine learning. Her recent work on neural networks has been published in Nature journal.\n\n<PERSON> collaborates with Prof. <PERSON> from MIT on quantum computing projects. They are working together on developing new algorithms for quantum machine learning.\n\nStanford University is located in California and is known for its cutting-edge research in technology. The university has partnerships with many tech companies including Google and Microsoft.\n\nMIT, located in Massachusetts, is another leading institution in technology research. The collaboration between Stanford and MIT has resulted in several breakthrough discoveries in the field of quantum computing.\n\nGoogle has been investing heavily in quantum computing research and has hired several researchers from both Stanford and MIT. Microsoft is also competing in this space with their quantum development kit.\n\nThe field of artificial intelligence is rapidly evolving, with new discoveries being made every day. Machine learning, a subset of AI, has applications in various industries including healthcare, finance, and autonomous vehicles.\n", "start": 0, "end": 1214, "segment_id": 0, "entities": [{"name": "<PERSON>", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"name": "Stanford University", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"name": "<PERSON>", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"name": "University", "type": "organization", "description": "Organization mentioned in text", "confidence": 0.6}], "relations": [{"source": "<PERSON>", "target": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "<PERSON>", "target": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "Stanford University", "target": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "Stanford University", "target": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "<PERSON>", "target": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "<PERSON>", "target": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "Stanford University", "target": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "Stanford University", "target": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}]}]}, {"file_path": "test_documents\\sample2.txt", "file_name": "sample2.txt", "content": "<PERSON> works as a software engineer at Acme Corporation. He has been with the company for five years and leads the development team for their flagship product.\n\nAcme Corporation is a technology company founded in 2010. The company focuses on developing enterprise software solutions for large corporations. Their main office is located in San Francisco, California.\n\n<PERSON> is the Chief Technology Officer at Acme Corporation. She oversees all technical aspects of the company's products and manages a team of 50 engineers. <PERSON> has a PhD in Computer Science from Carnegie Mellon University.\n\nCarnegie Mellon University is famous for its computer science program. Many successful tech entrepreneurs and engineers have graduated from this institution. The university is located in Pittsburgh, Pennsylvania.\n\nThe software development industry is highly competitive, with companies constantly innovating to stay ahead. Agile development methodologies have become the standard practice in most technology companies.\n\nSan Francisco is known as the heart of the tech industry, with many major companies having their headquarters there. The city attracts talent from around the world and is home to numerous startups and established tech giants.\n", "segments": [{"text": "<PERSON> works as a software engineer at Acme Corporation. He has been with the company for five years and leads the development team for their flagship product.\n\nAcme Corporation is a technology company founded in 2010. The company focuses on developing enterprise software solutions for large corporations. Their main office is located in San Francisco, California.\n\n<PERSON> is the Chief Technology Officer at Acme Corporation. She oversees all technical aspects of the company's products and manages a team of 50 engineers. <PERSON> has a PhD in Computer Science from Carnegie Mellon University.\n\nCarnegie Mellon University is famous for its computer science program. Many successful tech entrepreneurs and engineers have graduated from this institution. The university is located in Pittsburgh, Pennsylvania.\n\nThe software development industry is highly competitive, with companies constantly innovating to stay ahead. Agile development methodologies have become the standard practice in most technology companies.\n\nSan Francisco is known as the heart of the tech industry, with many major companies having their headquarters there. The city attracts talent from around the world and is home to numerous startups and established tech giants.\n", "start": 0, "end": 1245, "segment_id": 0}], "metadata": {"size": 1245, "format": ".txt"}, "processed_segments": [{"text": "<PERSON> works as a software engineer at Acme Corporation. He has been with the company for five years and leads the development team for their flagship product.\n\nAcme Corporation is a technology company founded in 2010. The company focuses on developing enterprise software solutions for large corporations. Their main office is located in San Francisco, California.\n\n<PERSON> is the Chief Technology Officer at Acme Corporation. She oversees all technical aspects of the company's products and manages a team of 50 engineers. <PERSON> has a PhD in Computer Science from Carnegie Mellon University.\n\nCarnegie Mellon University is famous for its computer science program. Many successful tech entrepreneurs and engineers have graduated from this institution. The university is located in Pittsburgh, Pennsylvania.\n\nThe software development industry is highly competitive, with companies constantly innovating to stay ahead. Agile development methodologies have become the standard practice in most technology companies.\n\nSan Francisco is known as the heart of the tech industry, with many major companies having their headquarters there. The city attracts talent from around the world and is home to numerous startups and established tech giants.\n", "start": 0, "end": 1245, "segment_id": 0, "entities": [{"name": "Acme Corporation", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"name": "Computer Science", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"name": "<PERSON>", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"name": "<PERSON>", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"name": "Chief Technology", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"name": "Carnegie Mellon", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"name": "San Francisco", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"name": "University", "type": "organization", "description": "Organization mentioned in text", "confidence": 0.6}], "relations": [{"source": "Chief Technology", "target": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "Chief Technology", "target": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "Computer Science", "target": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "Computer Science", "target": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "Carnegie Mellon", "target": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "Carnegie Mellon", "target": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "Carnegie Mellon", "target": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "Carnegie Mellon", "target": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}]}]}], "graph": {"entities": [{"id": "entity_1", "name": "<PERSON>", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"id": "entity_2", "name": "Stanford University", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"id": "entity_3", "name": "<PERSON>", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"id": "entity_4", "name": "University", "type": "organization", "description": "Organization mentioned in text", "confidence": 0.6}, {"id": "entity_5", "name": "Acme Corporation", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"id": "entity_6", "name": "Computer Science", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"id": "entity_7", "name": "<PERSON>", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"id": "entity_8", "name": "<PERSON>", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"id": "entity_9", "name": "Chief Technology", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"id": "entity_10", "name": "Carnegie Mellon", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"id": "entity_11", "name": "San Francisco", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}], "relations": [{"source": "entity_1", "target": "entity_4", "source_name": "<PERSON>", "target_name": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "entity_1", "target": "entity_4", "source_name": "<PERSON>", "target_name": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "entity_2", "target": "entity_4", "source_name": "Stanford University", "target_name": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "entity_2", "target": "entity_4", "source_name": "Stanford University", "target_name": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "entity_3", "target": "entity_4", "source_name": "<PERSON>", "target_name": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "entity_3", "target": "entity_4", "source_name": "<PERSON>", "target_name": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "entity_2", "target": "entity_4", "source_name": "Stanford University", "target_name": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "entity_2", "target": "entity_4", "source_name": "Stanford University", "target_name": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "entity_9", "target": "entity_4", "source_name": "Chief Technology", "target_name": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "entity_9", "target": "entity_4", "source_name": "Chief Technology", "target_name": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "entity_6", "target": "entity_4", "source_name": "Computer Science", "target_name": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "entity_6", "target": "entity_4", "source_name": "Computer Science", "target_name": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "entity_10", "target": "entity_4", "source_name": "Carnegie Mellon", "target_name": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "entity_10", "target": "entity_4", "source_name": "Carnegie Mellon", "target_name": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "entity_10", "target": "entity_4", "source_name": "Carnegie Mellon", "target_name": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "entity_10", "target": "entity_4", "source_name": "Carnegie Mellon", "target_name": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}], "statistics": {"total_entities": 11, "total_relations": 16, "entity_types": {"person": 10, "organization": 1}, "relation_types": {"works_at": 16}}}, "statistics": {"total_entities": 11, "total_relations": 16, "entity_types": {"person": 10, "organization": 1}, "relation_types": {"works_at": 16}}}