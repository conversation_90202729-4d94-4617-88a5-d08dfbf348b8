{"mode": "merge", "documents": [{"file_path": "test_documents\\sample1.txt", "file_name": "sample1.txt", "content": "Dr. <PERSON> is a renowned researcher at Stanford University. She specializes in artificial intelligence and machine learning. Her recent work on neural networks has been published in Nature journal.\n\n<PERSON> collaborates with Prof. <PERSON> from MIT on quantum computing projects. They are working together on developing new algorithms for quantum machine learning.\n\nStanford University is located in California and is known for its cutting-edge research in technology. The university has partnerships with many tech companies including Google and Microsoft.\n\nMIT, located in Massachusetts, is another leading institution in technology research. The collaboration between Stanford and MIT has resulted in several breakthrough discoveries in the field of quantum computing.\n\nGoogle has been investing heavily in quantum computing research and has hired several researchers from both Stanford and MIT. Microsoft is also competing in this space with their quantum development kit.\n\nThe field of artificial intelligence is rapidly evolving, with new discoveries being made every day. Machine learning, a subset of AI, has applications in various industries including healthcare, finance, and autonomous vehicles.\n", "segments": [{"text": "Dr. <PERSON> is a renowned researcher at Stanford University. She specializes in artificial intelligence and machine learning. Her recent work on neural networks has been published in Nature journal.\n\n<PERSON> collaborates with Prof. <PERSON> from MIT on quantum computing projects. They are working together on developing new algorithms for quantum machine learning.\n\nStanford University is located in California and is known for its cutting-edge research in technology. The university has partnerships with many tech companies including Google and Microsoft.\n\nMIT, located in Massachusetts, is another leading institution in technology research. The collaboration between Stanford and MIT has resulted in several breakthrough discoveries in the field of quantum computing.\n\nGoogle has been investing heavily in quantum computing research and has hired several researchers from both Stanford and MIT. Microsoft is also competing in this space with their quantum development kit.\n\nThe field of artificial intelligence is rapidly evolving, with new discoveries being made every day. Machine learning, a subset of AI, has applications in various industries including healthcare, finance, and autonomous vehicles.\n", "start": 0, "end": 1214, "segment_id": 0}], "metadata": {"size": 1214, "format": ".txt"}, "processed_segments": [{"text": "Dr. <PERSON> is a renowned researcher at Stanford University. She specializes in artificial intelligence and machine learning. Her recent work on neural networks has been published in Nature journal.\n\n<PERSON> collaborates with Prof. <PERSON> from MIT on quantum computing projects. They are working together on developing new algorithms for quantum machine learning.\n\nStanford University is located in California and is known for its cutting-edge research in technology. The university has partnerships with many tech companies including Google and Microsoft.\n\nMIT, located in Massachusetts, is another leading institution in technology research. The collaboration between Stanford and MIT has resulted in several breakthrough discoveries in the field of quantum computing.\n\nGoogle has been investing heavily in quantum computing research and has hired several researchers from both Stanford and MIT. Microsoft is also competing in this space with their quantum development kit.\n\nThe field of artificial intelligence is rapidly evolving, with new discoveries being made every day. Machine learning, a subset of AI, has applications in various industries including healthcare, finance, and autonomous vehicles.\n", "start": 0, "end": 1214, "segment_id": 0, "entities": [{"name": "Stanford University", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"name": "<PERSON>", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"name": "<PERSON>", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"name": "University", "type": "organization", "description": "Organization mentioned in text", "confidence": 0.6}], "relations": [{"source": "<PERSON>", "target": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "<PERSON>", "target": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "Stanford University", "target": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "Stanford University", "target": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "<PERSON>", "target": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "<PERSON>", "target": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "Stanford University", "target": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "Stanford University", "target": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}]}]}, {"file_path": "test_documents\\sample2.txt", "file_name": "sample2.txt", "content": "<PERSON> works as a software engineer at Acme Corporation. He has been with the company for five years and leads the development team for their flagship product.\n\nAcme Corporation is a technology company founded in 2010. The company focuses on developing enterprise software solutions for large corporations. Their main office is located in San Francisco, California.\n\n<PERSON> is the Chief Technology Officer at Acme Corporation. She oversees all technical aspects of the company's products and manages a team of 50 engineers. <PERSON> has a PhD in Computer Science from Carnegie Mellon University.\n\nCarnegie Mellon University is famous for its computer science program. Many successful tech entrepreneurs and engineers have graduated from this institution. The university is located in Pittsburgh, Pennsylvania.\n\nThe software development industry is highly competitive, with companies constantly innovating to stay ahead. Agile development methodologies have become the standard practice in most technology companies.\n\nSan Francisco is known as the heart of the tech industry, with many major companies having their headquarters there. The city attracts talent from around the world and is home to numerous startups and established tech giants.\n", "segments": [{"text": "<PERSON> works as a software engineer at Acme Corporation. He has been with the company for five years and leads the development team for their flagship product.\n\nAcme Corporation is a technology company founded in 2010. The company focuses on developing enterprise software solutions for large corporations. Their main office is located in San Francisco, California.\n\n<PERSON> is the Chief Technology Officer at Acme Corporation. She oversees all technical aspects of the company's products and manages a team of 50 engineers. <PERSON> has a PhD in Computer Science from Carnegie Mellon University.\n\nCarnegie Mellon University is famous for its computer science program. Many successful tech entrepreneurs and engineers have graduated from this institution. The university is located in Pittsburgh, Pennsylvania.\n\nThe software development industry is highly competitive, with companies constantly innovating to stay ahead. Agile development methodologies have become the standard practice in most technology companies.\n\nSan Francisco is known as the heart of the tech industry, with many major companies having their headquarters there. The city attracts talent from around the world and is home to numerous startups and established tech giants.\n", "start": 0, "end": 1245, "segment_id": 0}], "metadata": {"size": 1245, "format": ".txt"}, "processed_segments": [{"text": "<PERSON> works as a software engineer at Acme Corporation. He has been with the company for five years and leads the development team for their flagship product.\n\nAcme Corporation is a technology company founded in 2010. The company focuses on developing enterprise software solutions for large corporations. Their main office is located in San Francisco, California.\n\n<PERSON> is the Chief Technology Officer at Acme Corporation. She oversees all technical aspects of the company's products and manages a team of 50 engineers. <PERSON> has a PhD in Computer Science from Carnegie Mellon University.\n\nCarnegie Mellon University is famous for its computer science program. Many successful tech entrepreneurs and engineers have graduated from this institution. The university is located in Pittsburgh, Pennsylvania.\n\nThe software development industry is highly competitive, with companies constantly innovating to stay ahead. Agile development methodologies have become the standard practice in most technology companies.\n\nSan Francisco is known as the heart of the tech industry, with many major companies having their headquarters there. The city attracts talent from around the world and is home to numerous startups and established tech giants.\n", "start": 0, "end": 1245, "segment_id": 0, "entities": [{"name": "Carnegie Mellon", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"name": "<PERSON>", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"name": "Computer Science", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"name": "Acme Corporation", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"name": "Chief Technology", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"name": "San Francisco", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"name": "<PERSON>", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"name": "University", "type": "organization", "description": "Organization mentioned in text", "confidence": 0.6}], "relations": [{"source": "Chief Technology", "target": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "Chief Technology", "target": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "Computer Science", "target": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "Computer Science", "target": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "Carnegie Mellon", "target": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "Carnegie Mellon", "target": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "Carnegie Mellon", "target": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "Carnegie Mellon", "target": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}]}]}, {"file_path": "test_documents\\中文示例.txt", "file_name": "中文示例.txt", "content": "张三是北京大学的计算机科学教授，专门研究人工智能和机器学习。他在自然语言处理领域有着丰富的经验，发表了多篇高质量的学术论文。\n\n张三与清华大学的李四教授合作开展深度学习研究。他们共同开发了一套新的神经网络架构，在图像识别任务上取得了突破性进展。\n\n北京大学位于北京市海淀区，是中国顶尖的综合性大学之一。该校在计算机科学、数学、物理等领域都有着卓越的研究实力。\n\n清华大学同样位于北京市海淀区，与北京大学相邻。两所大学在学术研究方面经常开展合作，共同推动中国高等教育的发展。\n\n腾讯公司是中国领先的互联网科技企业，总部位于深圳。公司在人工智能、云计算、社交网络等领域都有重要布局。\n\n阿里巴巴集团总部位于杭州，是全球知名的电子商务和云计算服务提供商。该公司在机器学习和大数据分析方面投入巨大。\n\n人工智能技术正在快速发展，深度学习、自然语言处理、计算机视觉等技术不断取得新的突破。这些技术在医疗、金融、教育等行业都有广泛的应用前景。\n", "segments": [{"text": "张三是北京大学的计算机科学教授，专门研究人工智能和机器学习。他在自然语言处理领域有着丰富的经验，发表了多篇高质量的学术论文。\n\n张三与清华大学的李四教授合作开展深度学习研究。他们共同开发了一套新的神经网络架构，在图像识别任务上取得了突破性进展。\n\n北京大学位于北京市海淀区，是中国顶尖的综合性大学之一。该校在计算机科学、数学、物理等领域都有着卓越的研究实力。\n\n清华大学同样位于北京市海淀区，与北京大学相邻。两所大学在学术研究方面经常开展合作，共同推动中国高等教育的发展。\n\n腾讯公司是中国领先的互联网科技企业，总部位于深圳。公司在人工智能、云计算、社交网络等领域都有重要布局。\n\n阿里巴巴集团总部位于杭州，是全球知名的电子商务和云计算服务提供商。该公司在机器学习和大数据分析方面投入巨大。\n\n人工智能技术正在快速发展，深度学习、自然语言处理、计算机视觉等技术不断取得新的突破。这些技术在医疗、金融、教育等行业都有广泛的应用前景。\n", "start": 0, "end": 416, "segment_id": 0}], "metadata": {"size": 1222, "format": ".txt"}, "processed_segments": [{"text": "张三是北京大学的计算机科学教授，专门研究人工智能和机器学习。他在自然语言处理领域有着丰富的经验，发表了多篇高质量的学术论文。\n\n张三与清华大学的李四教授合作开展深度学习研究。他们共同开发了一套新的神经网络架构，在图像识别任务上取得了突破性进展。\n\n北京大学位于北京市海淀区，是中国顶尖的综合性大学之一。该校在计算机科学、数学、物理等领域都有着卓越的研究实力。\n\n清华大学同样位于北京市海淀区，与北京大学相邻。两所大学在学术研究方面经常开展合作，共同推动中国高等教育的发展。\n\n腾讯公司是中国领先的互联网科技企业，总部位于深圳。公司在人工智能、云计算、社交网络等领域都有重要布局。\n\n阿里巴巴集团总部位于杭州，是全球知名的电子商务和云计算服务提供商。该公司在机器学习和大数据分析方面投入巨大。\n\n人工智能技术正在快速发展，深度学习、自然语言处理、计算机视觉等技术不断取得新的突破。这些技术在医疗、金融、教育等行业都有广泛的应用前景。\n", "start": 0, "end": 416, "segment_id": 0, "entities": [], "relations": []}]}, {"file_path": "test_documents\\企业案例.txt", "file_name": "企业案例.txt", "content": "王五是华为技术有限公司的高级工程师，负责5G通信技术的研发工作。他拥有十年的通信行业经验，曾参与多个重大项目的开发。\n\n华为技术有限公司成立于1987年，总部位于深圳市龙岗区。公司是全球领先的信息与通信技术解决方案供应商，在5G、云计算、人工智能等领域处于行业前沿。\n\n赵六担任小米科技有限责任公司的产品经理，主要负责智能手机产品线的规划和设计。她具有敏锐的市场洞察力，成功推出了多款受欢迎的产品。\n\n小米科技有限责任公司成立于2010年，总部位于北京市海淀区。公司专注于智能硬件和电子产品的研发，以\"让每个人都能享受科技的乐趣\"为使命。\n\n字节跳动有限公司是一家全球化的互联网技术公司，旗下拥有抖音、今日头条等知名产品。公司在算法推荐、内容分发等技术领域有着深厚的积累。\n\n百度公司是中国领先的人工智能公司，在搜索引擎、自动驾驶、智能云等领域都有重要布局。公司致力于用科技让复杂的世界更简单。\n\n这些科技企业都在推动中国数字经济的发展，通过技术创新为社会创造价值。它们在人才培养、技术研发、产业合作等方面都发挥着重要作用。\n", "segments": [{"text": "王五是华为技术有限公司的高级工程师，负责5G通信技术的研发工作。他拥有十年的通信行业经验，曾参与多个重大项目的开发。\n\n华为技术有限公司成立于1987年，总部位于深圳市龙岗区。公司是全球领先的信息与通信技术解决方案供应商，在5G、云计算、人工智能等领域处于行业前沿。\n\n赵六担任小米科技有限责任公司的产品经理，主要负责智能手机产品线的规划和设计。她具有敏锐的市场洞察力，成功推出了多款受欢迎的产品。\n\n小米科技有限责任公司成立于2010年，总部位于北京市海淀区。公司专注于智能硬件和电子产品的研发，以\"让每个人都能享受科技的乐趣\"为使命。\n\n字节跳动有限公司是一家全球化的互联网技术公司，旗下拥有抖音、今日头条等知名产品。公司在算法推荐、内容分发等技术领域有着深厚的积累。\n\n百度公司是中国领先的人工智能公司，在搜索引擎、自动驾驶、智能云等领域都有重要布局。公司致力于用科技让复杂的世界更简单。\n\n这些科技企业都在推动中国数字经济的发展，通过技术创新为社会创造价值。它们在人才培养、技术研发、产业合作等方面都发挥着重要作用。\n", "start": 0, "end": 463, "segment_id": 0}], "metadata": {"size": 1335, "format": ".txt"}, "processed_segments": [{"text": "王五是华为技术有限公司的高级工程师，负责5G通信技术的研发工作。他拥有十年的通信行业经验，曾参与多个重大项目的开发。\n\n华为技术有限公司成立于1987年，总部位于深圳市龙岗区。公司是全球领先的信息与通信技术解决方案供应商，在5G、云计算、人工智能等领域处于行业前沿。\n\n赵六担任小米科技有限责任公司的产品经理，主要负责智能手机产品线的规划和设计。她具有敏锐的市场洞察力，成功推出了多款受欢迎的产品。\n\n小米科技有限责任公司成立于2010年，总部位于北京市海淀区。公司专注于智能硬件和电子产品的研发，以\"让每个人都能享受科技的乐趣\"为使命。\n\n字节跳动有限公司是一家全球化的互联网技术公司，旗下拥有抖音、今日头条等知名产品。公司在算法推荐、内容分发等技术领域有着深厚的积累。\n\n百度公司是中国领先的人工智能公司，在搜索引擎、自动驾驶、智能云等领域都有重要布局。公司致力于用科技让复杂的世界更简单。\n\n这些科技企业都在推动中国数字经济的发展，通过技术创新为社会创造价值。它们在人才培养、技术研发、产业合作等方面都发挥着重要作用。\n", "start": 0, "end": 463, "segment_id": 0, "entities": [], "relations": []}]}], "graph": {"entities": [{"id": "entity_1", "name": "Stanford University", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"id": "entity_2", "name": "<PERSON>", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"id": "entity_3", "name": "<PERSON>", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"id": "entity_4", "name": "University", "type": "organization", "description": "Organization mentioned in text", "confidence": 0.6}, {"id": "entity_5", "name": "Carnegie Mellon", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"id": "entity_6", "name": "<PERSON>", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"id": "entity_7", "name": "Computer Science", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"id": "entity_8", "name": "Acme Corporation", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"id": "entity_9", "name": "Chief Technology", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"id": "entity_10", "name": "San Francisco", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}, {"id": "entity_11", "name": "<PERSON>", "type": "person", "description": "Person mentioned in text", "confidence": 0.7}], "relations": [{"source": "entity_2", "target": "entity_4", "source_name": "<PERSON>", "target_name": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "entity_2", "target": "entity_4", "source_name": "<PERSON>", "target_name": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "entity_1", "target": "entity_4", "source_name": "Stanford University", "target_name": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "entity_1", "target": "entity_4", "source_name": "Stanford University", "target_name": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "entity_3", "target": "entity_4", "source_name": "<PERSON>", "target_name": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "entity_3", "target": "entity_4", "source_name": "<PERSON>", "target_name": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "entity_1", "target": "entity_4", "source_name": "Stanford University", "target_name": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "entity_1", "target": "entity_4", "source_name": "Stanford University", "target_name": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "entity_9", "target": "entity_4", "source_name": "Chief Technology", "target_name": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "entity_9", "target": "entity_4", "source_name": "Chief Technology", "target_name": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "entity_7", "target": "entity_4", "source_name": "Computer Science", "target_name": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "entity_7", "target": "entity_4", "source_name": "Computer Science", "target_name": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "entity_5", "target": "entity_4", "source_name": "Carnegie Mellon", "target_name": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "entity_5", "target": "entity_4", "source_name": "Carnegie Mellon", "target_name": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "entity_5", "target": "entity_4", "source_name": "Carnegie Mellon", "target_name": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}, {"source": "entity_5", "target": "entity_4", "source_name": "Carnegie Mellon", "target_name": "University", "relation_type": "works_at", "description": "Inferred from text proximity", "confidence": 0.5}], "statistics": {"total_entities": 11, "total_relations": 16, "entity_types": {"person": 10, "organization": 1}, "relation_types": {"works_at": 16}}}, "statistics": {"total_entities": 11, "total_relations": 16, "entity_types": {"person": 10, "organization": 1}, "relation_types": {"works_at": 16}}}