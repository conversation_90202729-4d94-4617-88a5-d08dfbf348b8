# 🚀 Knowledge Graph Builder - Quick Start

## ⚡ Immediate Solution for "ModuleNotFoundError"

If you're seeing dependency errors, run this **ONE COMMAND**:

```bash
python quick_fix.py
```

This will automatically install all required dependencies and set up the environment.

## 🎯 Three Ways to Run the Application

### 1. Quick Web Interface (Recommended)
```bash
python web_simple.py
```
Then open: http://localhost:8000

### 2. Full CLI Application
```bash
python main.py process /path/to/your/documents
```

### 3. Web Server via CLI
```bash
python main.py serve
```

## 📋 Step-by-Step Setup

### Step 1: Install Dependencies
```bash
python quick_fix.py
```

### Step 2: Start the Application
```bash
python web_simple.py
```

### Step 3: Use the Web Interface
1. Open http://localhost:8000 in your browser
2. Upload TXT or MD files
3. Choose processing mode (merge/batch)
4. Click "Start Processing"
5. View results and download JSON

## 🔧 What This Application Does

### Core Features
- **Document Processing**: Upload and process text documents
- **Entity Extraction**: AI-powered extraction of people, organizations, concepts
- **Relationship Discovery**: Find connections between entities
- **Knowledge Graph**: Build interactive graph visualizations
- **Search & Export**: Search entities and export results

### Supported Formats
- `.txt` - Plain text files
- `.md` - Markdown files
- More formats coming soon (DOCX, PDF)

## 🛠️ Project Structure

```
planner/
├── quick_fix.py         # 🔧 Dependency installer
├── web_simple.py        # 🌐 Simple web interface
├── main.py              # 💻 CLI application
├── core/                # 🧠 Core processing modules
│   ├── pipeline.py      # Main processing pipeline
│   ├── document_processor.py
│   ├── entity_extractor.py
│   └── graph_builder.py
├── config/              # ⚙️ Configuration
├── requirements-working.txt  # 📦 Dependencies
└── .env                 # 🔐 Environment settings
```

## 🎮 Usage Examples

### CLI Processing
```bash
# Process a single file
python main.py process document.txt

# Process a directory
python main.py process /path/to/documents --mode merge

# Batch processing
python main.py process /path/to/documents --mode batch --output ./results
```

### Web Interface
1. Start server: `python web_simple.py`
2. Upload files via web interface
3. View results in browser
4. Download JSON results

## 🔍 Example Output

After processing, you'll get:

```json
{
  "mode": "merge",
  "processing_stats": {
    "documents_processed": 2,
    "entities_extracted": 15,
    "relations_extracted": 8,
    "processing_time": 12.34
  },
  "statistics": {
    "total_entities": 12,
    "total_relations": 6,
    "entity_types": {
      "person": 4,
      "organization": 3,
      "concept": 5
    }
  }
}
```

## ⚙️ Configuration

The `.env` file contains important settings:

```bash
# LLM API (for advanced features)
LLM_BASE_URL=https://gateway.chat.sensedeal.vip/v1
LLM_API_KEY=your-api-key-here

# Processing
PROCESSING_MODE=merge
MAX_WORKERS=5

# Web Server
WEB_HOST=0.0.0.0
WEB_PORT=8000
```

## 🚨 Troubleshooting

### "ModuleNotFoundError"
```bash
python quick_fix.py
```

### "No module named 'rich'"
```bash
pip install rich click fastapi uvicorn
```

### Web server won't start
```bash
python web_simple.py
```

### Still having issues?
1. Check Python version (3.8+ required)
2. Try: `pip install -r requirements-working.txt`
3. Use the simple web interface: `python web_simple.py`

## 🎯 Quick Test

1. Create a test file:
   ```bash
   echo "John Doe works at Acme Corporation. He is a software engineer." > test.txt
   ```

2. Process it:
   ```bash
   python main.py process test.txt
   ```

3. Or use web interface:
   ```bash
   python web_simple.py
   # Upload test.txt via browser
   ```

## 🌟 Features

### Current Features ✅
- Document processing (TXT, MD)
- Entity extraction (pattern-based)
- Relationship discovery
- Web interface
- JSON export
- Search functionality

### Coming Soon 🚧
- LLM integration for better extraction
- PDF/DOCX support
- Graph visualization
- Advanced search
- Export to other formats

## 🎉 Success!

Once everything is working, you should see:
- Web interface at http://localhost:8000
- Ability to upload and process documents
- Entity and relationship extraction
- JSON export of knowledge graphs

## 📞 Need Help?

If you're still having issues:
1. Make sure Python 3.8+ is installed
2. Run `python quick_fix.py` to install dependencies
3. Try the simple web interface: `python web_simple.py`
4. Check the console for error messages

Happy knowledge graph building! 🚀
