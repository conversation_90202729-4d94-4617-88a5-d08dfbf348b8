"""Graph building and management module."""

from typing import List, Dict, Any, Set, Tuple
from collections import defaultdict
import json

from loguru import logger

try:
    import networkx as nx
except ImportError:
    logger.warning("NetworkX not available, using simple graph representation")
    nx = None


class GraphBuilder:
    """Build and manage knowledge graphs."""
    
    def __init__(self):
        self.entities = {}  # entity_id -> entity_data
        self.relations = []  # list of relations
        self.entity_name_to_id = {}  # name -> entity_id
        self.next_entity_id = 1
        
        if nx:
            self.graph = nx.DiGraph()
        else:
            self.graph = None
    
    def add_entity(self, name: str, entity_type: str, description: str = "", **kwargs) -> str:
        """Add an entity to the graph."""
        # Check if entity already exists (case-insensitive)
        normalized_name = name.lower().strip()
        
        if normalized_name in self.entity_name_to_id:
            entity_id = self.entity_name_to_id[normalized_name]
            # Update existing entity with new information
            existing = self.entities[entity_id]
            if description and not existing.get("description"):
                existing["description"] = description
            existing.update(kwargs)
            return entity_id
        
        # Create new entity
        entity_id = f"entity_{self.next_entity_id}"
        self.next_entity_id += 1
        
        entity_data = {
            "id": entity_id,
            "name": name,
            "type": entity_type,
            "description": description,
            **kwargs
        }
        
        self.entities[entity_id] = entity_data
        self.entity_name_to_id[normalized_name] = entity_id
        
        if self.graph:
            self.graph.add_node(entity_id, **entity_data)
        
        logger.debug(f"Added entity: {name} ({entity_type})")
        return entity_id
    
    def add_relation(self, source_name: str, target_name: str, relation_type: str, 
                    description: str = "", **kwargs):
        """Add a relation between entities."""
        # Get or create entities
        source_id = self._get_entity_id_by_name(source_name)
        target_id = self._get_entity_id_by_name(target_name)
        
        if not source_id or not target_id:
            logger.warning(f"Cannot create relation: missing entities {source_name} -> {target_name}")
            return
        
        relation_data = {
            "source": source_id,
            "target": target_id,
            "source_name": source_name,
            "target_name": target_name,
            "relation_type": relation_type,
            "description": description,
            **kwargs
        }
        
        self.relations.append(relation_data)
        
        if self.graph:
            self.graph.add_edge(source_id, target_id, **relation_data)
        
        logger.debug(f"Added relation: {source_name} --{relation_type}--> {target_name}")
    
    def _get_entity_id_by_name(self, name: str) -> str:
        """Get entity ID by name, return None if not found."""
        normalized_name = name.lower().strip()
        return self.entity_name_to_id.get(normalized_name)
    
    def merge_entities(self, entities_data: List[Dict[str, Any]]):
        """Merge multiple entities into the graph."""
        for entity in entities_data:
            self.add_entity(
                name=entity["name"],
                entity_type=entity["type"],
                description=entity.get("description", ""),
                confidence=entity.get("confidence", 1.0)
            )
    
    def merge_relations(self, relations_data: List[Dict[str, Any]]):
        """Merge multiple relations into the graph."""
        for relation in relations_data:
            self.add_relation(
                source_name=relation["source"],
                target_name=relation["target"],
                relation_type=relation["relation_type"],
                description=relation.get("description", ""),
                confidence=relation.get("confidence", 1.0)
            )
    
    def get_entity_by_name(self, name: str) -> Dict[str, Any]:
        """Get entity data by name."""
        entity_id = self._get_entity_id_by_name(name)
        if entity_id:
            return self.entities[entity_id]
        return None
    
    def get_neighbors(self, entity_name: str) -> List[Dict[str, Any]]:
        """Get neighboring entities."""
        entity_id = self._get_entity_id_by_name(entity_name)
        if not entity_id:
            return []
        
        neighbors = []
        
        # Find relations where this entity is source or target
        for relation in self.relations:
            if relation["source"] == entity_id:
                target_entity = self.entities[relation["target"]]
                neighbors.append({
                    "entity": target_entity,
                    "relation": relation["relation_type"],
                    "direction": "outgoing"
                })
            elif relation["target"] == entity_id:
                source_entity = self.entities[relation["source"]]
                neighbors.append({
                    "entity": source_entity,
                    "relation": relation["relation_type"],
                    "direction": "incoming"
                })
        
        return neighbors
    
    def search_entities(self, query: str, entity_type: str = None) -> List[Dict[str, Any]]:
        """Search entities by name or description."""
        query_lower = query.lower()
        results = []
        
        for entity in self.entities.values():
            # Check if query matches name or description
            name_match = query_lower in entity["name"].lower()
            desc_match = query_lower in entity.get("description", "").lower()
            type_match = not entity_type or entity["type"] == entity_type
            
            if (name_match or desc_match) and type_match:
                # Calculate simple relevance score
                score = 0
                if entity["name"].lower() == query_lower:
                    score = 1.0
                elif entity["name"].lower().startswith(query_lower):
                    score = 0.8
                elif query_lower in entity["name"].lower():
                    score = 0.6
                elif desc_match:
                    score = 0.4
                
                results.append({
                    "entity": entity,
                    "score": score
                })
        
        # Sort by relevance score
        results.sort(key=lambda x: x["score"], reverse=True)
        return results
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get graph statistics."""
        entity_types = defaultdict(int)
        relation_types = defaultdict(int)
        
        for entity in self.entities.values():
            entity_types[entity["type"]] += 1
        
        for relation in self.relations:
            relation_types[relation["relation_type"]] += 1
        
        return {
            "total_entities": len(self.entities),
            "total_relations": len(self.relations),
            "entity_types": dict(entity_types),
            "relation_types": dict(relation_types)
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """Export graph to dictionary format."""
        return {
            "entities": list(self.entities.values()),
            "relations": self.relations,
            "statistics": self.get_statistics()
        }
    
    def to_json(self) -> str:
        """Export graph to JSON format."""
        return json.dumps(self.to_dict(), indent=2, ensure_ascii=False)
    
    def from_dict(self, data: Dict[str, Any]):
        """Import graph from dictionary format."""
        self.entities = {}
        self.relations = []
        self.entity_name_to_id = {}
        self.next_entity_id = 1
        
        if self.graph:
            self.graph.clear()
        
        # Import entities
        for entity in data.get("entities", []):
            entity_id = entity["id"]
            self.entities[entity_id] = entity
            self.entity_name_to_id[entity["name"].lower().strip()] = entity_id
            
            if self.graph:
                self.graph.add_node(entity_id, **entity)
            
            # Update next_entity_id
            if entity_id.startswith("entity_"):
                try:
                    num = int(entity_id.split("_")[1])
                    self.next_entity_id = max(self.next_entity_id, num + 1)
                except ValueError:
                    pass
        
        # Import relations
        for relation in data.get("relations", []):
            self.relations.append(relation)
            
            if self.graph:
                self.graph.add_edge(relation["source"], relation["target"], **relation)
    
    def from_json(self, json_str: str):
        """Import graph from JSON format."""
        data = json.loads(json_str)
        self.from_dict(data)
