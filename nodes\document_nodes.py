"""Document processing nodes for parsing and content segmentation."""

from pathlib import Path
from typing import Dict, List, Any, Optional

from pocketflow import As<PERSON><PERSON><PERSON>, Node
from loguru import logger

from utils.file_utils import FileUtils, ContentSegment
from utils.security_filters import SecurityFilters


class ParseDocument(AsyncNode):
    """Parse and validate input files of various formats."""
    
    def __init__(self, max_retries: int = 3, wait: int = 10):
        super().__init__(max_retries=max_retries, wait=wait)
        self.file_utils = FileUtils()
        self.security_filters = SecurityFilters()
    
    async def prep_async(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare for document parsing.
        
        Args:
            shared: Shared data structure
            
        Returns:
            Preparation data
        """
        context = shared.get("context", {})
        input_path = context.get("input_path")
        
        if not input_path:
            raise ValueError("No input path specified in context")
        
        input_path = Path(input_path)
        
        if not input_path.exists():
            raise FileNotFoundError(f"Input path does not exist: {input_path}")
        
        # Determine if it's a file or directory
        if input_path.is_file():
            files_to_process = [input_path]
        elif input_path.is_directory():
            # Find all supported files in directory
            supported_extensions = context.get("supported_formats", ["txt", "docx", "pdf", "chat"])
            files_to_process = []
            
            for ext in supported_extensions:
                files_to_process.extend(input_path.glob(f"*.{ext}"))
                files_to_process.extend(input_path.glob(f"**/*.{ext}"))  # Recursive
        else:
            raise ValueError(f"Input path is neither file nor directory: {input_path}")
        
        if not files_to_process:
            raise ValueError(f"No supported files found in: {input_path}")
        
        logger.info(f"Found {len(files_to_process)} files to process")
        
        return {
            "files_to_process": files_to_process,
            "supported_formats": context.get("supported_formats", [])
        }
    
    async def exec_async(self, prep_res: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Execute document parsing for all files.
        
        Args:
            prep_res: Preparation results
            
        Returns:
            List of parsed document data
        """
        files_to_process = prep_res["files_to_process"]
        parsed_documents = []
        
        for file_path in files_to_process:
            try:
                logger.info(f"Parsing file: {file_path}")
                
                # Parse file
                file_type, content = self.file_utils.parse_file(file_path)
                
                # Security filtering
                filter_result = self.security_filters.filter_content(
                    content,
                    redact_pii=True,
                    sanitize_html=True,
                    validate_input=True
                )
                
                if not filter_result["is_safe"]:
                    logger.warning(f"File {file_path} failed security checks: {filter_result['warnings']}")
                    continue
                
                # Create document entry
                document = {
                    "file_path": str(file_path),
                    "file_type": file_type,
                    "source_weight": 1,  # Default weight, can be adjusted based on file type or source
                    "raw_content": filter_result["filtered_text"],
                    "content_segments": [],
                    "raw_entities": [],
                    "relations": [],
                    "normalized_entities": [],
                    "security_warnings": filter_result["warnings"],
                    "pii_detected": filter_result["pii_detection"].has_pii if filter_result["pii_detection"] else False
                }
                
                parsed_documents.append(document)
                logger.info(f"Successfully parsed {file_path}: {len(content)} characters")
                
            except Exception as e:
                logger.error(f"Failed to parse {file_path}: {e}")
                # Continue with other files
                continue
        
        if not parsed_documents:
            raise ValueError("No documents were successfully parsed")
        
        return parsed_documents
    
    async def exec_fallback_async(self, prep_res: Dict[str, Any], exc: Exception) -> List[Dict[str, Any]]:
        """
        Fallback execution when parsing fails.
        
        Args:
            prep_res: Preparation results
            exc: Exception that caused failure
            
        Returns:
            Empty list or partial results
        """
        logger.error(f"Document parsing failed: {exc}")
        
        # Try to return any successfully parsed documents from previous attempts
        return []
    
    async def post_async(
        self,
        shared: Dict[str, Any],
        prep_res: Dict[str, Any],
        exec_res: List[Dict[str, Any]]
    ) -> str:
        """
        Post-process parsing results.
        
        Args:
            shared: Shared data structure
            prep_res: Preparation results
            exec_res: Execution results
            
        Returns:
            Next action
        """
        # Store parsed documents in shared data
        shared["documents"] = exec_res
        
        # Update task state
        task_state = shared.setdefault("task_state", {})
        completed_files = task_state.setdefault("completed_files", set())
        
        for doc in exec_res:
            completed_files.add(doc["file_path"])
        
        logger.info(f"Parsed {len(exec_res)} documents successfully")
        
        # Determine next action based on content length
        needs_segmentation = any(
            len(doc["raw_content"]) > 5000  # Threshold for segmentation
            for doc in exec_res
        )
        
        return "needs_segmentation" if needs_segmentation else "default"


class SplitLongContent(Node):
    """Split long document content into semantic segments."""
    
    def __init__(self):
        super().__init__()
        self.file_utils = FileUtils()
    
    def prep(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare for content splitting.
        
        Args:
            shared: Shared data structure
            
        Returns:
            Preparation data
        """
        documents = shared.get("documents", [])
        
        if not documents:
            raise ValueError("No documents found for content splitting")
        
        # Calculate optimal chunk sizes for each document
        prep_data = []
        
        for doc in documents:
            content = doc["raw_content"]
            
            if len(content) > 1000:  # Only split if content is substantial
                chunk_size = self.file_utils.estimate_optimal_chunk_size(content)
                prep_data.append({
                    "document": doc,
                    "chunk_size": chunk_size,
                    "needs_splitting": True
                })
            else:
                prep_data.append({
                    "document": doc,
                    "chunk_size": len(content),
                    "needs_splitting": False
                })
        
        return {"documents_prep": prep_data}
    
    def exec(self, prep_res: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Execute content splitting.
        
        Args:
            prep_res: Preparation results
            
        Returns:
            List of documents with segments
        """
        documents_prep = prep_res["documents_prep"]
        processed_documents = []
        
        for doc_prep in documents_prep:
            document = doc_prep["document"].copy()
            
            if doc_prep["needs_splitting"]:
                content = document["raw_content"]
                chunk_size = doc_prep["chunk_size"]
                
                # Split content into segments
                segments = self.file_utils.split_by_semantic_boundaries(content, chunk_size)
                
                # Convert to dictionary format
                document["content_segments"] = [
                    {
                        "text": segment.text,
                        "start_pos": segment.start_pos,
                        "end_pos": segment.end_pos,
                        "metadata": segment.metadata or {}
                    }
                    for segment in segments
                ]
                
                logger.info(f"Split document {document['file_path']} into {len(segments)} segments")
            else:
                # Create single segment for short content
                document["content_segments"] = [{
                    "text": document["raw_content"],
                    "start_pos": 0,
                    "end_pos": len(document["raw_content"]),
                    "metadata": {}
                }]
                
                logger.info(f"Document {document['file_path']} kept as single segment")
            
            processed_documents.append(document)
        
        return processed_documents
    
    def post(
        self,
        shared: Dict[str, Any],
        prep_res: Dict[str, Any],
        exec_res: List[Dict[str, Any]]
    ) -> str:
        """
        Post-process splitting results.
        
        Args:
            shared: Shared data structure
            prep_res: Preparation results
            exec_res: Execution results
            
        Returns:
            Next action
        """
        # Update documents in shared data
        shared["documents"] = exec_res
        
        # Calculate total segments for progress tracking
        total_segments = sum(len(doc["content_segments"]) for doc in exec_res)
        
        logger.info(f"Content splitting completed: {total_segments} total segments")
        
        # Determine next action based on number of segments
        if total_segments > 1:
            return "needs_parallel_processing"
        else:
            return "default"
