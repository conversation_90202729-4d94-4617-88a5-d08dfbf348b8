"""
Base node classes for the knowledge graph builder
Provides foundation for individual processing nodes
"""

import asyncio
import time
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum
from loguru import logger


class NodeStatus(Enum):
    """Node execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class NodeResult:
    """Node execution result"""
    status: NodeStatus
    result: Any = None
    error: Optional[Exception] = None
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
    
    @property
    def duration(self) -> Optional[float]:
        """Get node duration in seconds"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return None
    
    @property
    def is_success(self) -> bool:
        """Check if node completed successfully"""
        return self.status == NodeStatus.COMPLETED


class BaseNode(ABC):
    """Base class for all processing nodes"""
    
    def __init__(self, node_id: Optional[str] = None, 
                 max_retries: int = 0,
                 retry_delay: float = 1.0,
                 timeout: Optional[float] = None):
        self.node_id = node_id or f"{self.__class__.__name__}_{id(self)}"
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.timeout = timeout
        
        # Node state
        self.status = NodeStatus.PENDING
        self.result = None
        self.error = None
        self.start_time = None
        self.end_time = None
        
        # Cancellation
        self._cancelled = False
    
    @abstractmethod
    def prep(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare data for execution"""
        pass
    
    @abstractmethod
    def exec(self, prep_result: Dict[str, Any]) -> Any:
        """Execute node logic (synchronous)"""
        pass
    
    def exec_async(self, prep_result: Dict[str, Any]) -> Any:
        """Execute node logic (asynchronous) - override if needed"""
        # Default implementation runs sync exec in thread pool
        loop = asyncio.get_event_loop()
        return loop.run_in_executor(None, self.exec, prep_result)
    
    def post(self, shared: Dict[str, Any], prep_result: Dict[str, Any], 
             exec_result: Any) -> str:
        """Post-process results and return next node"""
        return "default"
    
    def exec_fallback(self, prep_result: Dict[str, Any], 
                     exception: Exception) -> Any:
        """Fallback execution when main execution fails"""
        raise exception
    
    async def run(self, shared: Dict[str, Any]) -> NodeResult:
        """Run the node with error handling and timing"""
        
        logger.debug(f"Starting node: {self.node_id}")
        
        self.status = NodeStatus.RUNNING
        self.start_time = time.time()
        
        try:
            # Preparation phase
            prep_result = self.prep(shared)
            
            # Execution phase with retry logic
            exec_result = await self._execute_with_retry(prep_result)
            
            # Post-processing phase
            next_node = self.post(shared, prep_result, exec_result)
            
            if self._cancelled:
                self.status = NodeStatus.CANCELLED
                logger.debug(f"Node cancelled: {self.node_id}")
            else:
                self.status = NodeStatus.COMPLETED
                self.result = exec_result
                logger.debug(f"Node completed: {self.node_id} in {self.duration:.2f}s")
            
        except asyncio.CancelledError:
            self.status = NodeStatus.CANCELLED
            logger.debug(f"Node cancelled: {self.node_id}")
            
        except Exception as e:
            self.status = NodeStatus.FAILED
            self.error = e
            logger.error(f"Node failed: {self.node_id} - {e}")
            
        finally:
            self.end_time = time.time()
        
        return NodeResult(
            status=self.status,
            result=self.result,
            error=self.error,
            start_time=self.start_time,
            end_time=self.end_time
        )
    
    async def _execute_with_retry(self, prep_result: Dict[str, Any]) -> Any:
        """Execute with retry logic"""
        
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            if self._cancelled:
                raise asyncio.CancelledError()
            
            try:
                # Execute with timeout if specified
                if self.timeout:
                    exec_result = await asyncio.wait_for(
                        self.exec_async(prep_result),
                        timeout=self.timeout
                    )
                else:
                    exec_result = await self.exec_async(prep_result)
                
                return exec_result
                
            except asyncio.CancelledError:
                raise
                
            except Exception as e:
                last_exception = e
                
                if attempt < self.max_retries:
                    logger.warning(f"Node {self.node_id} failed (attempt {attempt + 1}/{self.max_retries + 1}): {e}")
                    await asyncio.sleep(self.retry_delay * (2 ** attempt))  # Exponential backoff
                else:
                    # Try fallback on final failure
                    try:
                        return self.exec_fallback(prep_result, e)
                    except Exception:
                        pass
        
        raise last_exception
    
    def cancel(self):
        """Cancel node execution"""
        self._cancelled = True
        logger.debug(f"Node cancellation requested: {self.node_id}")
    
    def is_cancelled(self) -> bool:
        """Check if node is cancelled"""
        return self._cancelled
    
    @property
    def duration(self) -> Optional[float]:
        """Get node duration"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return None


class AsyncNode(BaseNode):
    """Node that executes asynchronously"""
    
    @abstractmethod
    async def exec_async(self, prep_result: Dict[str, Any]) -> Any:
        """Execute node logic asynchronously"""
        pass
    
    def exec(self, prep_result: Dict[str, Any]) -> Any:
        """Sync exec not used for async nodes"""
        raise NotImplementedError("AsyncNode uses exec_async instead of exec")


class BatchNode(BaseNode):
    """Node that processes data in batches"""
    
    def __init__(self, node_id: Optional[str] = None,
                 batch_size: int = 10,
                 max_retries: int = 0,
                 retry_delay: float = 1.0,
                 timeout: Optional[float] = None):
        super().__init__(node_id, max_retries, retry_delay, timeout)
        self.batch_size = batch_size
    
    @abstractmethod
    def exec_batch(self, batch: List[Any], prep_result: Dict[str, Any]) -> List[Any]:
        """Execute batch processing"""
        pass
    
    def exec(self, prep_result: Dict[str, Any]) -> Any:
        """Execute batch processing on all items"""
        
        items = prep_result.get('items', [])
        
        if not items:
            return []
        
        # Split into batches
        batches = [
            items[i:i + self.batch_size]
            for i in range(0, len(items), self.batch_size)
        ]
        
        # Process batches
        all_results = []
        for batch in batches:
            batch_results = self.exec_batch(batch, prep_result)
            if isinstance(batch_results, list):
                all_results.extend(batch_results)
            else:
                all_results.append(batch_results)
        
        return all_results


class AsyncBatchNode(AsyncNode):
    """Node that processes data in batches asynchronously"""
    
    def __init__(self, node_id: Optional[str] = None,
                 batch_size: int = 10,
                 max_concurrent: int = 3,
                 max_retries: int = 0,
                 retry_delay: float = 1.0,
                 timeout: Optional[float] = None):
        super().__init__(node_id, max_retries, retry_delay, timeout)
        self.batch_size = batch_size
        self.max_concurrent = max_concurrent
    
    @abstractmethod
    async def exec_batch_async(self, batch: List[Any], prep_result: Dict[str, Any]) -> List[Any]:
        """Execute batch processing asynchronously"""
        pass
    
    async def exec_async(self, prep_result: Dict[str, Any]) -> Any:
        """Execute async batch processing on all items"""
        
        items = prep_result.get('items', [])
        
        if not items:
            return []
        
        # Split into batches
        batches = [
            items[i:i + self.batch_size]
            for i in range(0, len(items), self.batch_size)
        ]
        
        # Process batches with concurrency control
        semaphore = asyncio.Semaphore(self.max_concurrent)
        
        async def process_batch_with_semaphore(batch):
            async with semaphore:
                return await self.exec_batch_async(batch, prep_result)
        
        # Execute all batches
        batch_results = await asyncio.gather(
            *[process_batch_with_semaphore(batch) for batch in batches],
            return_exceptions=True
        )
        
        # Flatten results
        all_results = []
        for batch_result in batch_results:
            if isinstance(batch_result, Exception):
                logger.error(f"Batch processing failed: {batch_result}")
                continue
            
            if isinstance(batch_result, list):
                all_results.extend(batch_result)
            else:
                all_results.append(batch_result)
        
        return all_results


class ConditionalNode(BaseNode):
    """Node that executes conditionally"""
    
    def __init__(self, condition: Callable[[Dict[str, Any]], bool],
                 node_id: Optional[str] = None,
                 max_retries: int = 0,
                 retry_delay: float = 1.0,
                 timeout: Optional[float] = None):
        super().__init__(node_id, max_retries, retry_delay, timeout)
        self.condition = condition
    
    def prep(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """Check condition and prepare"""
        should_execute = self.condition(shared)
        return {
            'should_execute': should_execute,
            'shared': shared
        }
    
    def exec(self, prep_result: Dict[str, Any]) -> Any:
        """Execute conditionally"""
        if prep_result['should_execute']:
            return self.exec_conditional(prep_result)
        else:
            logger.debug(f"Condition false, skipping node: {self.node_id}")
            return None
    
    @abstractmethod
    def exec_conditional(self, prep_result: Dict[str, Any]) -> Any:
        """Execute when condition is true"""
        pass


class CacheNode(BaseNode):
    """Node with caching capability"""
    
    def __init__(self, node_id: Optional[str] = None,
                 use_cache: bool = True,
                 cache_ttl: Optional[float] = None,
                 max_retries: int = 0,
                 retry_delay: float = 1.0,
                 timeout: Optional[float] = None):
        super().__init__(node_id, max_retries, retry_delay, timeout)
        self.use_cache = use_cache
        self.cache_ttl = cache_ttl
        self._cache = {}
    
    def get_cache_key(self, prep_result: Dict[str, Any]) -> str:
        """Generate cache key"""
        # Simple implementation - override for custom keys
        return str(hash(str(prep_result)))
    
    def exec(self, prep_result: Dict[str, Any]) -> Any:
        """Execute with caching"""
        
        if not self.use_cache:
            return self.exec_cached(prep_result)
        
        cache_key = self.get_cache_key(prep_result)
        
        # Check cache
        if cache_key in self._cache:
            cached_result, timestamp = self._cache[cache_key]
            
            # Check TTL
            if self.cache_ttl is None or (time.time() - timestamp) < self.cache_ttl:
                logger.debug(f"Cache hit for node: {self.node_id}")
                return cached_result
        
        # Execute and cache
        result = self.exec_cached(prep_result)
        self._cache[cache_key] = (result, time.time())
        
        return result
    
    @abstractmethod
    def exec_cached(self, prep_result: Dict[str, Any]) -> Any:
        """Execute the actual logic (to be cached)"""
        pass
