"""Tests for web interface and API."""

import pytest
import json
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient

from web.app import app


@pytest.fixture
def client():
    """Create test client for FastAPI app."""
    return TestClient(app)


@pytest.fixture
def temp_text_file():
    """Create temporary text file for testing."""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write("This is test content for processing.")
        temp_path = f.name
    
    yield temp_path
    
    # Cleanup
    Path(temp_path).unlink()


class TestHealthEndpoint:
    """Test health check endpoint."""
    
    def test_health_check(self, client):
        """Test health check endpoint."""
        response = client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "version" in data


class TestStaticPages:
    """Test static page endpoints."""
    
    def test_index_page(self, client):
        """Test index page."""
        response = client.get("/")
        
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]
    
    def test_upload_page(self, client):
        """Test upload page."""
        response = client.get("/upload")
        
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]
    
    def test_search_page(self, client):
        """Test search page."""
        response = client.get("/search")
        
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]
    
    def test_graph_page(self, client):
        """Test graph visualization page."""
        response = client.get("/graph")
        
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]
    
    def test_jobs_page(self, client):
        """Test jobs page."""
        response = client.get("/jobs")
        
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]


class TestProcessingAPI:
    """Test document processing API endpoints."""
    
    @patch('web.app.process_documents_background')
    def test_start_processing_success(self, mock_background, client, temp_text_file):
        """Test successful processing start."""
        # Prepare file upload
        with open(temp_text_file, 'rb') as f:
            files = [("files", ("test.txt", f, "text/plain"))]
            data = {
                "request_data": json.dumps({
                    "mode": "merge",
                    "output_formats": ["html", "json"]
                })
            }
            
            response = client.post("/api/process", files=files, data=data)
        
        assert response.status_code == 200
        result = response.json()
        
        assert "job_id" in result
        assert result["status"] == "queued"
        assert result["progress"] == 0.0
        
        # Verify background task was called
        mock_background.assert_called_once()
    
    def test_start_processing_no_files(self, client):
        """Test processing start with no files."""
        data = {
            "request_data": json.dumps({
                "mode": "merge",
                "output_formats": ["html"]
            })
        }
        
        response = client.post("/api/process", data=data)
        
        assert response.status_code == 422  # Validation error
    
    def test_start_processing_invalid_format(self, client):
        """Test processing start with invalid file format."""
        # Create temporary file with unsupported extension
        with tempfile.NamedTemporaryFile(suffix='.xyz', delete=False) as f:
            f.write(b"test content")
            temp_path = f.name
        
        try:
            with open(temp_path, 'rb') as f:
                files = [("files", ("test.xyz", f, "application/octet-stream"))]
                data = {
                    "request_data": json.dumps({
                        "mode": "merge",
                        "output_formats": ["html"]
                    })
                }
                
                response = client.post("/api/process", files=files, data=data)
            
            assert response.status_code == 400
            assert "Unsupported file format" in response.json()["detail"]
            
        finally:
            Path(temp_path).unlink()
    
    def test_start_processing_invalid_request_data(self, client, temp_text_file):
        """Test processing start with invalid request data."""
        with open(temp_text_file, 'rb') as f:
            files = [("files", ("test.txt", f, "text/plain"))]
            data = {
                "request_data": "invalid json"
            }
            
            response = client.post("/api/process", files=files, data=data)
        
        assert response.status_code == 400
        assert "Invalid request data format" in response.json()["detail"]
    
    def test_get_processing_status_not_found(self, client):
        """Test getting status for non-existent job."""
        response = client.get("/api/status/nonexistent-job-id")
        
        assert response.status_code == 404
        assert response.json()["detail"] == "Job not found"
    
    @patch('web.app.processing_jobs')
    def test_get_processing_status_success(self, mock_jobs, client):
        """Test successful status retrieval."""
        job_id = "test-job-123"
        mock_jobs.__getitem__.return_value = {
            "status": "processing",
            "progress": 0.5,
            "message": "Processing segments...",
            "result": None,
            "error": None
        }
        mock_jobs.__contains__.return_value = True
        
        response = client.get(f"/api/status/{job_id}")
        
        assert response.status_code == 200
        result = response.json()
        
        assert result["job_id"] == job_id
        assert result["status"] == "processing"
        assert result["progress"] == 0.5
        assert result["message"] == "Processing segments..."
    
    @patch('web.app.processing_jobs')
    def test_delete_job_success(self, mock_jobs, client):
        """Test successful job deletion."""
        job_id = "test-job-123"
        mock_jobs.__contains__.return_value = True
        mock_jobs.__delitem__ = Mock()
        
        with patch('pathlib.Path.exists', return_value=True), \
             patch('shutil.rmtree') as mock_rmtree:
            
            response = client.delete(f"/api/jobs/{job_id}")
        
        assert response.status_code == 200
        assert response.json()["message"] == "Job deleted successfully"
        
        # Verify cleanup was called
        mock_rmtree.assert_called_once()
        mock_jobs.__delitem__.assert_called_once_with(job_id)
    
    def test_delete_job_not_found(self, client):
        """Test deleting non-existent job."""
        response = client.delete("/api/jobs/nonexistent-job-id")
        
        assert response.status_code == 404
        assert response.json()["detail"] == "Job not found"


class TestSearchAPI:
    """Test search API endpoints."""
    
    @patch('web.app.index_connector')
    def test_search_entities_success(self, mock_connector, client):
        """Test successful entity search."""
        # Mock search results
        mock_results = [
            {
                "id": "entity-1",
                "score": 0.95,
                "source": {
                    "canonical_name": "John Doe",
                    "entity_type": "person",
                    "description": "Software engineer",
                    "alternatives": ["J. Doe"],
                    "tags": ["engineer"],
                    "relationships": []
                },
                "highlight": {
                    "canonical_name": ["<em>John</em> Doe"]
                }
            }
        ]
        
        mock_connector.search_entities.return_value = mock_results
        
        search_request = {
            "query": "John",
            "entity_types": ["person"],
            "size": 10
        }
        
        response = client.post("/api/search", json=search_request)
        
        assert response.status_code == 200
        results = response.json()
        
        assert len(results) == 1
        assert results[0]["id"] == "entity-1"
        assert results[0]["source"]["canonical_name"] == "John Doe"
        
        # Verify search was called with correct parameters
        mock_connector.search_entities.assert_called_once_with(
            query="John",
            entity_types=["person"],
            size=10
        )
    
    @patch('web.app.index_connector')
    def test_search_entities_failure(self, mock_connector, client):
        """Test search failure."""
        mock_connector.search_entities.side_effect = Exception("Search service unavailable")
        
        search_request = {
            "query": "test",
            "size": 10
        }
        
        response = client.post("/api/search", json=search_request)
        
        assert response.status_code == 500
        assert "Search failed" in response.json()["detail"]
    
    @patch('web.app.index_connector')
    def test_get_entity_success(self, mock_connector, client):
        """Test successful entity retrieval."""
        entity_id = "entity-123"
        mock_results = [
            {
                "source": {
                    "canonical_name": "John Doe",
                    "entity_type": "person",
                    "description": "Software engineer",
                    "alternatives": ["J. Doe"],
                    "tags": ["engineer"],
                    "relationships": [
                        {
                            "relation_type": "works_at",
                            "target": "Acme Corp",
                            "description": "Employment"
                        }
                    ]
                }
            }
        ]
        
        mock_connector.search_entities.return_value = mock_results
        
        response = client.get(f"/api/entity/{entity_id}")
        
        assert response.status_code == 200
        entity = response.json()
        
        assert entity["id"] == entity_id
        assert entity["canonical_name"] == "John Doe"
        assert entity["entity_type"] == "person"
        assert len(entity["relationships"]) == 1
        
        # Verify search was called with entity ID
        mock_connector.search_entities.assert_called_once_with(
            query=f"_id:{entity_id}",
            size=1
        )
    
    @patch('web.app.index_connector')
    def test_get_entity_not_found(self, mock_connector, client):
        """Test entity not found."""
        mock_connector.search_entities.return_value = []
        
        response = client.get("/api/entity/nonexistent-entity")
        
        assert response.status_code == 404
        assert response.json()["detail"] == "Entity not found"


class TestExportAPI:
    """Test export API endpoints."""
    
    def test_export_graph_json(self, client):
        """Test JSON graph export."""
        response = client.get("/api/graph/export/json")
        
        assert response.status_code == 200
        result = response.json()
        
        # Currently returns placeholder message
        assert "not yet implemented" in result["message"]
    
    def test_export_graph_rdf(self, client):
        """Test RDF graph export."""
        response = client.get("/api/graph/export/rdf")
        
        assert response.status_code == 200
        result = response.json()
        
        # Currently returns placeholder message
        assert "not yet implemented" in result["message"]
    
    def test_export_graph_unsupported_format(self, client):
        """Test export with unsupported format."""
        response = client.get("/api/graph/export/xml")
        
        assert response.status_code == 400
        assert "Unsupported export format" in response.json()["detail"]


class TestErrorHandling:
    """Test error handling."""
    
    def test_404_handler(self, client):
        """Test 404 error handler."""
        response = client.get("/nonexistent-endpoint")
        
        assert response.status_code == 404
        assert response.json()["detail"] == "Resource not found"
    
    @patch('web.app.process_documents_background')
    def test_500_handler(self, mock_background, client, temp_text_file):
        """Test 500 error handler."""
        # Force an internal server error
        mock_background.side_effect = Exception("Internal error")
        
        with open(temp_text_file, 'rb') as f:
            files = [("files", ("test.txt", f, "text/plain"))]
            data = {
                "request_data": json.dumps({
                    "mode": "merge",
                    "output_formats": ["html"]
                })
            }
            
            response = client.post("/api/process", files=files, data=data)
        
        # Should handle the error gracefully
        assert response.status_code in [500, 200]  # Depends on when error occurs


# Integration tests
@pytest.mark.integration
class TestWebIntegration:
    """Integration tests for web interface."""
    
    @patch('web.app.MainFlow')
    def test_full_processing_workflow(self, mock_flow_class, client, temp_text_file):
        """Test complete processing workflow."""
        # Mock the flow execution
        mock_flow = Mock()
        mock_flow.run_async = AsyncMock(return_value=None)
        mock_flow_class.return_value = mock_flow
        
        # Start processing
        with open(temp_text_file, 'rb') as f:
            files = [("files", ("test.txt", f, "text/plain"))]
            data = {
                "request_data": json.dumps({
                    "mode": "merge",
                    "output_formats": ["html", "json"]
                })
            }
            
            response = client.post("/api/process", files=files, data=data)
        
        assert response.status_code == 200
        job_data = response.json()
        job_id = job_data["job_id"]
        
        # Check initial status
        response = client.get(f"/api/status/{job_id}")
        assert response.status_code == 200
        
        status_data = response.json()
        assert status_data["job_id"] == job_id
        assert status_data["status"] in ["queued", "processing"]
    
    def test_page_navigation_flow(self, client):
        """Test navigation between pages."""
        # Test main navigation flow
        pages = ["/", "/upload", "/search", "/graph", "/jobs"]
        
        for page in pages:
            response = client.get(page)
            assert response.status_code == 200
            assert "text/html" in response.headers["content-type"]
    
    @patch('web.app.index_connector')
    def test_search_workflow(self, mock_connector, client):
        """Test search workflow."""
        # Mock search results
        mock_connector.search_entities.return_value = [
            {
                "id": "test-entity",
                "score": 0.9,
                "source": {
                    "canonical_name": "Test Entity",
                    "entity_type": "concept",
                    "description": "Test description",
                    "alternatives": [],
                    "tags": [],
                    "relationships": []
                }
            }
        ]
        
        # Perform search
        search_request = {
            "query": "test",
            "size": 10
        }
        
        response = client.post("/api/search", json=search_request)
        assert response.status_code == 200
        
        results = response.json()
        assert len(results) == 1
        
        # Get entity details
        entity_id = results[0]["id"]
        response = client.get(f"/api/entity/{entity_id}")
        assert response.status_code == 200
        
        entity = response.json()
        assert entity["canonical_name"] == "Test Entity"
