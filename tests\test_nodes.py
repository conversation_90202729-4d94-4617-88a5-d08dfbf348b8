"""Tests for flow nodes."""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from pathlib import Path
import tempfile

from nodes.document_nodes import ParseDocument, SplitLongContent
from nodes.entity_nodes import NormalizeEntities
from nodes.graph_nodes import BuildGraphBatch, UpdateGlobalGraph
from utils.graph_ops import Entity, Relation


class TestParseDocument:
    """Test ParseDocument node."""
    
    @pytest.fixture
    def parse_node(self):
        """Create ParseDocument node for testing."""
        return ParseDocument()
    
    @pytest.fixture
    def temp_text_file(self):
        """Create temporary text file for testing."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("This is test content.\nSecond line of content.")
            temp_path = f.name
        
        yield temp_path
        
        # Cleanup
        Path(temp_path).unlink()
    
    @pytest.mark.asyncio
    async def test_prep_async_single_file(self, parse_node, temp_text_file):
        """Test preparation with single file."""
        shared = {
            "context": {
                "input_path": temp_text_file,
                "supported_formats": ["txt", "docx", "pdf"]
            }
        }
        
        prep_res = await parse_node.prep_async(shared)
        
        assert "files_to_process" in prep_res
        assert len(prep_res["files_to_process"]) == 1
        assert str(prep_res["files_to_process"][0]) == temp_text_file
    
    @pytest.mark.asyncio
    async def test_prep_async_missing_input(self, parse_node):
        """Test preparation with missing input path."""
        shared = {"context": {}}
        
        with pytest.raises(ValueError, match="No input path specified"):
            await parse_node.prep_async(shared)
    
    @pytest.mark.asyncio
    async def test_prep_async_nonexistent_file(self, parse_node):
        """Test preparation with non-existent file."""
        shared = {
            "context": {
                "input_path": "/nonexistent/file.txt"
            }
        }
        
        with pytest.raises(FileNotFoundError):
            await parse_node.prep_async(shared)
    
    @pytest.mark.asyncio
    async def test_exec_async_success(self, parse_node, temp_text_file):
        """Test successful document parsing."""
        prep_res = {
            "files_to_process": [Path(temp_text_file)],
            "supported_formats": ["txt"]
        }
        
        with patch.object(parse_node.security_filters, 'filter_content') as mock_filter:
            mock_filter.return_value = {
                "is_safe": True,
                "filtered_text": "This is test content.\nSecond line of content.",
                "warnings": [],
                "pii_detection": Mock(has_pii=False)
            }
            
            result = await parse_node.exec_async(prep_res)
        
        assert len(result) == 1
        assert result[0]["file_type"] == "txt"
        assert "This is test content" in result[0]["raw_content"]
        assert result[0]["pii_detected"] is False
    
    @pytest.mark.asyncio
    async def test_exec_async_security_failure(self, parse_node, temp_text_file):
        """Test parsing with security filter failure."""
        prep_res = {
            "files_to_process": [Path(temp_text_file)],
            "supported_formats": ["txt"]
        }
        
        with patch.object(parse_node.security_filters, 'filter_content') as mock_filter:
            mock_filter.return_value = {
                "is_safe": False,
                "filtered_text": "",
                "warnings": ["Security check failed"],
                "pii_detection": Mock(has_pii=True)
            }
            
            result = await parse_node.exec_async(prep_res)
        
        # Should skip unsafe files
        assert len(result) == 0


class TestSplitLongContent:
    """Test SplitLongContent node."""
    
    @pytest.fixture
    def split_node(self):
        """Create SplitLongContent node for testing."""
        return SplitLongContent()
    
    def test_prep_short_content(self, split_node):
        """Test preparation with short content."""
        shared = {
            "documents": [{
                "file_path": "test.txt",
                "raw_content": "Short content"
            }]
        }
        
        prep_res = split_node.prep(shared)
        
        assert len(prep_res["documents_prep"]) == 1
        assert prep_res["documents_prep"][0]["needs_splitting"] is False
    
    def test_prep_long_content(self, split_node):
        """Test preparation with long content."""
        long_content = "This is a sentence. " * 200  # Long content
        shared = {
            "documents": [{
                "file_path": "test.txt",
                "raw_content": long_content
            }]
        }
        
        prep_res = split_node.prep(shared)
        
        assert len(prep_res["documents_prep"]) == 1
        assert prep_res["documents_prep"][0]["needs_splitting"] is True
        assert prep_res["documents_prep"][0]["chunk_size"] > 1000
    
    def test_exec_split_content(self, split_node):
        """Test content splitting execution."""
        long_content = "First paragraph.\n\nSecond paragraph.\n\nThird paragraph."
        prep_res = {
            "documents_prep": [{
                "document": {
                    "file_path": "test.txt",
                    "raw_content": long_content
                },
                "chunk_size": 30,
                "needs_splitting": True
            }]
        }
        
        result = split_node.exec(prep_res)
        
        assert len(result) == 1
        document = result[0]
        assert len(document["content_segments"]) > 1
        
        # Check segments cover entire content
        total_length = sum(
            seg["end_pos"] - seg["start_pos"] 
            for seg in document["content_segments"]
        )
        assert total_length == len(long_content)
    
    def test_exec_no_split_needed(self, split_node):
        """Test execution when no splitting is needed."""
        prep_res = {
            "documents_prep": [{
                "document": {
                    "file_path": "test.txt",
                    "raw_content": "Short content"
                },
                "chunk_size": 100,
                "needs_splitting": False
            }]
        }
        
        result = split_node.exec(prep_res)
        
        assert len(result) == 1
        document = result[0]
        assert len(document["content_segments"]) == 1
        assert document["content_segments"][0]["text"] == "Short content"


class TestNormalizeEntities:
    """Test NormalizeEntities node."""
    
    @pytest.fixture
    def normalize_node(self):
        """Create NormalizeEntities node for testing."""
        return NormalizeEntities(use_cache=False)  # Disable cache for testing
    
    @pytest.mark.asyncio
    async def test_prep_async_with_entities(self, normalize_node):
        """Test preparation with entities."""
        shared = {
            "documents": [{
                "file_path": "test.txt",
                "raw_entities": [
                    {"name": "John Doe", "type": "person", "description": "Engineer"},
                    {"name": "Jane Smith", "type": "person", "description": "Scientist"}
                ],
                "source_weight": 1
            }]
        }
        
        prep_res = await normalize_node.prep_async(shared)
        
        assert len(prep_res["all_entities"]) == 2
        assert prep_res["all_entities"][0].canonical_name == "John Doe"
        assert prep_res["all_entities"][1].canonical_name == "Jane Smith"
    
    @pytest.mark.asyncio
    async def test_prep_async_no_entities(self, normalize_node):
        """Test preparation with no entities."""
        shared = {"documents": []}
        
        with pytest.raises(ValueError, match="No documents found"):
            await normalize_node.prep_async(shared)
    
    @pytest.mark.asyncio
    async def test_exec_async_normalization(self, normalize_node):
        """Test entity normalization execution."""
        entities = [
            Entity(canonical_name="John Doe", entity_type="person"),
            Entity(canonical_name="Jon Doe", entity_type="person"),  # Similar
            Entity(canonical_name="Jane Smith", entity_type="person")  # Different
        ]
        
        prep_res = {
            "all_entities": entities,
            "entity_sources": {},
            "documents": []
        }
        
        result = await normalize_node.exec_async(prep_res)
        
        # Should normalize similar entities
        assert len(result) <= len(entities)
        
        # Should preserve different entities
        names = [entity.canonical_name for entity in result]
        assert any("Jane Smith" in name for name in names)


class TestBuildGraphBatch:
    """Test BuildGraphBatch node."""
    
    @pytest.fixture
    def build_node(self):
        """Create BuildGraphBatch node for testing."""
        return BuildGraphBatch()
    
    def test_prep_with_documents(self, build_node):
        """Test preparation with documents."""
        shared = {
            "documents": [
                {
                    "file_path": "test1.txt",
                    "normalized_entities": [
                        {"canonical_name": "John Doe", "entity_type": "person"}
                    ],
                    "relations": [
                        {"source": "John Doe", "target": "Acme Corp", "relation": "works_at"}
                    ]
                }
            ]
        }
        
        prep_res = build_node.prep(shared)
        
        assert len(prep_res) == 1
        assert prep_res[0]["file_path"] == "test1.txt"
    
    def test_prep_no_documents(self, build_node):
        """Test preparation with no documents."""
        shared = {"documents": []}
        
        with pytest.raises(ValueError, match="No documents found"):
            build_node.prep(shared)
    
    def test_exec_create_graph(self, build_node):
        """Test graph creation execution."""
        document = {
            "file_path": "test.txt",
            "normalized_entities": [
                {
                    "canonical_name": "John Doe",
                    "entity_type": "person",
                    "description": "Engineer",
                    "alternatives": [],
                    "tags": [],
                    "source_weight": 1,
                    "metadata": {}
                }
            ],
            "relations": [
                {
                    "source": "John Doe",
                    "target": "Acme Corp",
                    "relation": "works_at",
                    "description": "Employment relationship"
                }
            ],
            "source_weight": 1
        }
        
        result = build_node.exec(document)
        
        assert "knowledge_graph" in result
        graph_dict = result["knowledge_graph"]
        assert len(graph_dict["nodes"]) >= 1
        assert len(graph_dict["edges"]) >= 0  # May be 0 if target entity not found
    
    def test_exec_no_entities(self, build_node):
        """Test execution with no entities."""
        document = {
            "file_path": "test.txt",
            "normalized_entities": [],
            "relations": []
        }
        
        result = build_node.exec(document)
        
        assert result["knowledge_graph"] is None


class TestUpdateGlobalGraph:
    """Test UpdateGlobalGraph node."""
    
    @pytest.fixture
    def update_node(self):
        """Create UpdateGlobalGraph node for testing."""
        return UpdateGlobalGraph()
    
    @pytest.mark.asyncio
    async def test_prep_async_with_documents(self, update_node):
        """Test preparation with documents."""
        shared = {
            "documents": [{
                "file_path": "test.txt",
                "normalized_entities": [
                    {"canonical_name": "John Doe", "entity_type": "person"}
                ],
                "relations": []
            }]
        }
        
        prep_res = await update_node.prep_async(shared)
        
        assert "documents" in prep_res
        assert prep_res["existing_graph"] is None  # No existing graph
    
    @pytest.mark.asyncio
    async def test_prep_async_no_documents(self, update_node):
        """Test preparation with no documents."""
        shared = {"documents": []}
        
        with pytest.raises(ValueError, match="No documents found"):
            await update_node.prep_async(shared)
    
    @pytest.mark.asyncio
    async def test_exec_async_create_global_graph(self, update_node):
        """Test global graph creation."""
        prep_res = {
            "documents": [{
                "file_path": "test.txt",
                "normalized_entities": [
                    {
                        "canonical_name": "John Doe",
                        "entity_type": "person",
                        "description": "Engineer",
                        "alternatives": [],
                        "tags": [],
                        "source_weight": 1,
                        "metadata": {}
                    }
                ],
                "relations": [],
                "source_weight": 1
            }],
            "existing_graph": None
        }
        
        result = await update_node.exec_async(prep_res)
        
        assert result.number_of_nodes() >= 1
        assert result.number_of_edges() >= 0


# Integration tests
@pytest.mark.asyncio
async def test_document_processing_integration():
    """Integration test for document processing flow."""
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write("John Doe works at Acme Corp. Jane Smith is a data scientist.")
        temp_path = f.name
    
    try:
        # Test parse document
        parse_node = ParseDocument()
        shared = {
            "context": {
                "input_path": temp_path,
                "supported_formats": ["txt"]
            }
        }
        
        prep_res = await parse_node.prep_async(shared)
        documents = await parse_node.exec_async(prep_res)
        
        assert len(documents) == 1
        assert "John Doe" in documents[0]["raw_content"]
        
        # Test split content
        split_node = SplitLongContent()
        shared["documents"] = documents
        
        prep_res = split_node.prep(shared)
        documents = split_node.exec(prep_res)
        
        assert len(documents[0]["content_segments"]) >= 1
        
    finally:
        Path(temp_path).unlink()


@pytest.mark.asyncio
async def test_graph_building_integration():
    """Integration test for graph building."""
    # Mock documents with entities and relations
    documents = [{
        "file_path": "test.txt",
        "normalized_entities": [
            {
                "canonical_name": "John Doe",
                "entity_type": "person",
                "description": "Software engineer",
                "alternatives": [],
                "tags": ["engineer"],
                "source_weight": 1,
                "metadata": {}
            },
            {
                "canonical_name": "Acme Corp",
                "entity_type": "organization",
                "description": "Technology company",
                "alternatives": [],
                "tags": ["company"],
                "source_weight": 1,
                "metadata": {}
            }
        ],
        "relations": [
            {
                "source": "John Doe",
                "target": "Acme Corp",
                "relation": "works_at",
                "description": "Employment relationship"
            }
        ],
        "source_weight": 1
    }]
    
    # Test batch graph building
    build_node = BuildGraphBatch()
    prep_res = build_node.prep({"documents": documents})
    result = build_node.exec(prep_res)
    
    assert len(result) == 1
    assert result[0]["knowledge_graph"] is not None
    
    # Test global graph update
    update_node = UpdateGlobalGraph()
    shared = {"documents": documents}
    prep_res = await update_node.prep_async(shared)
    graph = await update_node.exec_async(prep_res)
    
    assert graph.number_of_nodes() == 2
    assert graph.number_of_edges() == 1
