"""
Main flow for knowledge graph building
Orchestrates the entire process from file input to final output
"""

import asyncio
from pathlib import Path
from typing import Dict, List, Any, Optional
from loguru import logger

from .base_flow import Base<PERSON>low, Parallel<PERSON>low, SequentialFlow
from .file_processing_flow import FileProcessing<PERSON>low
from .graph_build_flow import Graph<PERSON><PERSON><PERSON><PERSON>
from .post_processing_flow import PostProcessing<PERSON><PERSON>
from config.settings import Settings


class MainFlow(BaseFlow):
    """Main orchestration flow for knowledge graph building"""
    
    def __init__(self, shared_context: Dict[str, Any], settings: Settings):
        super().__init__(shared_context, "MainFlow")
        self.settings = settings
        
        # Validate shared context
        self._validate_context()
    
    def _validate_context(self):
        """Validate shared context structure"""
        required_keys = ['context', 'documents', 'knowledge_graph', 'outputs', 'task_state']
        
        for key in required_keys:
            if key not in self.shared_context:
                raise ValueError(f"Missing required key in shared context: {key}")
        
        # Validate context sub-structure
        context = self.shared_context['context']
        required_context_keys = ['input_files', 'processing_mode', 'output_path']
        
        for key in required_context_keys:
            if key not in context:
                raise ValueError(f"Missing required key in context: {key}")
    
    async def execute(self) -> Dict[str, Any]:
        """Execute main flow"""
        
        logger.info("Starting main knowledge graph building flow")
        
        # Get input files
        input_files = self.shared_context['context']['input_files']
        processing_mode = self.shared_context['context']['processing_mode']
        max_parallel = self.shared_context['context'].get('max_parallel', 3)
        
        logger.info(f"Processing {len(input_files)} files in {processing_mode} mode")
        
        # Phase 1: File Processing
        await self._phase_1_file_processing(input_files, max_parallel)
        
        # Phase 2: Graph Building
        await self._phase_2_graph_building(processing_mode)
        
        # Phase 3: Post Processing
        await self._phase_3_post_processing()
        
        # Collect final results
        results = {
            'total_files_processed': len(self.shared_context['task_state']['completed_files']),
            'total_entities': len(self.shared_context['knowledge_graph']['nodes']),
            'total_relations': len(self.shared_context['knowledge_graph']['edges']),
            'output_files': self.shared_context['outputs'],
            'processing_mode': processing_mode,
            'failed_files': len(self.shared_context['task_state']['failed_segments'])
        }
        
        logger.success(f"Main flow completed: {results}")
        return results
    
    async def _phase_1_file_processing(self, input_files: List[Path], max_parallel: int):
        """Phase 1: Process all input files"""
        
        logger.info(f"Phase 1: Processing {len(input_files)} files")
        
        # Create file processing flows
        file_flows = []
        for i, file_path in enumerate(input_files):
            flow = FileProcessingFlow(
                shared_context=self.shared_context,
                file_path=file_path,
                file_index=i,
                settings=self.settings,
                flow_id=f"FileProcessing_{i}_{file_path.name}"
            )
            file_flows.append(flow)
        
        # Execute file processing flows in parallel
        parallel_flow = ParallelFlow(
            shared_context=self.shared_context,
            flows=file_flows,
            flow_id="FileProcessingParallel",
            max_concurrent=max_parallel
        )
        
        file_results = await parallel_flow.run()
        
        # Check results
        successful_files = 0
        failed_files = 0
        
        for i, result in enumerate(file_results.result):
            if result.is_success:
                successful_files += 1
                self.shared_context['task_state']['completed_files'].add(str(input_files[i]))
            else:
                failed_files += 1
                logger.error(f"File processing failed: {input_files[i]} - {result.error}")
        
        logger.info(f"Phase 1 completed: {successful_files} successful, {failed_files} failed")
        
        if successful_files == 0:
            raise RuntimeError("No files were processed successfully")
    
    async def _phase_2_graph_building(self, processing_mode: str):
        """Phase 2: Build knowledge graph"""
        
        logger.info(f"Phase 2: Building knowledge graph in {processing_mode} mode")
        
        # Create graph building flow
        graph_flow = GraphBuildFlow(
            shared_context=self.shared_context,
            processing_mode=processing_mode,
            settings=self.settings,
            flow_id="GraphBuilding"
        )
        
        graph_result = await graph_flow.run()
        
        if not graph_result.is_success:
            raise RuntimeError(f"Graph building failed: {graph_result.error}")
        
        # Update graph state
        self.shared_context['knowledge_graph']['index_ready'] = True
        
        logger.info(f"Phase 2 completed: {len(self.shared_context['knowledge_graph']['nodes'])} nodes, "
                   f"{len(self.shared_context['knowledge_graph']['edges'])} edges")
    
    async def _phase_3_post_processing(self):
        """Phase 3: Post processing and output generation"""
        
        logger.info("Phase 3: Post processing and output generation")
        
        # Create post processing flow
        post_flow = PostProcessingFlow(
            shared_context=self.shared_context,
            settings=self.settings,
            flow_id="PostProcessing"
        )
        
        post_result = await post_flow.run()
        
        if not post_result.is_success:
            raise RuntimeError(f"Post processing failed: {post_result.error}")
        
        logger.info("Phase 3 completed: All outputs generated")


class MainFlowBuilder:
    """Builder class for creating configured main flows"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
    
    def create_shared_context(self, input_files: List[Path], 
                            processing_mode: str = "merge",
                            output_path: Optional[Path] = None,
                            output_formats: Optional[List[str]] = None) -> Dict[str, Any]:
        """Create initial shared context"""
        
        if output_path is None:
            output_path = Path("output")
        
        if output_formats is None:
            output_formats = ["json", "html"]
        
        return {
            "context": {
                "input_files": input_files,
                "processing_mode": processing_mode,
                "output_path": output_path,
                "output_formats": output_formats,
                "max_parallel": self.settings.processing.max_concurrent_files,
                "supported_formats": self.settings.files.supported_formats,
                "model_router": self._create_model_router(),
            },
            "documents": [],
            "knowledge_graph": {
                "nodes": {},
                "edges": [],
                "index_ready": False
            },
            "outputs": {
                "wiki_pages": {},
                "json_graph": "",
                "search_index": ""
            },
            "task_state": {
                "completed_files": set(),
                "failed_segments": {},
                "retry_count": 0
            }
        }
    
    def _create_model_router(self):
        """Create model routing function"""
        def route_model(index: int) -> str:
            """Route model based on index for load balancing"""
            if index % 2 == 0:
                return self.settings.llm.models.get('primary', 'gpt-3.5-turbo')
            else:
                return self.settings.llm.models.get('secondary', 'gpt-3.5-turbo')
        
        return route_model
    
    def create_main_flow(self, input_files: List[Path], 
                        processing_mode: str = "merge",
                        output_path: Optional[Path] = None,
                        output_formats: Optional[List[str]] = None) -> MainFlow:
        """Create configured main flow"""
        
        shared_context = self.create_shared_context(
            input_files=input_files,
            processing_mode=processing_mode,
            output_path=output_path,
            output_formats=output_formats
        )
        
        return MainFlow(shared_context, self.settings)


async def run_knowledge_graph_builder(input_files: List[Path], 
                                    settings: Settings,
                                    processing_mode: str = "merge",
                                    output_path: Optional[Path] = None,
                                    output_formats: Optional[List[str]] = None) -> Dict[str, Any]:
    """Convenience function to run the complete knowledge graph building process"""
    
    builder = MainFlowBuilder(settings)
    main_flow = builder.create_main_flow(
        input_files=input_files,
        processing_mode=processing_mode,
        output_path=output_path,
        output_formats=output_formats
    )
    
    result = await main_flow.run()
    
    if not result.is_success:
        raise RuntimeError(f"Knowledge graph building failed: {result.error}")
    
    return result.result


# Example usage
async def example_usage():
    """Example of how to use the main flow"""
    
    from config.settings import Settings
    
    # Initialize settings
    settings = Settings()
    
    # Input files
    input_files = [
        Path("data/document1.pdf"),
        Path("data/document2.txt"),
        Path("data/document3.docx")
    ]
    
    try:
        # Run knowledge graph building
        results = await run_knowledge_graph_builder(
            input_files=input_files,
            settings=settings,
            processing_mode="merge",
            output_path=Path("output"),
            output_formats=["json", "html", "rdf"]
        )
        
        print(f"Successfully processed {results['total_files_processed']} files")
        print(f"Generated {results['total_entities']} entities and {results['total_relations']} relations")
        print(f"Output files: {results['output_files']}")
        
    except Exception as e:
        logger.error(f"Knowledge graph building failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(example_usage())
