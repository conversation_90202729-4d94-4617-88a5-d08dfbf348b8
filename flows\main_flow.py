"""Main flow orchestrating the entire knowledge graph building process."""

from typing import Dict, Any

from pocketflow import As<PERSON><PERSON>low
from loguru import logger

from .file_processing_flow import FileProcessing<PERSON>low
from .graph_build_flow import GraphBuild<PERSON>low
from .post_processing_flow import PostProcessingFlow


class MainFlow(AsyncFlow):
    """Top-level async controller managing the entire knowledge graph building pipeline."""
    
    def __init__(self):
        super().__init__()
        
        # Create sub-flows
        self.file_processing_flow = FileProcessingFlow()
        self.graph_build_flow = GraphBuildFlow()
        self.post_processing_flow = PostProcessingFlow()
        
        # Set up flow connections
        self._setup_flow()
    
    def _setup_flow(self):
        """Set up the flow connections between sub-flows."""
        
        # Start with file processing
        self.start(self.file_processing_flow)
        
        # Connect file processing to graph building
        self.file_processing_flow >> self.graph_build_flow
        
        # Connect graph building to post-processing
        self.graph_build_flow >> self.post_processing_flow
    
    async def prep_async(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare the main flow execution.
        
        Args:
            shared: Shared data structure
            
        Returns:
            Preparation data
        """
        logger.info("Starting Knowledge Graph Builder main flow")
        
        # Validate shared data structure
        if "context" not in shared:
            raise ValueError("Missing 'context' in shared data")
        
        context = shared["context"]
        
        # Validate required context fields
        required_fields = ["input_path", "processing_mode", "supported_formats"]
        for field in required_fields:
            if field not in context:
                raise ValueError(f"Missing required context field: {field}")
        
        # Initialize shared data structure if needed
        if "documents" not in shared:
            shared["documents"] = []
        
        if "knowledge_graph" not in shared:
            shared["knowledge_graph"] = {
                "nodes": {},
                "edges": [],
                "index_ready": False
            }
        
        if "outputs" not in shared:
            shared["outputs"] = {
                "wiki_pages": {},
                "json_graph": "",
                "search_index": ""
            }
        
        if "task_state" not in shared:
            shared["task_state"] = {
                "completed_files": set(),
                "failed_segments": {},
                "retry_count": 0
            }
        
        # Log configuration
        logger.info(f"Input path: {context['input_path']}")
        logger.info(f"Processing mode: {context['processing_mode']}")
        logger.info(f"Supported formats: {context['supported_formats']}")
        
        return {
            "start_time": logger._core.clock(),
            "context": context
        }
    
    async def post_async(
        self,
        shared: Dict[str, Any],
        prep_res: Dict[str, Any],
        exec_res: Any
    ) -> str:
        """
        Post-process the main flow execution.
        
        Args:
            shared: Shared data structure
            prep_res: Preparation results
            exec_res: Execution results
            
        Returns:
            Final result status
        """
        # Calculate execution time
        start_time = prep_res.get("start_time", 0)
        end_time = logger._core.clock()
        execution_time = end_time - start_time
        
        # Generate execution summary
        documents = shared.get("documents", [])
        knowledge_graph = shared.get("knowledge_graph", {})
        outputs = shared.get("outputs", {})
        task_state = shared.get("task_state", {})
        
        summary = {
            "execution_time": execution_time,
            "documents_processed": len(documents),
            "entities_created": len(knowledge_graph.get("nodes", {})),
            "relations_created": len(knowledge_graph.get("edges", [])),
            "wiki_pages_generated": len(outputs.get("wiki_pages", {})),
            "output_formats": list(outputs.keys()),
            "completed_files": len(task_state.get("completed_files", set())),
            "failed_segments": len(task_state.get("failed_segments", {})),
            "success": True
        }
        
        # Store summary in shared data
        shared["execution_summary"] = summary
        
        # Log completion
        logger.info("Knowledge Graph Builder main flow completed successfully")
        logger.info(f"Execution time: {execution_time:.2f} seconds")
        logger.info(f"Documents processed: {summary['documents_processed']}")
        logger.info(f"Entities created: {summary['entities_created']}")
        logger.info(f"Relations created: {summary['relations_created']}")
        logger.info(f"Wiki pages generated: {summary['wiki_pages_generated']}")
        
        return "completed"


class MainFlowWithErrorHandling(MainFlow):
    """Main flow with enhanced error handling and recovery."""
    
    async def prep_async(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """Enhanced preparation with error handling."""
        try:
            return await super().prep_async(shared)
        except Exception as e:
            logger.error(f"Main flow preparation failed: {e}")
            
            # Initialize minimal shared structure for error handling
            shared.setdefault("task_state", {})["preparation_error"] = str(e)
            
            raise
    
    async def post_async(
        self,
        shared: Dict[str, Any],
        prep_res: Dict[str, Any],
        exec_res: Any
    ) -> str:
        """Enhanced post-processing with error handling."""
        try:
            # Check for errors in task state
            task_state = shared.get("task_state", {})
            
            if task_state.get("preparation_error"):
                logger.error("Flow failed during preparation")
                return "failed_preparation"
            
            if task_state.get("failed_segments"):
                failed_count = sum(len(segments) for segments in task_state["failed_segments"].values())
                total_segments = sum(len(doc.get("content_segments", [])) for doc in shared.get("documents", []))
                
                if failed_count > total_segments * 0.5:  # More than 50% failed
                    logger.error(f"Too many failed segments: {failed_count}/{total_segments}")
                    return "failed_processing"
            
            # Continue with normal post-processing
            result = await super().post_async(shared, prep_res, exec_res)
            
            # Additional validation
            outputs = shared.get("outputs", {})
            if not any(outputs.values()):
                logger.warning("No outputs were generated")
                return "completed_with_warnings"
            
            return result
            
        except Exception as e:
            logger.error(f"Main flow post-processing failed: {e}")
            
            # Create error summary
            shared["execution_summary"] = {
                "execution_time": 0,
                "documents_processed": len(shared.get("documents", [])),
                "entities_created": 0,
                "relations_created": 0,
                "wiki_pages_generated": 0,
                "output_formats": [],
                "completed_files": 0,
                "failed_segments": len(shared.get("task_state", {}).get("failed_segments", {})),
                "success": False,
                "error": str(e)
            }
            
            return "failed_post_processing"


class MainFlowFactory:
    """Factory for creating main flow instances with different configurations."""
    
    @staticmethod
    def create_standard_flow() -> MainFlow:
        """Create standard main flow."""
        return MainFlow()
    
    @staticmethod
    def create_robust_flow() -> MainFlowWithErrorHandling:
        """Create main flow with enhanced error handling."""
        return MainFlowWithErrorHandling()
    
    @staticmethod
    def create_custom_flow(
        enable_error_handling: bool = True,
        custom_file_processing: bool = False,
        custom_graph_building: bool = False
    ) -> MainFlow:
        """
        Create custom main flow with specified features.
        
        Args:
            enable_error_handling: Whether to enable enhanced error handling
            custom_file_processing: Whether to use custom file processing
            custom_graph_building: Whether to use custom graph building
            
        Returns:
            Configured main flow instance
        """
        if enable_error_handling:
            flow = MainFlowWithErrorHandling()
        else:
            flow = MainFlow()
        
        # TODO: Add custom flow modifications based on parameters
        
        return flow
