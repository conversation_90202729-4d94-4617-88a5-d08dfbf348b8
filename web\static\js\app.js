/**
 * Knowledge Graph Builder - Main JavaScript Application
 */

class KnowledgeGraphApp {
    constructor() {
        this.currentGraph = null;
        this.selectedNode = null;
        this.searchResults = [];
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadInitialData();
    }

    setupEventListeners() {
        // Global search functionality
        const searchInput = document.getElementById('global-search');
        if (searchInput) {
            searchInput.addEventListener('input', this.debounce(this.handleGlobalSearch.bind(this), 300));
        }

        // Theme toggle
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', this.toggleTheme.bind(this));
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', this.handleKeyboardShortcuts.bind(this));
    }

    async loadInitialData() {
        try {
            const status = await this.fetchAPI('/api/status');
            this.updateStatus(status);

            if (status.has_graph) {
                await this.loadGraphStats();
            }
        } catch (error) {
            console.error('Failed to load initial data:', error);
        }
    }

    async fetchAPI(url, options = {}) {
        const response = await fetch(url, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        });

        if (!response.ok) {
            throw new Error(`API request failed: ${response.statusText}`);
        }

        return await response.json();
    }

    async loadGraphStats() {
        try {
            const stats = await this.fetchAPI('/api/graph/stats');
            this.updateStatsDisplay(stats);
        } catch (error) {
            console.error('Failed to load graph stats:', error);
        }
    }

    updateStatsDisplay(stats) {
        const elements = {
            'total-entities': stats.total_entities,
            'total-relations': stats.total_relations,
            'entity-types': Object.keys(stats.entity_types).length,
            'relation-types': Object.keys(stats.relation_types).length
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                this.animateNumber(element, value);
            }
        });
    }

    animateNumber(element, targetValue) {
        const startValue = parseInt(element.textContent) || 0;
        const duration = 1000;
        const startTime = performance.now();

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            const currentValue = Math.floor(startValue + (targetValue - startValue) * progress);
            element.textContent = currentValue.toLocaleString();

            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        requestAnimationFrame(animate);
    }

    updateStatus(status) {
        const statusIndicator = document.getElementById('status-indicator');
        const statusText = document.getElementById('status-text');

        if (!statusIndicator || !statusText) return;

        const statusConfig = {
            'idle': { icon: 'fas fa-circle text-success', text: '就绪' },
            'processing': { icon: 'fas fa-spinner fa-spin text-warning', text: '处理中...' },
            'completed': { icon: 'fas fa-check-circle text-success', text: '完成' },
            'failed': { icon: 'fas fa-exclamation-circle text-danger', text: '失败' }
        };

        const config = statusConfig[status.status] || statusConfig['idle'];
        statusIndicator.innerHTML = `<i class="${config.icon} me-1"></i>`;
        statusText.textContent = config.text;
    }

    async handleGlobalSearch(event) {
        const query = event.target.value.trim();
        
        if (query.length < 2) {
            this.clearSearchResults();
            return;
        }

        try {
            this.showSearchLoading();
            const results = await this.searchEntities(query);
            this.displaySearchResults(results);
        } catch (error) {
            console.error('Search failed:', error);
            this.showSearchError('搜索失败，请稍后重试');
        }
    }

    async searchEntities(query, options = {}) {
        const searchRequest = {
            query: query,
            limit: options.limit || 10,
            entity_types: options.entity_types || null
        };

        return await this.fetchAPI('/api/search/entities', {
            method: 'POST',
            body: JSON.stringify(searchRequest)
        });
    }

    displaySearchResults(results) {
        const container = document.getElementById('search-results');
        if (!container) return;

        container.innerHTML = '';

        if (results.results.length === 0) {
            container.innerHTML = '<div class="text-muted text-center py-3">未找到相关结果</div>';
            return;
        }

        results.results.forEach(result => {
            const resultElement = this.createSearchResultElement(result);
            container.appendChild(resultElement);
        });
    }

    createSearchResultElement(result) {
        const div = document.createElement('div');
        div.className = 'search-result fade-in';
        div.onclick = () => this.selectSearchResult(result);

        div.innerHTML = `
            <div class="search-result-title">${this.escapeHtml(result.name)}</div>
            <span class="search-result-type">${this.escapeHtml(result.type)}</span>
            <div class="search-result-description">${this.escapeHtml(result.description || '暂无描述')}</div>
            ${result.score ? `<div class="search-result-score">相关度: ${result.score.toFixed(2)}</div>` : ''}
        `;

        return div;
    }

    selectSearchResult(result) {
        // Navigate to entity detail or highlight in graph
        if (window.location.pathname === '/graph') {
            this.highlightNodeInGraph(result.id);
        } else {
            window.location.href = `/entity/${result.id}`;
        }
    }

    highlightNodeInGraph(nodeId) {
        // This will be implemented in the graph visualization
        if (window.graphVisualization) {
            window.graphVisualization.highlightNode(nodeId);
        }
    }

    showSearchLoading() {
        const container = document.getElementById('search-results');
        if (container) {
            container.innerHTML = '<div class="text-center py-3"><div class="loading-spinner"></div> 搜索中...</div>';
        }
    }

    showSearchError(message) {
        const container = document.getElementById('search-results');
        if (container) {
            container.innerHTML = `<div class="text-danger text-center py-3">${message}</div>`;
        }
    }

    clearSearchResults() {
        const container = document.getElementById('search-results');
        if (container) {
            container.innerHTML = '';
        }
    }

    toggleTheme() {
        const body = document.body;
        const isDark = body.classList.contains('dark-theme');
        
        if (isDark) {
            body.classList.remove('dark-theme');
            localStorage.setItem('theme', 'light');
        } else {
            body.classList.add('dark-theme');
            localStorage.setItem('theme', 'dark');
        }
    }

    handleKeyboardShortcuts(event) {
        // Ctrl/Cmd + K for search
        if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
            event.preventDefault();
            const searchInput = document.getElementById('global-search');
            if (searchInput) {
                searchInput.focus();
            }
        }

        // Escape to clear search
        if (event.key === 'Escape') {
            const searchInput = document.getElementById('global-search');
            if (searchInput && document.activeElement === searchInput) {
                searchInput.value = '';
                this.clearSearchResults();
                searchInput.blur();
            }
        }
    }

    // Utility functions
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    // API helper methods
    async uploadFiles(files, options = {}) {
        const formData = new FormData();
        
        files.forEach(file => {
            formData.append('files', file);
        });
        
        formData.append('mode', options.mode || 'merge');
        formData.append('output_formats', (options.formats || ['json', 'html']).join(','));

        const response = await fetch('/api/upload', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || 'Upload failed');
        }

        return await response.json();
    }

    async getEntityDetails(entityId) {
        return await this.fetchAPI(`/api/entity/${entityId}`);
    }

    async getGraphData(limit = 100) {
        return await this.fetchAPI(`/api/graph/data?limit=${limit}`);
    }
}

// Graph Visualization Class
class GraphVisualization {
    constructor(containerId, options = {}) {
        this.container = d3.select(`#${containerId}`);
        this.width = options.width || 800;
        this.height = options.height || 600;
        this.nodes = [];
        this.links = [];
        this.simulation = null;
        this.selectedNode = null;
        
        this.init();
    }

    init() {
        // Create SVG
        this.svg = this.container.append('svg')
            .attr('width', this.width)
            .attr('height', this.height);

        // Create groups for different elements
        this.linkGroup = this.svg.append('g').attr('class', 'links');
        this.nodeGroup = this.svg.append('g').attr('class', 'nodes');
        this.labelGroup = this.svg.append('g').attr('class', 'labels');

        // Setup zoom
        const zoom = d3.zoom()
            .scaleExtent([0.1, 3])
            .on('zoom', (event) => {
                this.svg.selectAll('g').attr('transform', event.transform);
            });

        this.svg.call(zoom);

        // Setup simulation
        this.simulation = d3.forceSimulation()
            .force('link', d3.forceLink().id(d => d.id).distance(100))
            .force('charge', d3.forceManyBody().strength(-300))
            .force('center', d3.forceCenter(this.width / 2, this.height / 2));
    }

    async loadData(limit = 100) {
        try {
            const data = await window.app.getGraphData(limit);
            this.updateGraph(data.nodes, data.edges);
        } catch (error) {
            console.error('Failed to load graph data:', error);
        }
    }

    updateGraph(nodes, links) {
        this.nodes = nodes;
        this.links = links;

        this.updateLinks();
        this.updateNodes();
        this.updateLabels();

        // Restart simulation
        this.simulation.nodes(this.nodes);
        this.simulation.force('link').links(this.links);
        this.simulation.alpha(1).restart();
    }

    updateLinks() {
        const link = this.linkGroup.selectAll('.link')
            .data(this.links, d => `${d.source.id || d.source}-${d.target.id || d.target}`);

        link.exit().remove();

        link.enter().append('line')
            .attr('class', 'link')
            .merge(link)
            .attr('stroke-width', d => Math.sqrt(d.weight || 1));
    }

    updateNodes() {
        const node = this.nodeGroup.selectAll('.node')
            .data(this.nodes, d => d.id);

        node.exit().remove();

        const nodeEnter = node.enter().append('circle')
            .attr('class', 'node')
            .attr('r', 8)
            .attr('fill', d => this.getNodeColor(d.type))
            .call(this.drag())
            .on('click', (event, d) => this.selectNode(d))
            .on('mouseover', (event, d) => this.showNodeTooltip(event, d))
            .on('mouseout', () => this.hideNodeTooltip());

        nodeEnter.merge(node);

        this.simulation.on('tick', () => {
            this.linkGroup.selectAll('.link')
                .attr('x1', d => d.source.x)
                .attr('y1', d => d.source.y)
                .attr('x2', d => d.target.x)
                .attr('y2', d => d.target.y);

            this.nodeGroup.selectAll('.node')
                .attr('cx', d => d.x)
                .attr('cy', d => d.y);

            this.labelGroup.selectAll('.node-label')
                .attr('x', d => d.x)
                .attr('y', d => d.y + 20);
        });
    }

    updateLabels() {
        const label = this.labelGroup.selectAll('.node-label')
            .data(this.nodes, d => d.id);

        label.exit().remove();

        label.enter().append('text')
            .attr('class', 'node-label')
            .merge(label)
            .text(d => d.name.length > 15 ? d.name.substring(0, 15) + '...' : d.name);
    }

    getNodeColor(type) {
        const colors = {
            'person': '#ff6b6b',
            'organization': '#4ecdc4',
            'location': '#45b7d1',
            'concept': '#96ceb4',
            'event': '#feca57',
            'product': '#ff9ff3',
            'unknown': '#95a5a6'
        };
        return colors[type] || colors['unknown'];
    }

    selectNode(node) {
        // Clear previous selection
        this.nodeGroup.selectAll('.node').classed('selected', false);
        
        // Select new node
        this.selectedNode = node;
        this.nodeGroup.selectAll('.node')
            .filter(d => d.id === node.id)
            .classed('selected', true);

        // Show node details
        this.showNodeDetails(node);
    }

    highlightNode(nodeId) {
        const node = this.nodes.find(n => n.id === nodeId);
        if (node) {
            this.selectNode(node);
            
            // Center on node
            const transform = d3.zoomIdentity
                .translate(this.width / 2 - node.x, this.height / 2 - node.y)
                .scale(1.5);
            
            this.svg.transition().duration(750)
                .call(d3.zoom().transform, transform);
        }
    }

    showNodeDetails(node) {
        // This would show node details in a sidebar or modal
        console.log('Selected node:', node);
    }

    showNodeTooltip(event, node) {
        // Create tooltip
        const tooltip = d3.select('body').append('div')
            .attr('class', 'tooltip')
            .style('opacity', 0)
            .style('position', 'absolute')
            .style('background', 'rgba(0,0,0,0.8)')
            .style('color', 'white')
            .style('padding', '10px')
            .style('border-radius', '5px')
            .style('pointer-events', 'none');

        tooltip.transition().duration(200).style('opacity', 1);
        
        tooltip.html(`
            <strong>${node.name}</strong><br/>
            Type: ${node.type}<br/>
            ${node.description ? `Description: ${node.description.substring(0, 100)}...` : ''}
        `)
        .style('left', (event.pageX + 10) + 'px')
        .style('top', (event.pageY - 10) + 'px');
    }

    hideNodeTooltip() {
        d3.selectAll('.tooltip').remove();
    }

    drag() {
        return d3.drag()
            .on('start', (event, d) => {
                if (!event.active) this.simulation.alphaTarget(0.3).restart();
                d.fx = d.x;
                d.fy = d.y;
            })
            .on('drag', (event, d) => {
                d.fx = event.x;
                d.fy = event.y;
            })
            .on('end', (event, d) => {
                if (!event.active) this.simulation.alphaTarget(0);
                d.fx = null;
                d.fy = null;
            });
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new KnowledgeGraphApp();
    
    // Initialize graph visualization if on graph page
    if (document.getElementById('graph-container')) {
        window.graphVisualization = new GraphVisualization('graph-container', {
            width: document.getElementById('graph-container').clientWidth,
            height: 600
        });
        window.graphVisualization.loadData();
    }
});
