"""
File processing flow for individual documents
Handles parsing, segmentation, and entity extraction for a single file
"""

import asyncio
from pathlib import Path
from typing import Dict, List, Any, Optional
from loguru import logger

from .base_flow import BaseFlow, SequentialFlow, BatchFlow
from .segment_processing_flow import SegmentProcessingBatch<PERSON>low
from utils.file_utils import FileProcessor, ContentSplitter
from utils.graph_ops import GraphOperations
from utils.security_filters import SecurityFilters
from config.settings import Settings


class FileProcessingFlow(BaseFlow):
    """Flow for processing a single file"""
    
    def __init__(self, shared_context: Dict[str, Any], 
                 file_path: Path, 
                 file_index: int,
                 settings: Settings,
                 flow_id: Optional[str] = None):
        super().__init__(shared_context, flow_id or f"FileProcessing_{file_path.name}")
        self.file_path = file_path
        self.file_index = file_index
        self.settings = settings
        
        # Initialize processors
        self.file_processor = FileProcessor(
            max_file_size_mb=settings.files.max_file_size_mb,
            encoding=settings.files.encoding
        )
        self.content_splitter = ContentSplitter(
            chunk_size=settings.processing.chunk_size,
            chunk_overlap=settings.processing.chunk_overlap
        )
        self.graph_ops = GraphOperations(
            similarity_threshold=settings.processing.entity_similarity_threshold,
            merge_strategy=settings.graph.merge_strategy
        )
        self.security_filters = SecurityFilters(
            enable_pii_detection=settings.security.enable_pii_detection,
            enable_xss_protection=settings.security.enable_xss_protection,
            max_input_size=settings.security.max_input_size
        )
    
    async def execute(self) -> Dict[str, Any]:
        """Execute file processing flow"""
        
        logger.info(f"Processing file: {self.file_path}")
        
        # Step 1: Parse document
        document_data = await self._parse_document()
        
        # Step 2: Security validation and cleaning
        document_data = await self._validate_and_clean_content(document_data)
        
        # Step 3: Split content into segments
        segments = await self._split_content(document_data)
        
        # Step 4: Process segments with LLM
        extraction_results = await self._process_segments(segments)
        
        # Step 5: Merge segment results
        merged_results = await self._merge_segment_results(extraction_results)
        
        # Step 6: Normalize entities
        normalized_entities = await self._normalize_entities(merged_results)
        
        # Step 7: Store document data
        document_result = await self._store_document_data(
            document_data, segments, normalized_entities
        )
        
        logger.info(f"File processing completed: {self.file_path} - "
                   f"{len(normalized_entities['entities'])} entities, "
                   f"{len(normalized_entities['relations'])} relations")
        
        return document_result
    
    async def _parse_document(self) -> Dict[str, Any]:
        """Parse document and extract content"""
        
        try:
            parsed_data = await self.file_processor.parse_file(self.file_path)
            
            # Validate content length
            content = parsed_data['content']
            if len(content) < self.settings.files.min_content_length:
                raise ValueError(f"Content too short: {len(content)} characters")
            
            if len(content) > self.settings.files.max_content_length:
                logger.warning(f"Content very long, truncating: {len(content)} characters")
                content = content[:self.settings.files.max_content_length]
                parsed_data['content'] = content
            
            logger.debug(f"Parsed document: {len(content)} characters")
            return parsed_data
            
        except Exception as e:
            logger.error(f"Document parsing failed: {self.file_path} - {e}")
            raise
    
    async def _validate_and_clean_content(self, document_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and clean document content"""
        
        content = document_data['content']
        
        # Validate content
        validation_result = self.security_filters.validate_content(content)
        
        if not validation_result['is_valid']:
            logger.warning(f"Content validation issues: {validation_result['issues']}")
        
        # Clean content if needed
        if validation_result['pii_detected'] or validation_result['xss_detected']:
            cleaned_content, cleaning_report = self.security_filters.clean_content(
                content, 
                redact_pii=True, 
                sanitize_html=True
            )
            
            document_data['content'] = cleaned_content
            document_data['metadata']['security_cleaning'] = cleaning_report
            
            logger.info(f"Content cleaned: PII redacted={len(cleaning_report['pii_redacted'])}, "
                       f"XSS removed={cleaning_report['xss_removed']}")
        
        return document_data
    
    async def _split_content(self, document_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Split content into semantic segments"""
        
        content = document_data['content']
        
        # Split content
        segments = self.content_splitter.split_by_semantic_boundaries(content)
        
        # Add metadata to segments
        for segment in segments:
            segment['file_path'] = str(self.file_path)
            segment['file_type'] = document_data['metadata']['file_type']
            segment['source_weight'] = self.settings.graph.source_weights.get(
                document_data['metadata']['file_type'], 1
            )
        
        logger.debug(f"Split content into {len(segments)} segments")
        return segments
    
    async def _process_segments(self, segments: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Process segments with LLM for entity extraction"""
        
        if not segments:
            return []
        
        # Create segment processing flow
        segment_flow = SegmentProcessingBatchFlow(
            shared_context=self.shared_context,
            segments=segments,
            settings=self.settings,
            flow_id=f"SegmentProcessing_{self.file_index}"
        )
        
        # Execute segment processing
        result = await segment_flow.run()
        
        if not result.is_success:
            logger.error(f"Segment processing failed: {result.error}")
            # Return empty results for failed segments
            return [{"entities": [], "relations": []} for _ in segments]
        
        return result.result
    
    async def _merge_segment_results(self, extraction_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Merge results from all segments"""
        
        all_entities = []
        all_relations = []
        
        for result in extraction_results:
            if isinstance(result, dict):
                all_entities.extend(result.get('entities', []))
                all_relations.extend(result.get('relations', []))
        
        logger.debug(f"Merged results: {len(all_entities)} entities, {len(all_relations)} relations")
        
        return {
            'entities': all_entities,
            'relations': all_relations
        }
    
    async def _normalize_entities(self, merged_results: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize and deduplicate entities"""
        
        entities = merged_results['entities']
        relations = merged_results['relations']
        
        # Get source weight for this file
        source_weight = self.settings.graph.source_weights.get(
            self.file_processor.detect_format(self.file_path), 1
        )
        
        # Normalize entities
        normalized_entities = self.graph_ops.normalize_entities(entities, source_weight)
        
        # Create entity map for relation normalization
        entity_map = {entity.canonical_name: entity for entity in normalized_entities}
        
        # Normalize relations
        normalized_relations = self.graph_ops.normalize_relations(relations, entity_map)
        
        logger.debug(f"Normalized: {len(normalized_entities)} entities, {len(normalized_relations)} relations")
        
        return {
            'entities': normalized_entities,
            'relations': normalized_relations
        }
    
    async def _store_document_data(self, document_data: Dict[str, Any], 
                                 segments: List[Dict[str, Any]],
                                 normalized_results: Dict[str, Any]) -> Dict[str, Any]:
        """Store document data in shared context"""
        
        # Create document entry
        document_entry = {
            "file_path": str(self.file_path),
            "file_type": document_data['metadata']['file_type'],
            "source_weight": self.settings.graph.source_weights.get(
                document_data['metadata']['file_type'], 1
            ),
            "content_segments": segments,
            "raw_entities": [],  # Could store raw extraction results if needed
            "relations": [rel.__dict__ for rel in normalized_results['relations']],
            "normalized_entities": [entity.__dict__ for entity in normalized_results['entities']],
            "metadata": document_data['metadata']
        }
        
        # Add to shared context
        self.shared_context['documents'].append(document_entry)
        
        # Update task state
        self.shared_context['task_state']['completed_files'].add(str(self.file_path))
        
        return {
            'file_path': str(self.file_path),
            'entities_count': len(normalized_results['entities']),
            'relations_count': len(normalized_results['relations']),
            'segments_count': len(segments),
            'processing_successful': True
        }


class FileProcessingBatchFlow(BatchFlow):
    """Batch flow for processing multiple files"""
    
    def __init__(self, shared_context: Dict[str, Any],
                 file_paths: List[Path],
                 settings: Settings,
                 batch_size: int = 5,
                 flow_id: Optional[str] = None):
        
        async def process_file_batch(file_batch: List[Path], context: Dict[str, Any]) -> List[Dict[str, Any]]:
            """Process a batch of files"""
            
            results = []
            
            for i, file_path in enumerate(file_batch):
                try:
                    file_flow = FileProcessingFlow(
                        shared_context=context,
                        file_path=file_path,
                        file_index=i,
                        settings=settings
                    )
                    
                    result = await file_flow.run()
                    
                    if result.is_success:
                        results.append(result.result)
                    else:
                        logger.error(f"File processing failed: {file_path} - {result.error}")
                        results.append({
                            'file_path': str(file_path),
                            'processing_successful': False,
                            'error': str(result.error)
                        })
                        
                except Exception as e:
                    logger.error(f"File processing exception: {file_path} - {e}")
                    results.append({
                        'file_path': str(file_path),
                        'processing_successful': False,
                        'error': str(e)
                    })
            
            return results
        
        super().__init__(
            shared_context=shared_context,
            items=file_paths,
            batch_processor=process_file_batch,
            batch_size=batch_size,
            flow_id=flow_id or "FileProcessingBatch",
            max_concurrent=settings.processing.max_concurrent_files
        )
