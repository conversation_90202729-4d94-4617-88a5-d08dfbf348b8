"""File processing flow for document parsing and content segmentation."""

from typing import Dict, Any

from pocketflow import As<PERSON><PERSON>low
from loguru import logger

from nodes.document_nodes import ParseDocument, SplitLongContent
from .segment_processing_flow import SegmentProcessingBatchFlow
from .entity_normalization_flow import EntityNormalizationFlow


class FileProcessingFlow(AsyncFlow):
    """Async flow per input file with segment batching and entity processing."""
    
    def __init__(self):
        super().__init__()
        
        # Create nodes
        self.parse_document = ParseDocument()
        self.split_content = SplitLongContent()
        self.segment_processing_flow = SegmentProcessingBatchFlow()
        self.entity_normalization_flow = EntityNormalizationFlow()
        
        # Set up flow
        self._setup_flow()
    
    def _setup_flow(self):
        """Set up the flow connections between nodes."""
        
        # Start with document parsing
        self.start(self.parse_document)
        
        # Connect parsing to content splitting
        self.parse_document >> self.split_content
        
        # Connect content splitting to segment processing
        # Use conditional transitions based on whether segmentation is needed
        self.split_content - "needs_parallel_processing" >> self.segment_processing_flow
        self.split_content - "default" >> self.segment_processing_flow
        
        # Connect segment processing to entity normalization
        self.segment_processing_flow >> self.entity_normalization_flow
    
    async def prep_async(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare file processing flow.
        
        Args:
            shared: Shared data structure
            
        Returns:
            Preparation data
        """
        logger.info("Starting file processing flow")
        
        context = shared.get("context", {})
        input_path = context.get("input_path")
        
        if not input_path:
            raise ValueError("No input path specified for file processing")
        
        return {
            "input_path": input_path,
            "processing_start_time": logger._core.clock()
        }
    
    async def post_async(
        self,
        shared: Dict[str, Any],
        prep_res: Dict[str, Any],
        exec_res: Any
    ) -> str:
        """
        Post-process file processing results.
        
        Args:
            shared: Shared data structure
            prep_res: Preparation results
            exec_res: Execution results
            
        Returns:
            Next action
        """
        processing_time = logger._core.clock() - prep_res["processing_start_time"]
        
        # Validate processing results
        documents = shared.get("documents", [])
        
        if not documents:
            logger.error("No documents were processed successfully")
            return "failed"
        
        # Check for normalized entities
        total_entities = sum(len(doc.get("normalized_entities", [])) for doc in documents)
        total_relations = sum(len(doc.get("relations", [])) for doc in documents)
        
        logger.info(f"File processing completed in {processing_time:.2f} seconds")
        logger.info(f"Processed {len(documents)} documents")
        logger.info(f"Extracted {total_entities} entities and {total_relations} relations")
        
        # Update task state
        task_state = shared.setdefault("task_state", {})
        task_state["file_processing_completed"] = True
        task_state["processing_time"] = processing_time
        
        return "default"


class BatchFileProcessingFlow(AsyncFlow):
    """File processing flow optimized for batch processing of multiple files."""
    
    def __init__(self, batch_size: int = 5):
        super().__init__()
        self.batch_size = batch_size
        
        # Create nodes with batch optimization
        self.parse_document = ParseDocument(max_retries=2, wait=5)
        self.split_content = SplitLongContent()
        self.segment_processing_flow = SegmentProcessingBatchFlow()
        self.entity_normalization_flow = EntityNormalizationFlow()
        
        # Set up flow
        self._setup_flow()
    
    def _setup_flow(self):
        """Set up the flow for batch processing."""
        
        # Start with document parsing
        self.start(self.parse_document)
        
        # Connect to content splitting
        self.parse_document >> self.split_content
        
        # Connect to segment processing with batch optimization
        self.split_content >> self.segment_processing_flow
        
        # Connect to entity normalization
        self.segment_processing_flow >> self.entity_normalization_flow
    
    async def prep_async(self, shared: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Prepare batch file processing.
        
        Args:
            shared: Shared data structure
            
        Returns:
            List of batch processing parameters
        """
        logger.info(f"Starting batch file processing with batch size {self.batch_size}")
        
        context = shared.get("context", {})
        
        # Create batch parameters
        batch_params = []
        
        # For now, create a single batch (can be extended for multiple batches)
        batch_params.append({
            "batch_id": 0,
            "batch_size": self.batch_size,
            "context": context
        })
        
        return batch_params
    
    async def post_async(
        self,
        shared: Dict[str, Any],
        prep_res: List[Dict[str, Any]],
        exec_res: Any
    ) -> str:
        """
        Post-process batch file processing results.
        
        Args:
            shared: Shared data structure
            prep_res: Preparation results
            exec_res: Execution results
            
        Returns:
            Next action
        """
        logger.info("Batch file processing completed")
        
        # Validate results
        documents = shared.get("documents", [])
        
        if not documents:
            logger.error("Batch processing failed - no documents processed")
            return "failed"
        
        # Calculate statistics
        total_segments = sum(len(doc.get("content_segments", [])) for doc in documents)
        total_entities = sum(len(doc.get("normalized_entities", [])) for doc in documents)
        total_relations = sum(len(doc.get("relations", [])) for doc in documents)
        
        logger.info(f"Batch processing results:")
        logger.info(f"  Documents: {len(documents)}")
        logger.info(f"  Segments: {total_segments}")
        logger.info(f"  Entities: {total_entities}")
        logger.info(f"  Relations: {total_relations}")
        
        return "default"


class FileProcessingFlowFactory:
    """Factory for creating file processing flows with different configurations."""
    
    @staticmethod
    def create_standard_flow() -> FileProcessingFlow:
        """Create standard file processing flow."""
        return FileProcessingFlow()
    
    @staticmethod
    def create_batch_flow(batch_size: int = 5) -> BatchFileProcessingFlow:
        """Create batch file processing flow."""
        return BatchFileProcessingFlow(batch_size=batch_size)
    
    @staticmethod
    def create_optimized_flow(
        max_retries: int = 3,
        enable_parallel_segments: bool = True,
        enable_entity_enhancement: bool = True
    ) -> FileProcessingFlow:
        """
        Create optimized file processing flow.
        
        Args:
            max_retries: Maximum retry attempts for failed operations
            enable_parallel_segments: Whether to enable parallel segment processing
            enable_entity_enhancement: Whether to enable entity enhancement
            
        Returns:
            Configured file processing flow
        """
        flow = FileProcessingFlow()
        
        # Configure nodes with optimization parameters
        flow.parse_document.max_retries = max_retries
        
        if enable_parallel_segments:
            # Segment processing is already parallel by default
            pass
        
        if not enable_entity_enhancement:
            # TODO: Add option to skip entity enhancement
            pass
        
        return flow
