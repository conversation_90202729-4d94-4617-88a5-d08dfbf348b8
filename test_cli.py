#!/usr/bin/env python3
"""Simple test script for the CLI functionality."""

import asyncio
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from core.pipeline import Pipeline
from config.settings import Settings

async def test_processing():
    """Test the processing pipeline."""
    print("🧪 Testing Knowledge Graph Builder Pipeline...")
    
    # Initialize
    settings = Settings()
    pipeline = Pipeline()
    
    # Test with sample documents
    input_path = "test_documents"
    output_path = "output"
    mode = "merge"
    
    print(f"📁 Input: {input_path}")
    print(f"📁 Output: {output_path}")
    print(f"⚙️ Mode: {mode}")
    
    try:
        # Run processing
        result = await pipeline.process(input_path, output_path, mode)
        
        print("\n✅ Processing completed successfully!")
        
        # Display results
        print("\n📊 Results:")
        stats = result.get("processing_stats", {})
        print(f"  Documents processed: {stats.get('documents_processed', 0)}")
        print(f"  Entities extracted: {stats.get('entities_extracted', 0)}")
        print(f"  Relations extracted: {stats.get('relations_extracted', 0)}")
        print(f"  Processing time: {stats.get('processing_time', 0):.2f}s")
        
        # Graph statistics
        graph_stats = result.get("statistics", {})
        if graph_stats:
            print(f"  Total entities: {graph_stats.get('total_entities', 0)}")
            print(f"  Total relations: {graph_stats.get('total_relations', 0)}")
            
            # Entity types
            entity_types = graph_stats.get("entity_types", {})
            if entity_types:
                print("  Entity types:")
                for entity_type, count in entity_types.items():
                    print(f"    {entity_type}: {count}")
        
        return True
        
    except Exception as e:
        print(f"❌ Processing failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    success = asyncio.run(test_processing())
    
    if success:
        print("\n🎉 Test completed successfully!")
        return 0
    else:
        print("\n💥 Test failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
