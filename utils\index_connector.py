"""Index connector for search integration with Elasticsearch and other systems."""

import json
from typing import Dict, List, Any, Optional, Union
from datetime import datetime

import networkx as nx
from elasticsearch import Elasticsearch
from elasticsearch.helpers import bulk
from loguru import logger

from config.settings import Settings
from .graph_ops import Entity, Relation


class IndexConnector:
    """Connector for integrating knowledge graphs with search indices."""
    
    def __init__(self, settings: Optional[Settings] = None):
        self.settings = settings or Settings()
        self.es_client: Optional[Elasticsearch] = None
    
    def connect_elasticsearch(self) -> bool:
        """
        Connect to Elasticsearch cluster.
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            self.es_client = Elasticsearch(
                hosts=self.settings.elasticsearch_hosts_list,
                timeout=30,
                max_retries=3,
                retry_on_timeout=True
            )
            
            # Test connection
            if self.es_client.ping():
                logger.info("Connected to Elasticsearch successfully")
                return True
            else:
                logger.error("Failed to ping Elasticsearch")
                return False
                
        except Exception as e:
            logger.error(f"Failed to connect to Elasticsearch: {e}")
            return False
    
    def create_entity_index_mapping(self) -> Dict[str, Any]:
        """
        Create Elasticsearch mapping for entity index.
        
        Returns:
            Mapping configuration
        """
        return {
            "mappings": {
                "properties": {
                    "canonical_name": {
                        "type": "text",
                        "analyzer": "standard",
                        "fields": {
                            "keyword": {
                                "type": "keyword"
                            }
                        }
                    },
                    "entity_type": {
                        "type": "keyword"
                    },
                    "description": {
                        "type": "text",
                        "analyzer": "standard"
                    },
                    "alternatives": {
                        "type": "text",
                        "analyzer": "standard"
                    },
                    "tags": {
                        "type": "keyword"
                    },
                    "source_weight": {
                        "type": "integer"
                    },
                    "relationships": {
                        "type": "nested",
                        "properties": {
                            "target": {
                                "type": "keyword"
                            },
                            "relation_type": {
                                "type": "keyword"
                            },
                            "description": {
                                "type": "text"
                            }
                        }
                    },
                    "created_at": {
                        "type": "date"
                    },
                    "updated_at": {
                        "type": "date"
                    }
                }
            },
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0,
                "analysis": {
                    "analyzer": {
                        "entity_analyzer": {
                            "type": "custom",
                            "tokenizer": "standard",
                            "filter": ["lowercase", "stop"]
                        }
                    }
                }
            }
        }
    
    def setup_elasticsearch_index(self, index_name: Optional[str] = None) -> bool:
        """
        Setup Elasticsearch index with proper mapping.
        
        Args:
            index_name: Name of the index (uses default if None)
            
        Returns:
            True if setup successful, False otherwise
        """
        if not self.es_client:
            if not self.connect_elasticsearch():
                return False
        
        index_name = index_name or self.settings.elasticsearch_index
        
        try:
            # Delete index if it exists
            if self.es_client.indices.exists(index=index_name):
                self.es_client.indices.delete(index=index_name)
                logger.info(f"Deleted existing index: {index_name}")
            
            # Create index with mapping
            mapping = self.create_entity_index_mapping()
            self.es_client.indices.create(index=index_name, body=mapping)
            logger.info(f"Created Elasticsearch index: {index_name}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to setup Elasticsearch index: {e}")
            return False
    
    def format_entity_for_elasticsearch(
        self,
        entity: Entity,
        entity_id: str,
        relationships: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Format entity for Elasticsearch indexing.
        
        Args:
            entity: Entity to format
            entity_id: Entity ID
            relationships: List of relationships
            
        Returns:
            Formatted document for Elasticsearch
        """
        now = datetime.utcnow().isoformat()
        
        return {
            "_id": entity_id,
            "canonical_name": entity.canonical_name,
            "entity_type": entity.entity_type,
            "description": entity.description,
            "alternatives": entity.alternatives,
            "tags": entity.tags,
            "source_weight": entity.source_weight,
            "relationships": relationships,
            "created_at": now,
            "updated_at": now,
            "metadata": entity.metadata
        }
    
    def index_knowledge_graph_to_elasticsearch(
        self,
        graph: nx.MultiDiGraph,
        index_name: Optional[str] = None,
        batch_size: int = 100
    ) -> bool:
        """
        Index entire knowledge graph to Elasticsearch.
        
        Args:
            graph: Knowledge graph to index
            index_name: Name of the index
            batch_size: Batch size for bulk indexing
            
        Returns:
            True if indexing successful, False otherwise
        """
        if not self.es_client:
            if not self.connect_elasticsearch():
                return False
        
        index_name = index_name or self.settings.elasticsearch_index
        
        try:
            # Setup index
            if not self.setup_elasticsearch_index(index_name):
                return False
            
            # Prepare documents for bulk indexing
            documents = []
            
            for node_id, node_data in graph.nodes(data=True):
                entity = Entity.from_dict(node_data)
                
                # Get relationships for this entity
                relationships = []
                for source, target, edge_data in graph.edges(data=True):
                    if source == node_id:
                        target_data = graph.nodes[target]
                        target_entity = Entity.from_dict(target_data)
                        relation = Relation.from_dict(edge_data)
                        
                        relationships.append({
                            "target": target_entity.canonical_name,
                            "target_id": target,
                            "relation_type": relation.relation_type,
                            "description": relation.description,
                            "confidence": relation.confidence
                        })
                
                # Format document
                doc = self.format_entity_for_elasticsearch(entity, node_id, relationships)
                
                documents.append({
                    "_index": index_name,
                    "_id": node_id,
                    "_source": doc
                })
                
                # Bulk index when batch size is reached
                if len(documents) >= batch_size:
                    success, failed = bulk(self.es_client, documents)
                    logger.debug(f"Indexed batch: {success} successful, {len(failed)} failed")
                    documents = []
            
            # Index remaining documents
            if documents:
                success, failed = bulk(self.es_client, documents)
                logger.debug(f"Indexed final batch: {success} successful, {len(failed)} failed")
            
            # Refresh index
            self.es_client.indices.refresh(index=index_name)
            
            logger.info(f"Successfully indexed knowledge graph to Elasticsearch: {index_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to index knowledge graph to Elasticsearch: {e}")
            return False
    
    def search_entities(
        self,
        query: str,
        entity_types: Optional[List[str]] = None,
        size: int = 10,
        index_name: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Search entities in Elasticsearch.
        
        Args:
            query: Search query
            entity_types: Filter by entity types
            size: Number of results to return
            index_name: Name of the index
            
        Returns:
            List of search results
        """
        if not self.es_client:
            if not self.connect_elasticsearch():
                return []
        
        index_name = index_name or self.settings.elasticsearch_index
        
        try:
            # Build search query
            search_body = {
                "query": {
                    "bool": {
                        "should": [
                            {
                                "multi_match": {
                                    "query": query,
                                    "fields": ["canonical_name^3", "description^2", "alternatives"],
                                    "type": "best_fields",
                                    "fuzziness": "AUTO"
                                }
                            },
                            {
                                "nested": {
                                    "path": "relationships",
                                    "query": {
                                        "multi_match": {
                                            "query": query,
                                            "fields": ["relationships.target", "relationships.description"]
                                        }
                                    }
                                }
                            }
                        ],
                        "minimum_should_match": 1
                    }
                },
                "size": size,
                "highlight": {
                    "fields": {
                        "canonical_name": {},
                        "description": {},
                        "alternatives": {}
                    }
                }
            }
            
            # Add entity type filter if specified
            if entity_types:
                search_body["query"]["bool"]["filter"] = [
                    {"terms": {"entity_type": entity_types}}
                ]
            
            # Execute search
            response = self.es_client.search(index=index_name, body=search_body)
            
            # Format results
            results = []
            for hit in response["hits"]["hits"]:
                result = {
                    "id": hit["_id"],
                    "score": hit["_score"],
                    "source": hit["_source"],
                    "highlight": hit.get("highlight", {})
                }
                results.append(result)
            
            logger.debug(f"Search query '{query}' returned {len(results)} results")
            return results
            
        except Exception as e:
            logger.error(f"Failed to search entities: {e}")
            return []
    
    def export_to_json(self, graph: nx.MultiDiGraph) -> str:
        """
        Export knowledge graph to JSON format.
        
        Args:
            graph: Knowledge graph to export
            
        Returns:
            JSON string representation
        """
        export_data = {
            "metadata": {
                "export_time": datetime.utcnow().isoformat(),
                "node_count": graph.number_of_nodes(),
                "edge_count": graph.number_of_edges(),
                "format_version": "1.0"
            },
            "entities": [],
            "relations": []
        }
        
        # Export entities
        for node_id, node_data in graph.nodes(data=True):
            entity = Entity.from_dict(node_data)
            export_data["entities"].append({
                "id": node_id,
                **entity.to_dict()
            })
        
        # Export relations
        for source, target, edge_data in graph.edges(data=True):
            relation = Relation.from_dict(edge_data)
            export_data["relations"].append({
                "source_id": source,
                "target_id": target,
                **relation.to_dict()
            })
        
        return json.dumps(export_data, indent=2, ensure_ascii=False)
    
    def export_to_rdf(self, graph: nx.MultiDiGraph, format: str = "turtle") -> str:
        """
        Export knowledge graph to RDF format.
        
        Args:
            graph: Knowledge graph to export
            format: RDF format (turtle, xml, n3, etc.)
            
        Returns:
            RDF string representation
        """
        try:
            from rdflib import Graph, Namespace, URIRef, Literal, RDF, RDFS
            
            # Create RDF graph
            rdf_graph = Graph()
            
            # Define namespaces
            KG = Namespace("http://example.org/kg/")
            rdf_graph.bind("kg", KG)
            
            # Add entities
            for node_id, node_data in graph.nodes(data=True):
                entity = Entity.from_dict(node_data)
                entity_uri = KG[node_id]
                
                rdf_graph.add((entity_uri, RDF.type, KG.Entity))
                rdf_graph.add((entity_uri, RDFS.label, Literal(entity.canonical_name)))
                rdf_graph.add((entity_uri, KG.entityType, Literal(entity.entity_type)))
                
                if entity.description:
                    rdf_graph.add((entity_uri, RDFS.comment, Literal(entity.description)))
                
                for alt in entity.alternatives:
                    rdf_graph.add((entity_uri, KG.alternativeName, Literal(alt)))
                
                for tag in entity.tags:
                    rdf_graph.add((entity_uri, KG.tag, Literal(tag)))
            
            # Add relations
            for source, target, edge_data in graph.edges(data=True):
                relation = Relation.from_dict(edge_data)
                source_uri = KG[source]
                target_uri = KG[target]
                
                # Create relation property
                relation_prop = KG[relation.relation_type.replace(" ", "_")]
                rdf_graph.add((source_uri, relation_prop, target_uri))
            
            return rdf_graph.serialize(format=format)
            
        except ImportError:
            logger.error("rdflib not available, cannot export to RDF")
            return ""
        except Exception as e:
            logger.error(f"Failed to export to RDF: {e}")
            return ""
