"""
Search index integration for Elasticsearch and other search backends
"""

import json
import asyncio
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import networkx as nx
from loguru import logger

try:
    from elasticsearch import AsyncElasticsearch
    from elasticsearch.helpers import async_bulk
    ELASTICSEARCH_AVAILABLE = True
except ImportError:
    ELASTICSEARCH_AVAILABLE = False
    logger.warning("Elasticsearch not available. Install with: pip install elasticsearch")


class SearchIndexConnector:
    """Base class for search index connectors"""
    
    def __init__(self, index_name: str = "knowledge_graph"):
        self.index_name = index_name
    
    async def index_graph(self, graph: nx.DiGraph) -> bool:
        """Index knowledge graph"""
        raise NotImplementedError
    
    async def search_entities(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search entities"""
        raise NotImplementedError
    
    async def search_relations(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search relations"""
        raise NotImplementedError
    
    def close(self):
        """Close connection"""
        pass


class ElasticsearchConnector(SearchIndexConnector):
    """Elasticsearch search index connector"""
    
    def __init__(self, hosts: List[str], index_name: str = "knowledge_graph",
                 username: Optional[str] = None, password: Optional[str] = None):
        
        if not ELASTICSEARCH_AVAILABLE:
            raise ImportError("Elasticsearch not available. Install with: pip install elasticsearch")
        
        super().__init__(index_name)
        
        self.hosts = hosts
        self.client = AsyncElasticsearch(
            hosts=hosts,
            basic_auth=(username, password) if username and password else None,
            verify_certs=False,
            ssl_show_warn=False
        )
        
        # Index mappings
        self.entity_mapping = {
            "mappings": {
                "properties": {
                    "id": {"type": "keyword"},
                    "name": {
                        "type": "text",
                        "analyzer": "standard",
                        "fields": {
                            "keyword": {"type": "keyword"},
                            "suggest": {
                                "type": "completion",
                                "analyzer": "simple"
                            }
                        }
                    },
                    "canonical_name": {"type": "keyword"},
                    "type": {"type": "keyword"},
                    "description": {
                        "type": "text",
                        "analyzer": "standard"
                    },
                    "tags": {"type": "keyword"},
                    "alternatives": {"type": "keyword"},
                    "confidence": {"type": "float"},
                    "source_weight": {"type": "integer"},
                    "created_at": {"type": "date"},
                    "updated_at": {"type": "date"}
                }
            }
        }
        
        self.relation_mapping = {
            "mappings": {
                "properties": {
                    "id": {"type": "keyword"},
                    "source_id": {"type": "keyword"},
                    "target_id": {"type": "keyword"},
                    "source_name": {"type": "keyword"},
                    "target_name": {"type": "keyword"},
                    "relation_type": {"type": "keyword"},
                    "description": {
                        "type": "text",
                        "analyzer": "standard"
                    },
                    "confidence": {"type": "float"},
                    "weight": {"type": "float"},
                    "created_at": {"type": "date"},
                    "updated_at": {"type": "date"}
                }
            }
        }
    
    async def _create_indices(self):
        """Create Elasticsearch indices"""
        
        entity_index = f"{self.index_name}_entities"
        relation_index = f"{self.index_name}_relations"
        
        try:
            # Create entity index
            if not await self.client.indices.exists(index=entity_index):
                await self.client.indices.create(
                    index=entity_index,
                    body=self.entity_mapping
                )
                logger.info(f"Created entity index: {entity_index}")
            
            # Create relation index
            if not await self.client.indices.exists(index=relation_index):
                await self.client.indices.create(
                    index=relation_index,
                    body=self.relation_mapping
                )
                logger.info(f"Created relation index: {relation_index}")
                
        except Exception as e:
            logger.error(f"Failed to create indices: {e}")
            raise
    
    async def index_graph(self, graph: nx.DiGraph) -> bool:
        """Index knowledge graph in Elasticsearch"""
        
        try:
            await self._create_indices()
            
            # Index entities
            entity_docs = []
            for node_id, node_data in graph.nodes(data=True):
                doc = {
                    "_index": f"{self.index_name}_entities",
                    "_id": node_id,
                    "_source": {
                        "id": node_id,
                        "name": node_data.get('name', ''),
                        "canonical_name": node_data.get('canonical_name', ''),
                        "type": node_data.get('type', 'unknown'),
                        "description": node_data.get('description', ''),
                        "tags": node_data.get('tags', []),
                        "alternatives": node_data.get('alternatives', []),
                        "confidence": node_data.get('confidence', 1.0),
                        "source_weight": node_data.get('source_weight', 1),
                        "created_at": datetime.now().isoformat(),
                        "updated_at": datetime.now().isoformat()
                    }
                }
                entity_docs.append(doc)
            
            # Bulk index entities
            if entity_docs:
                success, failed = await async_bulk(self.client, entity_docs)
                logger.info(f"Indexed {success} entities, {len(failed)} failed")
            
            # Index relations
            relation_docs = []
            for source, target, edge_data in graph.edges(data=True):
                source_name = graph.nodes[source].get('name', source)
                target_name = graph.nodes[target].get('name', target)
                
                relation_id = f"{source}_{target}_{edge_data.get('relation_type', '')}"
                
                doc = {
                    "_index": f"{self.index_name}_relations",
                    "_id": relation_id,
                    "_source": {
                        "id": relation_id,
                        "source_id": source,
                        "target_id": target,
                        "source_name": source_name,
                        "target_name": target_name,
                        "relation_type": edge_data.get('relation_type', ''),
                        "description": edge_data.get('description', ''),
                        "confidence": edge_data.get('confidence', 1.0),
                        "weight": edge_data.get('weight', 1.0),
                        "created_at": datetime.now().isoformat(),
                        "updated_at": datetime.now().isoformat()
                    }
                }
                relation_docs.append(doc)
            
            # Bulk index relations
            if relation_docs:
                success, failed = await async_bulk(self.client, relation_docs)
                logger.info(f"Indexed {success} relations, {len(failed)} failed")
            
            # Refresh indices
            await self.client.indices.refresh(index=f"{self.index_name}_*")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to index graph: {e}")
            return False
    
    async def search_entities(self, query: str, limit: int = 10, 
                            entity_types: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """Search entities in Elasticsearch"""
        
        try:
            search_body = {
                "query": {
                    "bool": {
                        "should": [
                            {
                                "multi_match": {
                                    "query": query,
                                    "fields": ["name^3", "description^2", "tags", "alternatives"],
                                    "type": "best_fields",
                                    "fuzziness": "AUTO"
                                }
                            },
                            {
                                "match_phrase_prefix": {
                                    "name": {
                                        "query": query,
                                        "boost": 2
                                    }
                                }
                            }
                        ],
                        "minimum_should_match": 1
                    }
                },
                "size": limit,
                "sort": [
                    {"_score": {"order": "desc"}},
                    {"confidence": {"order": "desc"}}
                ]
            }
            
            # Add entity type filter if specified
            if entity_types:
                search_body["query"]["bool"]["filter"] = [
                    {"terms": {"type": entity_types}}
                ]
            
            response = await self.client.search(
                index=f"{self.index_name}_entities",
                body=search_body
            )
            
            results = []
            for hit in response['hits']['hits']:
                result = hit['_source']
                result['score'] = hit['_score']
                results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"Entity search failed: {e}")
            return []
    
    async def search_relations(self, query: str, limit: int = 10,
                             relation_types: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """Search relations in Elasticsearch"""
        
        try:
            search_body = {
                "query": {
                    "bool": {
                        "should": [
                            {
                                "multi_match": {
                                    "query": query,
                                    "fields": ["source_name^2", "target_name^2", "relation_type^3", "description"],
                                    "type": "best_fields",
                                    "fuzziness": "AUTO"
                                }
                            }
                        ],
                        "minimum_should_match": 1
                    }
                },
                "size": limit,
                "sort": [
                    {"_score": {"order": "desc"}},
                    {"confidence": {"order": "desc"}}
                ]
            }
            
            # Add relation type filter if specified
            if relation_types:
                search_body["query"]["bool"]["filter"] = [
                    {"terms": {"relation_type": relation_types}}
                ]
            
            response = await self.client.search(
                index=f"{self.index_name}_relations",
                body=search_body
            )
            
            results = []
            for hit in response['hits']['hits']:
                result = hit['_source']
                result['score'] = hit['_score']
                results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"Relation search failed: {e}")
            return []
    
    async def get_entity_suggestions(self, prefix: str, limit: int = 5) -> List[str]:
        """Get entity name suggestions"""
        
        try:
            search_body = {
                "suggest": {
                    "entity_suggest": {
                        "prefix": prefix,
                        "completion": {
                            "field": "name.suggest",
                            "size": limit
                        }
                    }
                }
            }
            
            response = await self.client.search(
                index=f"{self.index_name}_entities",
                body=search_body
            )
            
            suggestions = []
            for option in response['suggest']['entity_suggest'][0]['options']:
                suggestions.append(option['text'])
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Suggestion search failed: {e}")
            return []
    
    async def get_graph_statistics(self) -> Dict[str, Any]:
        """Get graph statistics from indices"""
        
        try:
            # Entity statistics
            entity_stats = await self.client.search(
                index=f"{self.index_name}_entities",
                body={
                    "size": 0,
                    "aggs": {
                        "types": {
                            "terms": {"field": "type", "size": 100}
                        },
                        "avg_confidence": {
                            "avg": {"field": "confidence"}
                        }
                    }
                }
            )
            
            # Relation statistics
            relation_stats = await self.client.search(
                index=f"{self.index_name}_relations",
                body={
                    "size": 0,
                    "aggs": {
                        "types": {
                            "terms": {"field": "relation_type", "size": 100}
                        },
                        "avg_confidence": {
                            "avg": {"field": "confidence"}
                        }
                    }
                }
            )
            
            return {
                "total_entities": entity_stats['hits']['total']['value'],
                "total_relations": relation_stats['hits']['total']['value'],
                "entity_types": {
                    bucket['key']: bucket['doc_count'] 
                    for bucket in entity_stats['aggregations']['types']['buckets']
                },
                "relation_types": {
                    bucket['key']: bucket['doc_count'] 
                    for bucket in relation_stats['aggregations']['types']['buckets']
                },
                "avg_entity_confidence": entity_stats['aggregations']['avg_confidence']['value'],
                "avg_relation_confidence": relation_stats['aggregations']['avg_confidence']['value']
            }
            
        except Exception as e:
            logger.error(f"Failed to get statistics: {e}")
            return {}
    
    async def close(self):
        """Close Elasticsearch connection"""
        await self.client.close()


class SimpleIndexConnector(SearchIndexConnector):
    """Simple in-memory search index for testing"""
    
    def __init__(self, index_name: str = "knowledge_graph"):
        super().__init__(index_name)
        self.entities = {}
        self.relations = {}
    
    async def index_graph(self, graph: nx.DiGraph) -> bool:
        """Index graph in memory"""
        
        try:
            # Index entities
            for node_id, node_data in graph.nodes(data=True):
                self.entities[node_id] = {
                    "id": node_id,
                    "name": node_data.get('name', ''),
                    "type": node_data.get('type', 'unknown'),
                    "description": node_data.get('description', ''),
                    "tags": node_data.get('tags', []),
                    "alternatives": node_data.get('alternatives', []),
                    "confidence": node_data.get('confidence', 1.0)
                }
            
            # Index relations
            for source, target, edge_data in graph.edges(data=True):
                relation_id = f"{source}_{target}_{edge_data.get('relation_type', '')}"
                self.relations[relation_id] = {
                    "id": relation_id,
                    "source_id": source,
                    "target_id": target,
                    "source_name": graph.nodes[source].get('name', source),
                    "target_name": graph.nodes[target].get('name', target),
                    "relation_type": edge_data.get('relation_type', ''),
                    "description": edge_data.get('description', ''),
                    "confidence": edge_data.get('confidence', 1.0)
                }
            
            logger.info(f"Indexed {len(self.entities)} entities and {len(self.relations)} relations in memory")
            return True
            
        except Exception as e:
            logger.error(f"Failed to index graph: {e}")
            return False
    
    async def search_entities(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Simple text search in entities"""
        
        query_lower = query.lower()
        results = []
        
        for entity in self.entities.values():
            score = 0
            
            # Name match
            if query_lower in entity['name'].lower():
                score += 3
            
            # Description match
            if query_lower in entity['description'].lower():
                score += 2
            
            # Tag match
            for tag in entity['tags']:
                if query_lower in tag.lower():
                    score += 1
            
            # Alternative name match
            for alt in entity['alternatives']:
                if query_lower in alt.lower():
                    score += 2
            
            if score > 0:
                entity_copy = entity.copy()
                entity_copy['score'] = score
                results.append(entity_copy)
        
        # Sort by score and confidence
        results.sort(key=lambda x: (x['score'], x['confidence']), reverse=True)
        
        return results[:limit]
    
    async def search_relations(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Simple text search in relations"""
        
        query_lower = query.lower()
        results = []
        
        for relation in self.relations.values():
            score = 0
            
            # Source/target name match
            if query_lower in relation['source_name'].lower():
                score += 2
            if query_lower in relation['target_name'].lower():
                score += 2
            
            # Relation type match
            if query_lower in relation['relation_type'].lower():
                score += 3
            
            # Description match
            if query_lower in relation['description'].lower():
                score += 1
            
            if score > 0:
                relation_copy = relation.copy()
                relation_copy['score'] = score
                results.append(relation_copy)
        
        # Sort by score and confidence
        results.sort(key=lambda x: (x['score'], x['confidence']), reverse=True)
        
        return results[:limit]


def create_index_connector(backend: str, **kwargs) -> SearchIndexConnector:
    """Factory function to create search index connector"""
    
    if backend == "elasticsearch":
        if not ELASTICSEARCH_AVAILABLE:
            logger.warning("Elasticsearch not available, falling back to simple connector")
            return SimpleIndexConnector(**kwargs)
        return ElasticsearchConnector(**kwargs)
    
    elif backend == "simple":
        return SimpleIndexConnector(**kwargs)
    
    else:
        raise ValueError(f"Unknown search backend: {backend}")


async def test_index_connector():
    """Test index connector functionality"""
    
    # Create test graph
    graph = nx.DiGraph()
    graph.add_node("1", name="苹果公司", type="组织", description="美国科技公司")
    graph.add_node("2", name="蒂姆·库克", type="人物", description="苹果公司CEO")
    graph.add_edge("2", "1", relation_type="工作于", description="担任CEO")
    
    # Test simple connector
    connector = create_index_connector("simple")
    
    success = await connector.index_graph(graph)
    print(f"Indexing success: {success}")
    
    # Test search
    entities = await connector.search_entities("苹果")
    print(f"Entity search results: {entities}")
    
    relations = await connector.search_relations("工作")
    print(f"Relation search results: {relations}")


if __name__ == "__main__":
    asyncio.run(test_index_connector())
