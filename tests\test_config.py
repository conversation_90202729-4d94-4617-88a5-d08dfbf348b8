"""Tests for configuration management."""

import os
import pytest
from unittest.mock import patch

from config.settings import Settings


class TestSettings:
    """Test Settings class."""
    
    def test_default_settings(self):
        """Test default settings values."""
        settings = Settings()
        
        assert settings.llm_base_url == "https://gateway.chat.sensedeal.vip/v1"
        assert settings.llm_model_local == "qwen2.5-32b-instruct-int4"
        assert settings.llm_model_performance == "doubao-seed-1.6"
        assert settings.processing_mode == "merge"
        assert settings.max_workers == 5
        assert settings.entity_similarity_threshold == 0.85
        assert settings.web_host == "0.0.0.0"
        assert settings.web_port == 8000
    
    def test_environment_override(self):
        """Test that environment variables override defaults."""
        with patch.dict(os.environ, {
            'LLM_BASE_URL': 'http://localhost:8080',
            'MAX_WORKERS': '10',
            'WEB_PORT': '3000',
            'ENTITY_SIMILARITY_THRESHOLD': '0.9'
        }):
            settings = Settings()
            
            assert settings.llm_base_url == "http://localhost:8080"
            assert settings.max_workers == 10
            assert settings.web_port == 3000
            assert settings.entity_similarity_threshold == 0.9
    
    def test_supported_formats(self):
        """Test supported file formats."""
        settings = Settings()
        
        expected_formats = ["txt", "docx", "pdf", "chat"]
        assert settings.supported_formats == expected_formats
    
    def test_elasticsearch_hosts_list(self):
        """Test Elasticsearch hosts parsing."""
        with patch.dict(os.environ, {
            'ELASTICSEARCH_HOSTS': 'localhost:9200,remote:9200'
        }):
            settings = Settings()
            
            expected_hosts = ['localhost:9200', 'remote:9200']
            assert settings.elasticsearch_hosts_list == expected_hosts
    
    def test_get_model_for_index(self):
        """Test model selection based on index."""
        settings = Settings()
        
        # Test alternating model selection
        assert settings.get_model_for_index(0) == settings.llm_model_local
        assert settings.get_model_for_index(1) == settings.llm_model_performance
        assert settings.get_model_for_index(2) == settings.llm_model_local
        assert settings.get_model_for_index(3) == settings.llm_model_performance
    
    def test_debug_mode(self):
        """Test debug mode setting."""
        # Test default (False)
        settings = Settings()
        assert settings.debug is False
        
        # Test environment override
        with patch.dict(os.environ, {'DEBUG': 'true'}):
            settings = Settings()
            assert settings.debug is True
        
        with patch.dict(os.environ, {'DEBUG': '1'}):
            settings = Settings()
            assert settings.debug is True
    
    def test_log_level(self):
        """Test log level setting."""
        # Test default
        settings = Settings()
        assert settings.log_level == "INFO"
        
        # Test environment override
        with patch.dict(os.environ, {'LOG_LEVEL': 'DEBUG'}):
            settings = Settings()
            assert settings.log_level == "DEBUG"
    
    def test_invalid_processing_mode(self):
        """Test handling of invalid processing mode."""
        with patch.dict(os.environ, {'PROCESSING_MODE': 'invalid'}):
            settings = Settings()
            # Should fall back to default
            assert settings.processing_mode == "merge"
    
    def test_timeout_settings(self):
        """Test timeout configuration."""
        settings = Settings()
        
        assert settings.segment_timeout_seconds == 300
        assert settings.total_timeout_seconds == 3600
        
        # Test environment override
        with patch.dict(os.environ, {
            'SEGMENT_TIMEOUT_SECONDS': '600',
            'TOTAL_TIMEOUT_SECONDS': '7200'
        }):
            settings = Settings()
            assert settings.segment_timeout_seconds == 600
            assert settings.total_timeout_seconds == 7200
    
    def test_temp_directory(self):
        """Test temporary directory setting."""
        settings = Settings()
        
        # Should have a default temp directory
        assert settings.temp_dir
        assert isinstance(settings.temp_dir, str)
        
        # Test environment override
        with patch.dict(os.environ, {'TEMP_DIR': '/custom/temp'}):
            settings = Settings()
            assert settings.temp_dir == "/custom/temp"


class TestSettingsValidation:
    """Test settings validation."""
    
    def test_invalid_similarity_threshold(self):
        """Test validation of similarity threshold."""
        with patch.dict(os.environ, {'ENTITY_SIMILARITY_THRESHOLD': '1.5'}):
            settings = Settings()
            # Should clamp to valid range
            assert 0.0 <= settings.entity_similarity_threshold <= 1.0
        
        with patch.dict(os.environ, {'ENTITY_SIMILARITY_THRESHOLD': '-0.5'}):
            settings = Settings()
            # Should clamp to valid range
            assert 0.0 <= settings.entity_similarity_threshold <= 1.0
    
    def test_invalid_max_workers(self):
        """Test validation of max workers."""
        with patch.dict(os.environ, {'MAX_WORKERS': '0'}):
            settings = Settings()
            # Should have minimum value
            assert settings.max_workers >= 1
        
        with patch.dict(os.environ, {'MAX_WORKERS': '100'}):
            settings = Settings()
            # Should have reasonable maximum
            assert settings.max_workers <= 50
    
    def test_invalid_port(self):
        """Test validation of port number."""
        with patch.dict(os.environ, {'WEB_PORT': '0'}):
            settings = Settings()
            # Should have minimum valid port
            assert settings.web_port >= 1024
        
        with patch.dict(os.environ, {'WEB_PORT': '70000'}):
            settings = Settings()
            # Should have maximum valid port
            assert settings.web_port <= 65535


@pytest.fixture
def clean_environment():
    """Fixture to provide clean environment for tests."""
    # Store original environment
    original_env = os.environ.copy()
    
    # Clear relevant environment variables
    env_vars_to_clear = [
        'LLM_BASE_URL', 'LLM_API_KEY', 'LLM_MODEL_LOCAL', 'LLM_MODEL_PERFORMANCE',
        'PROCESSING_MODE', 'MAX_WORKERS', 'ENTITY_SIMILARITY_THRESHOLD',
        'WEB_HOST', 'WEB_PORT', 'DEBUG', 'LOG_LEVEL', 'TEMP_DIR',
        'ELASTICSEARCH_HOSTS', 'ELASTICSEARCH_INDEX',
        'SEGMENT_TIMEOUT_SECONDS', 'TOTAL_TIMEOUT_SECONDS'
    ]
    
    for var in env_vars_to_clear:
        if var in os.environ:
            del os.environ[var]
    
    yield
    
    # Restore original environment
    os.environ.clear()
    os.environ.update(original_env)


def test_settings_with_clean_environment(clean_environment):
    """Test settings with completely clean environment."""
    settings = Settings()
    
    # Should use all default values
    assert settings.llm_base_url == "https://gateway.chat.sensedeal.vip/v1"
    assert settings.processing_mode == "merge"
    assert settings.max_workers == 5
    assert settings.web_port == 8000
