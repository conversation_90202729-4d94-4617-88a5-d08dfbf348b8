"""Main processing pipeline."""

import asyncio
from pathlib import Path
from typing import Dict, Any, List, Optional
import time

from loguru import logger

from .document_processor import DocumentProcessor
from .entity_extractor import EntityExtractor
from .graph_builder import GraphBuilder


class Pipeline:
    """Main processing pipeline for knowledge graph building."""
    
    def __init__(self, llm_client=None):
        self.document_processor = DocumentProcessor()
        self.entity_extractor = EntityExtractor(llm_client)
        self.graph_builder = GraphBuilder()
        
        self.stats = {
            "documents_processed": 0,
            "entities_extracted": 0,
            "relations_extracted": 0,
            "processing_time": 0
        }
    
    async def process(self, input_path: str, output_path: str = None, 
                     mode: str = "merge") -> Dict[str, Any]:
        """Process documents and build knowledge graph."""
        start_time = time.time()
        
        try:
            logger.info(f"Starting pipeline processing: {input_path}")
            
            # Step 1: Process documents
            logger.info("Step 1: Processing documents...")
            documents = await self.document_processor.process_directory(input_path)
            self.stats["documents_processed"] = len(documents)
            
            if not documents:
                raise ValueError("No documents found to process")
            
            # Step 2: Extract entities and relations
            logger.info("Step 2: Extracting entities and relations...")
            all_entities = []
            all_relations = []
            
            for doc in documents:
                logger.info(f"Processing document: {doc['file_name']}")
                
                # Process each segment
                processed_segments = await self.entity_extractor.process_segments(doc["segments"])
                
                # Collect entities and relations
                for segment in processed_segments:
                    all_entities.extend(segment.get("entities", []))
                    all_relations.extend(segment.get("relations", []))
                
                # Update document with processed segments
                doc["processed_segments"] = processed_segments
            
            self.stats["entities_extracted"] = len(all_entities)
            self.stats["relations_extracted"] = len(all_relations)
            
            # Step 3: Build knowledge graph
            logger.info("Step 3: Building knowledge graph...")
            
            if mode == "merge":
                # Merge all entities and relations into single graph
                self.graph_builder.merge_entities(all_entities)
                self.graph_builder.merge_relations(all_relations)
                
                result = {
                    "mode": mode,
                    "documents": documents,
                    "graph": self.graph_builder.to_dict(),
                    "statistics": self.graph_builder.get_statistics()
                }
            
            elif mode == "batch":
                # Create separate graphs for each document
                document_graphs = []
                
                for doc in documents:
                    doc_graph = GraphBuilder()
                    
                    # Collect entities and relations for this document
                    doc_entities = []
                    doc_relations = []
                    
                    for segment in doc.get("processed_segments", []):
                        doc_entities.extend(segment.get("entities", []))
                        doc_relations.extend(segment.get("relations", []))
                    
                    doc_graph.merge_entities(doc_entities)
                    doc_graph.merge_relations(doc_relations)
                    
                    document_graphs.append({
                        "document": doc["file_name"],
                        "graph": doc_graph.to_dict()
                    })
                
                result = {
                    "mode": mode,
                    "documents": documents,
                    "document_graphs": document_graphs,
                    "statistics": self._calculate_batch_statistics(document_graphs)
                }
            
            else:
                raise ValueError(f"Unknown processing mode: {mode}")
            
            # Step 4: Save results if output path provided
            if output_path:
                await self._save_results(result, output_path)
            
            # Update timing
            self.stats["processing_time"] = time.time() - start_time
            result["processing_stats"] = self.stats.copy()
            
            logger.info(f"Pipeline completed successfully in {self.stats['processing_time']:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"Pipeline processing failed: {e}")
            raise
    
    def _calculate_batch_statistics(self, document_graphs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate statistics for batch mode."""
        total_entities = 0
        total_relations = 0
        entity_types = {}
        relation_types = {}
        
        for doc_graph in document_graphs:
            stats = doc_graph["graph"]["statistics"]
            total_entities += stats["total_entities"]
            total_relations += stats["total_relations"]
            
            # Merge entity types
            for entity_type, count in stats["entity_types"].items():
                entity_types[entity_type] = entity_types.get(entity_type, 0) + count
            
            # Merge relation types
            for relation_type, count in stats["relation_types"].items():
                relation_types[relation_type] = relation_types.get(relation_type, 0) + count
        
        return {
            "total_entities": total_entities,
            "total_relations": total_relations,
            "entity_types": entity_types,
            "relation_types": relation_types,
            "documents_processed": len(document_graphs)
        }
    
    async def _save_results(self, result: Dict[str, Any], output_path: str):
        """Save processing results to files."""
        output_dir = Path(output_path)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        try:
            import aiofiles
            
            # Save main result as JSON
            result_file = output_dir / "knowledge_graph.json"
            async with aiofiles.open(result_file, 'w', encoding='utf-8') as f:
                import json
                await f.write(json.dumps(result, indent=2, ensure_ascii=False))
            
            # Save statistics
            stats_file = output_dir / "statistics.json"
            async with aiofiles.open(stats_file, 'w', encoding='utf-8') as f:
                stats_data = result.get("statistics", result.get("processing_stats", {}))
                await f.write(json.dumps(stats_data, indent=2, ensure_ascii=False))
            
            logger.info(f"Results saved to {output_dir}")
            
        except ImportError:
            # Fallback to synchronous file operations
            import json
            
            result_file = output_dir / "knowledge_graph.json"
            result_file.write_text(json.dumps(result, indent=2, ensure_ascii=False), encoding='utf-8')
            
            stats_file = output_dir / "statistics.json"
            stats_data = result.get("statistics", result.get("processing_stats", {}))
            stats_file.write_text(json.dumps(stats_data, indent=2, ensure_ascii=False), encoding='utf-8')
            
            logger.info(f"Results saved to {output_dir}")
    
    def get_graph(self) -> GraphBuilder:
        """Get the current graph builder instance."""
        return self.graph_builder
    
    def search_entities(self, query: str, entity_type: str = None) -> List[Dict[str, Any]]:
        """Search entities in the current graph."""
        return self.graph_builder.search_entities(query, entity_type)
    
    def get_entity_neighbors(self, entity_name: str) -> List[Dict[str, Any]]:
        """Get neighbors of an entity."""
        return self.graph_builder.get_neighbors(entity_name)
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get current graph statistics."""
        graph_stats = self.graph_builder.get_statistics()
        graph_stats.update(self.stats)
        return graph_stats
