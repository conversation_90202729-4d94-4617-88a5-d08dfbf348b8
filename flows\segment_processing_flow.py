"""Segment processing flow for parallel LLM-based entity extraction."""

from typing import Dict, Any, List

from pocketflow import AsyncFlow
from loguru import logger

from nodes.processing_nodes import ProcessSegmentBatch, MergeSegmentResults


class SegmentProcessingBatchFlow(AsyncFlow):
    """Parallel LLM task execution for document segments."""
    
    def __init__(self, max_concurrent: int = 5, timeout: int = 300):
        super().__init__()
        
        # Create nodes
        self.process_segments = ProcessSegmentBatch(
            max_concurrent=max_concurrent,
            timeout=timeout
        )
        self.merge_results = MergeSegmentResults()
        
        # Set up flow
        self._setup_flow()
    
    def _setup_flow(self):
        """Set up the flow connections between nodes."""
        
        # Start with segment processing
        self.start(self.process_segments)
        
        # Connect to result merging
        self.process_segments >> self.merge_results
    
    async def prep_async(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare segment processing flow.
        
        Args:
            shared: Shared data structure
            
        Returns:
            Preparation data
        """
        logger.info("Starting segment processing flow")
        
        documents = shared.get("documents", [])
        
        if not documents:
            raise ValueError("No documents found for segment processing")
        
        # Count total segments
        total_segments = sum(len(doc.get("content_segments", [])) for doc in documents)
        
        if total_segments == 0:
            raise ValueError("No content segments found for processing")
        
        logger.info(f"Processing {total_segments} segments across {len(documents)} documents")
        
        return {
            "total_segments": total_segments,
            "total_documents": len(documents),
            "processing_start_time": logger._core.clock()
        }
    
    async def post_async(
        self,
        shared: Dict[str, Any],
        prep_res: Dict[str, Any],
        exec_res: Any
    ) -> str:
        """
        Post-process segment processing results.
        
        Args:
            shared: Shared data structure
            prep_res: Preparation results
            exec_res: Execution results
            
        Returns:
            Next action
        """
        processing_time = logger._core.clock() - prep_res["processing_start_time"]
        
        # Calculate processing statistics
        documents = shared.get("documents", [])
        total_entities = sum(len(doc.get("raw_entities", [])) for doc in documents)
        total_relations = sum(len(doc.get("relations", [])) for doc in documents)
        
        # Check for failed segments
        task_state = shared.get("task_state", {})
        failed_segments = task_state.get("failed_segments", {})
        total_failed = sum(len(segments) for segments in failed_segments.values())
        
        logger.info(f"Segment processing completed in {processing_time:.2f} seconds")
        logger.info(f"Extracted {total_entities} entities and {total_relations} relations")
        
        if total_failed > 0:
            logger.warning(f"Failed to process {total_failed} segments")
        
        # Update task state
        task_state["segment_processing_completed"] = True
        task_state["segment_processing_time"] = processing_time
        task_state["entities_extracted"] = total_entities
        task_state["relations_extracted"] = total_relations
        
        return "default"


class AdaptiveSegmentProcessingFlow(AsyncFlow):
    """Segment processing flow with adaptive concurrency and retry logic."""
    
    def __init__(self, initial_concurrent: int = 3, max_concurrent: int = 10):
        super().__init__()
        
        self.initial_concurrent = initial_concurrent
        self.max_concurrent = max_concurrent
        self.current_concurrent = initial_concurrent
        
        # Create nodes with adaptive settings
        self.process_segments = ProcessSegmentBatch(
            max_concurrent=self.current_concurrent,
            timeout=300
        )
        self.merge_results = MergeSegmentResults()
        
        # Set up flow
        self._setup_flow()
    
    def _setup_flow(self):
        """Set up the adaptive flow."""
        
        # Start with segment processing
        self.start(self.process_segments)
        
        # Connect to result merging
        self.process_segments >> self.merge_results
    
    async def prep_async(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare adaptive segment processing.
        
        Args:
            shared: Shared data structure
            
        Returns:
            Preparation data
        """
        logger.info("Starting adaptive segment processing flow")
        
        documents = shared.get("documents", [])
        total_segments = sum(len(doc.get("content_segments", [])) for doc in documents)
        
        # Adapt concurrency based on workload
        if total_segments > 50:
            self.current_concurrent = min(self.max_concurrent, total_segments // 10)
        elif total_segments > 20:
            self.current_concurrent = min(self.max_concurrent, 5)
        else:
            self.current_concurrent = self.initial_concurrent
        
        # Update process segments node
        self.process_segments.max_concurrent = self.current_concurrent
        
        logger.info(f"Adaptive processing: {total_segments} segments with {self.current_concurrent} concurrent workers")
        
        return {
            "total_segments": total_segments,
            "concurrent_workers": self.current_concurrent,
            "processing_start_time": logger._core.clock()
        }
    
    async def post_async(
        self,
        shared: Dict[str, Any],
        prep_res: Dict[str, Any],
        exec_res: Any
    ) -> str:
        """
        Post-process adaptive segment processing results.
        
        Args:
            shared: Shared data structure
            prep_res: Preparation results
            exec_res: Execution results
            
        Returns:
            Next action
        """
        processing_time = logger._core.clock() - prep_res["processing_start_time"]
        concurrent_workers = prep_res["concurrent_workers"]
        
        # Calculate throughput
        total_segments = prep_res["total_segments"]
        throughput = total_segments / processing_time if processing_time > 0 else 0
        
        logger.info(f"Adaptive processing completed:")
        logger.info(f"  Time: {processing_time:.2f} seconds")
        logger.info(f"  Workers: {concurrent_workers}")
        logger.info(f"  Throughput: {throughput:.2f} segments/second")
        
        # Store performance metrics
        task_state = shared.setdefault("task_state", {})
        task_state["adaptive_processing_metrics"] = {
            "processing_time": processing_time,
            "concurrent_workers": concurrent_workers,
            "throughput": throughput,
            "total_segments": total_segments
        }
        
        return "default"


class SegmentProcessingFlowFactory:
    """Factory for creating segment processing flows with different configurations."""
    
    @staticmethod
    def create_standard_flow(max_concurrent: int = 5) -> SegmentProcessingBatchFlow:
        """Create standard segment processing flow."""
        return SegmentProcessingBatchFlow(max_concurrent=max_concurrent)
    
    @staticmethod
    def create_adaptive_flow(
        initial_concurrent: int = 3,
        max_concurrent: int = 10
    ) -> AdaptiveSegmentProcessingFlow:
        """Create adaptive segment processing flow."""
        return AdaptiveSegmentProcessingFlow(
            initial_concurrent=initial_concurrent,
            max_concurrent=max_concurrent
        )
    
    @staticmethod
    def create_high_throughput_flow(
        max_concurrent: int = 15,
        timeout: int = 180
    ) -> SegmentProcessingBatchFlow:
        """Create high-throughput segment processing flow."""
        return SegmentProcessingBatchFlow(
            max_concurrent=max_concurrent,
            timeout=timeout
        )
    
    @staticmethod
    def create_conservative_flow(
        max_concurrent: int = 2,
        timeout: int = 600
    ) -> SegmentProcessingBatchFlow:
        """Create conservative segment processing flow with longer timeouts."""
        return SegmentProcessingBatchFlow(
            max_concurrent=max_concurrent,
            timeout=timeout
        )
