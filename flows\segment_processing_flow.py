"""
Segment processing flow for parallel LLM processing
Handles entity extraction from document segments using multiple LLM models
"""

import asyncio
from typing import Dict, List, Any, Optional
from loguru import logger

from .base_flow import BaseFlow, BatchFlow, RetryFlow
from utils.llm_client import LLMClient
from utils.task_queue import AsyncTaskManager
from config.settings import Settings


class SegmentProcessingFlow(BaseFlow):
    """Flow for processing a single segment with LLM"""
    
    def __init__(self, shared_context: Dict[str, Any],
                 segment: Dict[str, Any],
                 segment_index: int,
                 settings: Settings,
                 flow_id: Optional[str] = None):
        super().__init__(shared_context, flow_id or f"SegmentProcessing_{segment_index}")
        self.segment = segment
        self.segment_index = segment_index
        self.settings = settings
    
    async def execute(self) -> Dict[str, Any]:
        """Execute segment processing with LLM"""
        
        text = self.segment['text']
        
        # Skip very short segments
        if len(text.strip()) < 50:
            logger.debug(f"Skipping short segment {self.segment_index}: {len(text)} chars")
            return {"entities": [], "relations": []}
        
        # Get model for this segment (load balancing)
        model_router = self.shared_context['context']['model_router']
        model_name = model_router(self.segment_index)
        
        logger.debug(f"Processing segment {self.segment_index} with model {model_name}")
        
        try:
            # Create LLM client
            async with LLMClient(
                base_url=self.settings.llm.base_url,
                api_key=self.settings.llm.api_key,
                models=self.settings.llm.models,
                max_tokens=self.settings.llm.max_tokens,
                temperature=self.settings.llm.temperature,
                timeout=self.settings.llm.timeout,
                max_retries=self.settings.llm.max_retries
            ) as client:
                
                # Extract entities and relations
                result = await client.extract_entities_and_relations(text, model_name)
                
                # Filter results by confidence
                filtered_entities = [
                    entity for entity in result.get('entities', [])
                    if entity.get('confidence', 0) >= self.settings.processing.min_entity_confidence
                ]
                
                # Limit number of entities per segment
                if len(filtered_entities) > self.settings.processing.max_entities_per_segment:
                    # Sort by confidence and take top entities
                    filtered_entities.sort(key=lambda x: x.get('confidence', 0), reverse=True)
                    filtered_entities = filtered_entities[:self.settings.processing.max_entities_per_segment]
                
                # Add segment metadata to entities
                for entity in filtered_entities:
                    entity['segment_index'] = self.segment_index
                    entity['source_file'] = self.segment.get('file_path', '')
                
                # Add segment metadata to relations
                filtered_relations = result.get('relations', [])
                for relation in filtered_relations:
                    relation['segment_index'] = self.segment_index
                    relation['source_file'] = self.segment.get('file_path', '')
                
                logger.debug(f"Segment {self.segment_index} processed: "
                           f"{len(filtered_entities)} entities, {len(filtered_relations)} relations")
                
                return {
                    "entities": filtered_entities,
                    "relations": filtered_relations,
                    "segment_index": self.segment_index,
                    "model_used": model_name,
                    "processing_successful": True
                }
                
        except Exception as e:
            logger.error(f"Segment processing failed {self.segment_index}: {e}")
            
            # Store failed segment info
            failed_segments = self.shared_context['task_state']['failed_segments']
            file_path = self.segment.get('file_path', 'unknown')
            if file_path not in failed_segments:
                failed_segments[file_path] = []
            failed_segments[file_path].append(self.segment_index)
            
            return {
                "entities": [],
                "relations": [],
                "segment_index": self.segment_index,
                "processing_successful": False,
                "error": str(e)
            }


class SegmentProcessingBatchFlow(BaseFlow):
    """Batch flow for processing multiple segments in parallel"""
    
    def __init__(self, shared_context: Dict[str, Any],
                 segments: List[Dict[str, Any]],
                 settings: Settings,
                 flow_id: Optional[str] = None):
        super().__init__(shared_context, flow_id or "SegmentProcessingBatch")
        self.segments = segments
        self.settings = settings
    
    async def execute(self) -> List[Dict[str, Any]]:
        """Execute parallel segment processing"""
        
        if not self.segments:
            return []
        
        logger.info(f"Processing {len(self.segments)} segments in parallel")
        
        # Create task manager for rate limiting and concurrency control
        task_manager = AsyncTaskManager(
            max_concurrent=self.settings.processing.max_concurrent_segments,
            rate_limit=None  # LLM client handles its own rate limiting
        )
        
        # Add segment processing tasks
        task_ids = []
        for i, segment in enumerate(self.segments):
            
            # Create segment processing flow with retry
            segment_flow = SegmentProcessingFlow(
                shared_context=self.shared_context,
                segment=segment,
                segment_index=i,
                settings=self.settings
            )
            
            # Wrap in retry flow
            retry_flow = RetryFlow(
                shared_context=self.shared_context,
                target_flow=segment_flow,
                max_retries=2,  # Fewer retries for segments
                retry_delay=1.0,
                flow_id=f"RetrySegment_{i}"
            )
            
            # Add to task manager
            task_id = task_manager.add_task(
                task_id=f"segment_{i}",
                func=self._run_segment_flow,
                retry_flow,
                priority=0,
                max_retries=1,  # Task manager level retry
                timeout=300  # 5 minute timeout per segment
            )
            task_ids.append(task_id)
        
        # Start task manager
        manager_task = asyncio.create_task(task_manager.start())
        
        try:
            # Wait for all tasks to complete
            await task_manager.wait_for_completion(timeout=1800)  # 30 minute total timeout
            
            # Collect results
            results = []
            for task_id in task_ids:
                task_result = task_manager.get_task_result(task_id)
                if task_result and task_result.result:
                    if task_result.result.is_success:
                        results.append(task_result.result.result)
                    else:
                        # Failed segment
                        segment_index = int(task_id.split('_')[1])
                        results.append({
                            "entities": [],
                            "relations": [],
                            "segment_index": segment_index,
                            "processing_successful": False,
                            "error": str(task_result.result.error)
                        })
                else:
                    # Task not found or failed
                    segment_index = int(task_id.split('_')[1])
                    results.append({
                        "entities": [],
                        "relations": [],
                        "segment_index": segment_index,
                        "processing_successful": False,
                        "error": "Task execution failed"
                    })
            
            # Sort results by segment index
            results.sort(key=lambda x: x.get('segment_index', 0))
            
            # Log statistics
            successful_segments = sum(1 for r in results if r.get('processing_successful', False))
            total_entities = sum(len(r.get('entities', [])) for r in results)
            total_relations = sum(len(r.get('relations', [])) for r in results)
            
            logger.info(f"Segment processing completed: {successful_segments}/{len(self.segments)} successful, "
                       f"{total_entities} entities, {total_relations} relations")
            
            return results
            
        finally:
            # Stop task manager
            await task_manager.stop()
            manager_task.cancel()
            try:
                await manager_task
            except asyncio.CancelledError:
                pass
    
    async def _run_segment_flow(self, retry_flow: RetryFlow) -> Any:
        """Run segment flow and return result"""
        result = await retry_flow.run()
        return result


class SegmentProcessingSimpleFlow(BaseFlow):
    """Simple sequential segment processing flow (for testing/fallback)"""
    
    def __init__(self, shared_context: Dict[str, Any],
                 segments: List[Dict[str, Any]],
                 settings: Settings,
                 flow_id: Optional[str] = None):
        super().__init__(shared_context, flow_id or "SegmentProcessingSimple")
        self.segments = segments
        self.settings = settings
    
    async def execute(self) -> List[Dict[str, Any]]:
        """Execute sequential segment processing"""
        
        if not self.segments:
            return []
        
        logger.info(f"Processing {len(self.segments)} segments sequentially")
        
        results = []
        
        # Create single LLM client for all segments
        async with LLMClient(
            base_url=self.settings.llm.base_url,
            api_key=self.settings.llm.api_key,
            models=self.settings.llm.models,
            max_tokens=self.settings.llm.max_tokens,
            temperature=self.settings.llm.temperature,
            timeout=self.settings.llm.timeout,
            max_retries=self.settings.llm.max_retries
        ) as client:
            
            for i, segment in enumerate(self.segments):
                if self.is_cancelled():
                    break
                
                try:
                    # Create segment flow
                    segment_flow = SegmentProcessingFlow(
                        shared_context=self.shared_context,
                        segment=segment,
                        segment_index=i,
                        settings=self.settings
                    )
                    
                    # Override LLM client to reuse connection
                    segment_flow._llm_client = client
                    
                    result = await segment_flow.run()
                    
                    if result.is_success:
                        results.append(result.result)
                    else:
                        results.append({
                            "entities": [],
                            "relations": [],
                            "segment_index": i,
                            "processing_successful": False,
                            "error": str(result.error)
                        })
                        
                except Exception as e:
                    logger.error(f"Segment {i} processing failed: {e}")
                    results.append({
                        "entities": [],
                        "relations": [],
                        "segment_index": i,
                        "processing_successful": False,
                        "error": str(e)
                    })
        
        successful_segments = sum(1 for r in results if r.get('processing_successful', False))
        total_entities = sum(len(r.get('entities', [])) for r in results)
        total_relations = sum(len(r.get('relations', [])) for r in results)
        
        logger.info(f"Sequential processing completed: {successful_segments}/{len(self.segments)} successful, "
                   f"{total_entities} entities, {total_relations} relations")
        
        return results


def create_segment_processing_flow(shared_context: Dict[str, Any],
                                 segments: List[Dict[str, Any]],
                                 settings: Settings,
                                 use_parallel: bool = True,
                                 flow_id: Optional[str] = None) -> BaseFlow:
    """Factory function to create appropriate segment processing flow"""
    
    if use_parallel and len(segments) > 1:
        return SegmentProcessingBatchFlow(
            shared_context=shared_context,
            segments=segments,
            settings=settings,
            flow_id=flow_id
        )
    else:
        return SegmentProcessingSimpleFlow(
            shared_context=shared_context,
            segments=segments,
            settings=settings,
            flow_id=flow_id
        )
