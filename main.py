#!/usr/bin/env python3
"""
Knowledge Graph Builder from Text Documents
Main entry point for the application
"""

import asyncio
import sys
import os
from pathlib import Path
from typing import List, Optional

import click
from loguru import logger
from dotenv import load_dotenv

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.settings import Settings
from flows.main_flow import MainFlow
from utils.file_utils import validate_input_files


# Load environment variables
load_dotenv()

# Initialize settings
settings = Settings()


@click.group()
@click.option('--debug/--no-debug', default=False, help='Enable debug mode')
@click.option('--config', '-c', type=click.Path(exists=True), help='Config file path')
def cli(debug: bool, config: Optional[str]):
    """Knowledge Graph Builder CLI"""
    if debug:
        logger.remove()
        logger.add(sys.stderr, level="DEBUG")
    
    if config:
        settings.load_from_file(config)


@cli.command()
@click.argument('input_path', type=click.Path(exists=True))
@click.option('--mode', '-m', type=click.Choice(['batch', 'merge']), default='merge',
              help='Processing mode: batch (separate graphs) or merge (unified graph)')
@click.option('--output', '-o', type=click.Path(), default='output',
              help='Output directory')
@click.option('--formats', '-f', multiple=True, 
              type=click.Choice(['json', 'html', 'rdf', 'neo4j']),
              default=['json', 'html'], help='Output formats')
@click.option('--max-files', type=int, default=10, help='Maximum files to process')
@click.option('--parallel', '-p', type=int, default=3, help='Number of parallel workers')
def process(input_path: str, mode: str, output: str, formats: List[str], 
           max_files: int, parallel: int):
    """Process documents and build knowledge graph"""
    
    logger.info(f"Starting knowledge graph building from: {input_path}")
    logger.info(f"Mode: {mode}, Output: {output}, Formats: {formats}")
    
    # Validate input
    input_files = validate_input_files(input_path, max_files)
    if not input_files:
        logger.error("No valid input files found")
        sys.exit(1)
    
    logger.info(f"Found {len(input_files)} files to process")
    
    # Create output directory
    output_path = Path(output)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Run main processing flow
    try:
        asyncio.run(run_main_flow(input_files, mode, output_path, formats, parallel))
        logger.success("Knowledge graph building completed successfully!")
    except Exception as e:
        logger.error(f"Processing failed: {e}")
        sys.exit(1)


async def run_main_flow(input_files: List[Path], mode: str, output_path: Path, 
                       formats: List[str], parallel: int):
    """Run the main processing flow"""
    
    # Initialize shared context
    shared_context = {
        "context": {
            "input_files": input_files,
            "processing_mode": mode,
            "output_path": output_path,
            "output_formats": formats,
            "max_parallel": parallel,
            "supported_formats": settings.files.supported_formats,
        },
        "documents": [],
        "knowledge_graph": {
            "nodes": {},
            "edges": [],
            "index_ready": False
        },
        "outputs": {
            "wiki_pages": {},
            "json_graph": "",
            "search_index": ""
        },
        "task_state": {
            "completed_files": set(),
            "failed_segments": {},
            "retry_count": 0
        }
    }
    
    # Create and run main flow
    main_flow = MainFlow(shared_context, settings)
    await main_flow.run()


@cli.command()
@click.option('--host', default='0.0.0.0', help='Host to bind to')
@click.option('--port', default=8000, help='Port to bind to')
@click.option('--reload/--no-reload', default=True, help='Enable auto-reload')
def serve(host: str, port: int, reload: bool):
    """Start the web interface"""
    
    logger.info(f"Starting web server at http://{host}:{port}")
    
    try:
        import uvicorn
        from web.app import create_app
        
        app = create_app(settings)
        uvicorn.run(app, host=host, port=port, reload=reload)
    except ImportError:
        logger.error("Web dependencies not installed. Run: pip install fastapi uvicorn")
        sys.exit(1)


@cli.command()
@click.argument('graph_file', type=click.Path(exists=True))
@click.option('--format', '-f', type=click.Choice(['json', 'rdf']), default='json',
              help='Input graph format')
def visualize(graph_file: str, format: str):
    """Visualize an existing knowledge graph"""
    
    logger.info(f"Visualizing graph from: {graph_file}")
    
    try:
        from web.visualizer import GraphVisualizer
        
        visualizer = GraphVisualizer(settings)
        visualizer.load_graph(graph_file, format)
        visualizer.show()
    except ImportError:
        logger.error("Visualization dependencies not installed")
        sys.exit(1)


@cli.command()
def test():
    """Run the test suite"""
    
    logger.info("Running test suite...")
    
    try:
        import pytest
        exit_code = pytest.main(['-v', 'tests/'])
        sys.exit(exit_code)
    except ImportError:
        logger.error("Test dependencies not installed. Run: pip install pytest")
        sys.exit(1)


if __name__ == '__main__':
    cli()
