#!/usr/bin/env python3
"""
Knowledge Graph Builder - Main Entry Point

This script provides the main entry point for the Knowledge Graph Builder application.
It supports both CLI and web interface modes.
"""

import asyncio
import sys
from pathlib import Path
from typing import List, Optional

# Check for required dependencies
try:
    import click
    import uvicorn
    from rich.console import Console
    from rich.table import Table
    from rich.panel import Panel
except ImportError as e:
    print(f"❌ Missing required dependency: {e}")
    print("Please install dependencies by running:")
    print("  python quick_fix.py")
    print("Or manually install:")
    print("  pip install -r requirements-working.txt")
    sys.exit(1)

try:
    from config.settings import Settings
    from core.pipeline import Pipeline
except ImportError as e:
    print(f"❌ Failed to import project modules: {e}")
    print("Please ensure the project is properly set up.")
    sys.exit(1)


console = Console()
settings = Settings()


@click.group()
@click.version_option(version="0.1.0")
def cli():
    """Knowledge Graph Builder - Build knowledge graphs from text documents."""
    pass


@cli.command()
@click.argument("input_path", type=click.Path(exists=True))
@click.option("--mode", "-m", type=click.Choice(["batch", "merge"]), default="merge",
              help="Processing mode: 'batch' for independent graphs, 'merge' for unified graph")
@click.option("--output", "-o", type=click.Path(), default="./output",
              help="Output directory for generated files")
@click.option("--verbose", "-v", is_flag=True, help="Enable verbose logging")
def process(input_path: str, mode: str, output: str, verbose: bool):
    """Process documents and build knowledge graph."""

    if verbose:
        from loguru import logger
        logger.remove()
        logger.add(sys.stderr, level="DEBUG")
    
    if workers:
        settings.max_workers = workers
    
    settings.processing_mode = mode
    
    console.print(f"[bold green]Starting Knowledge Graph Builder[/bold green]")
    console.print(f"Input: {input_path}")
    console.print(f"Mode: {mode}")
    console.print(f"Output: {output}")

    # Run the pipeline
    try:
        pipeline = Pipeline()
        result = asyncio.run(pipeline.process(input_path, output, mode))

        console.print(f"[bold green]✓ Processing completed successfully![/bold green]")

        # Display summary
        display_summary(result)

    except Exception as e:
        console.print(f"[bold red]✗ Processing failed: {e}[/bold red]")
        if verbose:
            console.print_exception()
        sys.exit(1)


@cli.command()
@click.option("--host", default="0.0.0.0", help="Host to bind the server")
@click.option("--port", default=8000, type=int, help="Port to bind the server")
@click.option("--reload", is_flag=True, help="Enable auto-reload for development")
def serve(host: str, port: int, reload: bool):
    """Start the web interface server."""
    
    console.print(f"[bold blue]Starting Knowledge Graph Builder Web Interface[/bold blue]")
    console.print(f"Server will be available at: http://{host}:{port}")
    
    try:
        uvicorn.run(
            "web.app:app",
            host=host,
            port=port,
            reload=reload,
            log_level=settings.log_level.lower()
        )
    except KeyboardInterrupt:
        console.print("\n[yellow]Server stopped by user[/yellow]")
    except Exception as e:
        console.print(f"[bold red]Failed to start server: {e}[/bold red]")
        sys.exit(1)


@cli.command()
def models():
    """List available LLM models."""
    
    console.print("[bold blue]Available LLM Models[/bold blue]")
    
    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("Type", style="cyan")
    table.add_column("Model", style="green")
    table.add_column("Description", style="white")
    
    table.add_row("Local/Cheap", settings.llm_model_local, "Local model, approximately free")
    table.add_row("Performance", settings.llm_model_performance, "High performance model")
    
    console.print(table)
    console.print(f"\nAPI Endpoint: {settings.llm_base_url}")


@cli.command()
@click.argument("config_key", required=False)
def config(config_key: Optional[str]):
    """Show configuration settings."""
    
    if config_key:
        value = getattr(settings, config_key, None)
        if value is not None:
            console.print(f"{config_key}: {value}")
        else:
            console.print(f"[red]Configuration key '{config_key}' not found[/red]")
            sys.exit(1)
    else:
        console.print("[bold blue]Current Configuration[/bold blue]")
        
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Setting", style="cyan")
        table.add_column("Value", style="green")
        
        # Display key configuration values
        config_items = [
            ("llm_base_url", settings.llm_base_url),
            ("llm_model_local", settings.llm_model_local),
            ("llm_model_performance", settings.llm_model_performance),
            ("processing_mode", settings.processing_mode),
            ("max_workers", settings.max_workers),
            ("web_host", settings.web_host),
            ("web_port", settings.web_port),
            ("debug", settings.debug),
            ("log_level", settings.log_level)
        ]

        for field_name, value in config_items:
            if isinstance(value, list):
                value = ", ".join(map(str, value))
            table.add_row(field_name, str(value))
        
        console.print(table)


def display_summary(result: dict):
    """Display processing summary."""

    console.print("\n[bold blue]Processing Summary[/bold blue]")

    # Processing stats
    stats = result.get("processing_stats", {})
    console.print(f"Documents processed: {stats.get('documents_processed', 0)}")
    console.print(f"Entities extracted: {stats.get('entities_extracted', 0)}")
    console.print(f"Relations extracted: {stats.get('relations_extracted', 0)}")
    console.print(f"Processing time: {stats.get('processing_time', 0):.2f}s")

    # Graph statistics
    graph_stats = result.get("statistics", {})
    if graph_stats:
        console.print(f"Total entities: {graph_stats.get('total_entities', 0)}")
        console.print(f"Total relations: {graph_stats.get('total_relations', 0)}")

        # Entity types
        entity_types = graph_stats.get("entity_types", {})
        if entity_types:
            console.print("\nEntity types:")
            for entity_type, count in entity_types.items():
                console.print(f"  {entity_type}: {count}")

        # Relation types
        relation_types = graph_stats.get("relation_types", {})
        if relation_types:
            console.print("\nRelation types:")
            for relation_type, count in relation_types.items():
                console.print(f"  {relation_type}: {count}")
    
    # Task state
    task_state = shared["task_state"]
    completed = len(task_state["completed_files"])
    failed = len(task_state["failed_segments"])
    console.print(f"Files completed: {completed}")
    if failed > 0:
        console.print(f"[yellow]Failed segments: {failed}[/yellow]")


if __name__ == "__main__":
    cli()
