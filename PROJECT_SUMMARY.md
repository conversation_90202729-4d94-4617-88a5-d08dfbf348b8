# Knowledge Graph Builder - Project Summary

## 项目概述

Knowledge Graph Builder 是一个基于 PocketFlow 框架的知识图谱构建系统，能够从文本文档中提取实体和关系，构建可视化的知识图谱。

## 核心特性

### 🚀 主要功能
- **多格式支持**: 支持 TXT、DOCX、PDF、CHAT 等文件格式
- **并行处理**: 利用多个 LLM 并行处理文档段落
- **智能分割**: 基于语义边界的文档分割
- **实体规范化**: 自动去重和合并相似实体
- **双模式处理**: 支持批处理模式和合并模式
- **多格式输出**: 生成 HTML Wiki、JSON、RDF 等多种格式
- **Web 界面**: 交互式图形可视化和搜索功能

### 🔧 技术特点
- **异步架构**: 基于 asyncio 的高性能异步处理
- **安全优先**: 内置 PII 检测和输入清理
- **可配置**: 环境变量配置，支持灵活定制
- **可扩展**: 模块化设计，易于添加新功能
- **全面测试**: 单元测试、集成测试、Web 测试

## 项目结构

```
planner/
├── config/                 # 配置管理
│   ├── __init__.py
│   └── settings.py        # 环境变量配置
├── flows/                  # PocketFlow 工作流定义
│   ├── __init__.py
│   ├── main_flow.py       # 主流程
│   ├── file_processing_flow.py
│   ├── segment_processing_flow.py
│   ├── entity_normalization_flow.py
│   ├── graph_build_flow.py
│   └── post_processing_flow.py
├── nodes/                  # 处理节点
│   ├── __init__.py
│   ├── document_nodes.py  # 文档解析节点
│   ├── processing_nodes.py # LLM 处理节点
│   ├── entity_nodes.py    # 实体处理节点
│   ├── graph_nodes.py     # 图构建节点
│   └── output_nodes.py    # 输出生成节点
├── utils/                  # 工具函数
│   ├── __init__.py
│   ├── file_utils.py      # 文件处理工具
│   ├── llm_client.py      # LLM 客户端
│   ├── graph_ops.py       # 图操作工具
│   ├── wiki_renderer.py   # Wiki 渲染器
│   ├── index_connector.py # 搜索索引连接器
│   ├── security_filters.py # 安全过滤器
│   └── task_queue.py      # 异步任务管理器
├── web/                    # Web 界面
│   ├── __init__.py
│   ├── app.py             # FastAPI 应用
│   └── templates/         # HTML 模板
│       ├── base.html
│       ├── index.html
│       ├── upload.html
│       ├── search.html
│       ├── graph.html
│       └── jobs.html
├── tests/                  # 测试套件
│   ├── __init__.py
│   ├── test_config.py     # 配置测试
│   ├── test_utils.py      # 工具函数测试
│   ├── test_nodes.py      # 节点测试
│   ├── test_web.py        # Web 测试
│   └── test_integration.py # 集成测试
├── main.py                 # CLI 入口点
├── requirements.txt        # 项目依赖
├── pytest.ini            # 测试配置
├── run_tests.py           # 测试运行脚本
├── README.md              # 项目说明
├── DEVELOPMENT.md         # 开发指南
└── PROJECT_SUMMARY.md     # 项目总结
```

## 核心架构

### 流程架构
```
MainFlow
├── FileProcessingFlow
│   ├── ParseDocument (解析文档)
│   ├── SplitLongContent (内容分割)
│   ├── SegmentProcessingBatchFlow
│   │   ├── ProcessSegmentBatch (并行 LLM 处理)
│   │   └── MergeSegmentResults (结果合并)
│   └── EntityNormalizationFlow
│       ├── NormalizeEntities (实体规范化)
│       └── AddNodeTagsAndDescriptions (实体增强)
├── GraphBuildFlow
│   ├── GraphModeSelector (模式选择)
│   ├── BuildGraphBatch (批处理模式)
│   └── UpdateGlobalGraph (合并模式)
└── PostProcessingFlow
    ├── GenerateWikiPages (生成 Wiki 页面)
    ├── PersistKnowledgeGraph (持久化图谱)
    └── CleanTemporaryFiles (清理临时文件)
```

### 数据流
1. **文档解析**: 解析多种格式文档，提取文本内容
2. **内容分割**: 基于语义边界分割长文档
3. **并行处理**: 多个 LLM 并行提取实体和关系
4. **实体规范化**: 去重、合并相似实体，增强描述
5. **图谱构建**: 构建知识图谱（批处理或合并模式）
6. **输出生成**: 生成多种格式的输出文件

## 已实现功能

### ✅ 核心功能
- [x] 多格式文档解析 (TXT, DOCX, PDF)
- [x] 语义分割和内容处理
- [x] LLM 集成和并行处理
- [x] 实体提取和关系识别
- [x] 实体规范化和去重
- [x] 知识图谱构建
- [x] HTML Wiki 生成
- [x] JSON/RDF 导出
- [x] Elasticsearch 索引

### ✅ Web 界面
- [x] 响应式 Web 界面
- [x] 文档上传功能
- [x] 处理进度跟踪
- [x] 实体搜索功能
- [x] 图形可视化 (D3.js)
- [x] 作业管理界面

### ✅ 安全和质量
- [x] PII 检测和过滤
- [x] 输入验证和清理
- [x] 错误处理和重试机制
- [x] 全面的测试覆盖
- [x] 配置管理
- [x] 日志记录

## 技术栈

### 后端
- **Python 3.8+**: 主要编程语言
- **PocketFlow**: 工作流编排框架
- **FastAPI**: Web 框架
- **asyncio**: 异步处理
- **NetworkX**: 图操作
- **Elasticsearch**: 搜索索引
- **Jinja2**: 模板引擎

### 前端
- **Bootstrap 5**: UI 框架
- **D3.js**: 图形可视化
- **Font Awesome**: 图标库
- **Vanilla JavaScript**: 交互逻辑

### LLM 集成
- **OpenAI 兼容 API**: 支持多种 LLM 模型
- **推荐模型**:
  - `qwen2.5-32b-instruct-int4` (本地/经济型)
  - `doubao-seed-1.6` (高性能)

### 测试和开发
- **pytest**: 测试框架
- **pytest-asyncio**: 异步测试
- **pytest-cov**: 覆盖率测试
- **unittest.mock**: 模拟测试

## 配置说明

### 环境变量
```bash
# LLM 配置
LLM_BASE_URL=https://gateway.chat.sensedeal.vip/v1
LLM_API_KEY=974fd8d1c155aa3d04b17bf253176b5e
LLM_MODEL_LOCAL=qwen2.5-32b-instruct-int4
LLM_MODEL_PERFORMANCE=doubao-seed-1.6

# 处理配置
PROCESSING_MODE=merge
MAX_WORKERS=5
ENTITY_SIMILARITY_THRESHOLD=0.85

# Web 配置
WEB_HOST=0.0.0.0
WEB_PORT=8000
DEBUG=false

# 存储配置
ELASTICSEARCH_HOSTS=localhost:9200
TEMP_DIR=./temp
```

## 使用方法

### CLI 使用
```bash
# 处理文档
python main.py process /path/to/documents --mode merge --output ./output

# 启动 Web 服务
python main.py serve --host 0.0.0.0 --port 8000

# 查看配置
python main.py config

# 列出可用模型
python main.py models
```

### Web 界面使用
1. 访问 http://localhost:8000
2. 上传文档文件
3. 配置处理选项
4. 监控处理进度
5. 查看和搜索结果

## 测试

### 运行测试
```bash
# 所有测试
python run_tests.py

# 单元测试
python run_tests.py --unit

# 集成测试
python run_tests.py --integration

# Web 测试
python run_tests.py --web

# 覆盖率测试
python run_tests.py --coverage
```

### 测试覆盖
- **单元测试**: 配置、工具函数、节点
- **集成测试**: 完整流程、错误处理
- **Web 测试**: API 端点、页面渲染
- **性能测试**: 大文档处理、并发处理

## 性能特点

### 处理能力
- **并发处理**: 支持多个 LLM 并行调用
- **内存优化**: 流式处理大文档
- **错误恢复**: 自动重试和错误处理
- **进度跟踪**: 实时处理进度反馈

### 扩展性
- **模块化设计**: 易于添加新节点和流程
- **配置驱动**: 灵活的配置选项
- **插件架构**: 支持自定义处理器
- **多模型支持**: 支持不同 LLM 模型

## 部署建议

### 开发环境
```bash
# 克隆项目
git clone <repository-url>
cd planner

# 安装依赖
pip install -r requirements.txt

# 配置环境
cp .env.example .env

# 运行测试
python run_tests.py

# 启动服务
python main.py serve
```

### 生产环境
- 使用 Docker 容器化部署
- 配置 Nginx 反向代理
- 设置 Elasticsearch 集群
- 配置日志收集和监控
- 实施备份和恢复策略

## 未来改进

### 功能增强
- [ ] 支持更多文件格式 (Excel, PowerPoint)
- [ ] 实时协作编辑
- [ ] 图谱版本控制
- [ ] 高级查询语言
- [ ] 机器学习推荐

### 性能优化
- [ ] 分布式处理
- [ ] 缓存优化
- [ ] 数据库优化
- [ ] CDN 集成

### 用户体验
- [ ] 移动端适配
- [ ] 多语言支持
- [ ] 主题定制
- [ ] 快捷键支持

## 总结

Knowledge Graph Builder 是一个功能完整、架构清晰的知识图谱构建系统。它成功地将现代 Web 技术、异步处理、LLM 集成和图形可视化结合在一起，提供了一个强大而易用的知识管理平台。

项目的模块化设计和全面的测试覆盖确保了代码的可维护性和可靠性，而灵活的配置系统和扩展架构为未来的功能增强提供了良好的基础。
