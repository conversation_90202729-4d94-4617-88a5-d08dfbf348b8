"""Entity processing nodes for normalization and enhancement."""

import asyncio
from typing import Dict, List, Any, Optional

from pocketflow import As<PERSON><PERSON><PERSON>, AsyncBatchNode
from loguru import logger

from utils.graph_ops import GraphOperations, Entity
from utils.llm_client import LLMClient
from config.settings import Settings


class NormalizeEntities(AsyncNode):
    """Normalize and deduplicate entities across documents."""
    
    def __init__(self, use_cache: bool = True):
        super().__init__(max_retries=2, wait=5)
        self.use_cache = use_cache
        self.settings = Settings()
        self.graph_ops = GraphOperations(
            similarity_threshold=self.settings.entity_similarity_threshold
        )
        self.entity_cache = {}  # Simple in-memory cache
    
    async def prep_async(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare entities for normalization.
        
        Args:
            shared: Shared data structure
            
        Returns:
            Preparation data
        """
        documents = shared.get("documents", [])
        
        if not documents:
            raise ValueError("No documents found for entity normalization")
        
        # Collect all entities from all documents
        all_entities = []
        entity_sources = {}  # Track which document each entity came from
        
        for doc_idx, document in enumerate(documents):
            raw_entities = document.get("raw_entities", [])
            
            for entity_data in raw_entities:
                # Convert to Entity object
                entity = Entity(
                    canonical_name=entity_data["name"],
                    entity_type=entity_data["type"],
                    description=entity_data.get("description", ""),
                    source_weight=document.get("source_weight", 1),
                    metadata={
                        "source_file": entity_data.get("source_file", document["file_path"]),
                        "source_segment": entity_data.get("source_segment", 0),
                        "document_index": doc_idx
                    }
                )
                
                all_entities.append(entity)
                entity_sources[entity.canonical_name] = doc_idx
        
        logger.info(f"Collected {len(all_entities)} entities for normalization")
        
        return {
            "all_entities": all_entities,
            "entity_sources": entity_sources,
            "documents": documents
        }
    
    async def exec_async(self, prep_res: Dict[str, Any]) -> List[Entity]:
        """
        Execute entity normalization.
        
        Args:
            prep_res: Preparation results
            
        Returns:
            List of normalized entities
        """
        all_entities = prep_res["all_entities"]
        
        if not all_entities:
            return []
        
        # Use cache if enabled
        cache_key = f"normalize_{len(all_entities)}_{hash(tuple(e.canonical_name for e in all_entities[:10]))}"
        
        if self.use_cache and cache_key in self.entity_cache:
            logger.debug("Using cached normalization results")
            return self.entity_cache[cache_key]
        
        # Perform normalization
        normalized_entities = self.graph_ops.normalize_entities(all_entities)
        
        # Cache results
        if self.use_cache:
            self.entity_cache[cache_key] = normalized_entities
        
        logger.info(f"Normalized {len(all_entities)} entities to {len(normalized_entities)} unique entities")
        
        return normalized_entities
    
    async def post_async(
        self,
        shared: Dict[str, Any],
        prep_res: Dict[str, Any],
        exec_res: List[Entity]
    ) -> str:
        """
        Post-process normalization results.
        
        Args:
            shared: Shared data structure
            prep_res: Preparation results
            exec_res: Execution results
            
        Returns:
            Next action
        """
        normalized_entities = exec_res
        documents = prep_res["documents"]
        
        # Update documents with normalized entities
        for document in documents:
            document["normalized_entities"] = []
        
        # Distribute normalized entities back to documents based on source
        for entity in normalized_entities:
            source_docs = set()
            
            # Check metadata for source information
            if "document_index" in entity.metadata:
                source_docs.add(entity.metadata["document_index"])
            
            # Also check alternatives for source information
            for alt_entity in prep_res["all_entities"]:
                if (alt_entity.canonical_name in entity.alternatives or 
                    alt_entity.canonical_name == entity.canonical_name):
                    if "document_index" in alt_entity.metadata:
                        source_docs.add(alt_entity.metadata["document_index"])
            
            # Add entity to relevant documents
            for doc_idx in source_docs:
                if doc_idx < len(documents):
                    documents[doc_idx]["normalized_entities"].append(entity.to_dict())
        
        # Update shared data
        shared["documents"] = documents
        
        # Store global normalized entities
        shared["normalized_entities"] = [entity.to_dict() for entity in normalized_entities]
        
        logger.info(f"Entity normalization completed: {len(normalized_entities)} unique entities")
        
        return "default"


class AddNodeTagsAndDescriptions(AsyncBatchNode):
    """Enhance entities with additional tags and improved descriptions using LLM."""
    
    def __init__(self):
        super().__init__(max_retries=2, wait=3)
        self.settings = Settings()
    
    async def prep_async(self, shared: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Prepare entities for enhancement.
        
        Args:
            shared: Shared data structure
            
        Returns:
            List of entities to enhance
        """
        normalized_entities = shared.get("normalized_entities", [])
        
        if not normalized_entities:
            logger.warning("No normalized entities found for enhancement")
            return []
        
        # Prepare enhancement tasks
        enhancement_tasks = []
        
        for entity_data in normalized_entities:
            # Only enhance entities that need improvement
            needs_enhancement = (
                len(entity_data.get("description", "")) < 50 or  # Short description
                not entity_data.get("tags", []) or  # No tags
                entity_data.get("entity_type", "") == "unknown"  # Unknown type
            )
            
            if needs_enhancement:
                # Gather context from documents
                context_texts = []
                documents = shared.get("documents", [])
                
                for document in documents:
                    for segment in document.get("content_segments", []):
                        if entity_data["canonical_name"].lower() in segment["text"].lower():
                            context_texts.append(segment["text"])
                
                enhancement_tasks.append({
                    "entity": entity_data,
                    "context": " ".join(context_texts[:3])  # Limit context
                })
        
        logger.info(f"Prepared {len(enhancement_tasks)} entities for enhancement")
        return enhancement_tasks
    
    async def exec_async(self, enhancement_task: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enhance a single entity with LLM.
        
        Args:
            enhancement_task: Entity enhancement task
            
        Returns:
            Enhanced entity data
        """
        entity_data = enhancement_task["entity"]
        context = enhancement_task["context"]
        
        if not context:
            # No context available, return original entity
            return entity_data
        
        try:
            async with LLMClient(self.settings) as llm_client:
                # Enhance description
                enhanced_description = await llm_client.enhance_entity_description(
                    entity_name=entity_data["canonical_name"],
                    entity_type=entity_data.get("entity_type", "unknown"),
                    context=context,
                    model=self.settings.llm_model_performance  # Use better model for enhancement
                )
                
                # Generate tags based on context and description
                tag_prompt = f"""Based on the entity "{entity_data['canonical_name']}" of type "{entity_data.get('entity_type', 'unknown')}" and its description:

{enhanced_description}

Generate 3-5 relevant tags that categorize this entity. Return only the tags separated by commas, no explanations."""
                
                tag_response = await llm_client.call_llm(
                    prompt=tag_prompt,
                    model=self.settings.llm_model_local,  # Use local model for simple task
                    temperature=0.3
                )
                
                # Parse tags
                tags = [tag.strip() for tag in tag_response.content.split(",") if tag.strip()]
                tags = tags[:5]  # Limit to 5 tags
                
                # Update entity data
                enhanced_entity = entity_data.copy()
                enhanced_entity["description"] = enhanced_description.strip()
                enhanced_entity["tags"] = list(set(enhanced_entity.get("tags", []) + tags))
                
                logger.debug(f"Enhanced entity: {entity_data['canonical_name']}")
                
                return enhanced_entity
                
        except Exception as e:
            logger.error(f"Failed to enhance entity {entity_data['canonical_name']}: {e}")
            return entity_data  # Return original on failure
    
    async def post_async(
        self,
        shared: Dict[str, Any],
        prep_res: List[Dict[str, Any]],
        exec_res: List[Dict[str, Any]]
    ) -> str:
        """
        Post-process enhancement results.
        
        Args:
            shared: Shared data structure
            prep_res: Preparation results
            exec_res: Execution results
            
        Returns:
            Next action
        """
        enhanced_entities = exec_res
        
        # Create mapping of enhanced entities
        enhanced_map = {
            entity["canonical_name"]: entity
            for entity in enhanced_entities
        }
        
        # Update normalized entities in shared data
        normalized_entities = shared.get("normalized_entities", [])
        
        for i, entity_data in enumerate(normalized_entities):
            entity_name = entity_data["canonical_name"]
            if entity_name in enhanced_map:
                normalized_entities[i] = enhanced_map[entity_name]
        
        shared["normalized_entities"] = normalized_entities
        
        # Also update entities in documents
        documents = shared.get("documents", [])
        for document in documents:
            doc_entities = document.get("normalized_entities", [])
            for i, entity_data in enumerate(doc_entities):
                entity_name = entity_data["canonical_name"]
                if entity_name in enhanced_map:
                    doc_entities[i] = enhanced_map[entity_name]
            document["normalized_entities"] = doc_entities
        
        shared["documents"] = documents
        
        enhanced_count = len([e for e in enhanced_entities if e.get("description") or e.get("tags")])
        logger.info(f"Entity enhancement completed: {enhanced_count} entities enhanced")
        
        return "default"
