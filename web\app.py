"""FastAPI web application for Knowledge Graph Builder."""

import asyncio
import json
from pathlib import Path
from typing import Dict, List, Optional, Any

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, UploadFile, File, Form, BackgroundTasks
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.requests import Request
from fastapi.responses import HTMLResponse, JSONResponse
from pydantic import BaseModel
import uvicorn

from config.settings import Settings
from flows.main_flow import MainFlow
from utils.index_connector import IndexConnector
from utils.security_filters import SecurityFilters


# Initialize FastAPI app
app = FastAPI(
    title="Knowledge Graph Builder",
    description="Build knowledge graphs from text documents with LLM processing",
    version="0.1.0"
)

# Global settings
settings = Settings()
security_filters = SecurityFilters()
index_connector = IndexConnector(settings)

# Templates and static files
templates = Jinja2Templates(directory="web/templates")

# Mount static files if directory exists
static_path = Path("web/static")
if static_path.exists():
    app.mount("/static", StaticFiles(directory="web/static"), name="static")

# Global state for tracking processing jobs
processing_jobs: Dict[str, Dict[str, Any]] = {}


# Pydantic models
class ProcessingRequest(BaseModel):
    """Request model for document processing."""
    mode: str = "merge"
    output_formats: List[str] = ["html", "json"]
    max_workers: Optional[int] = None


class ProcessingStatus(BaseModel):
    """Response model for processing status."""
    job_id: str
    status: str
    progress: float
    message: str
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class SearchRequest(BaseModel):
    """Request model for entity search."""
    query: str
    entity_types: Optional[List[str]] = None
    size: int = 10


class EntityResponse(BaseModel):
    """Response model for entity data."""
    id: str
    canonical_name: str
    entity_type: str
    description: str
    alternatives: List[str]
    tags: List[str]
    relationships: List[Dict[str, Any]]


# Routes

@app.get("/", response_class=HTMLResponse)
async def index(request: Request):
    """Main page."""
    return templates.TemplateResponse("index.html", {"request": request})


@app.get("/upload", response_class=HTMLResponse)
async def upload_page(request: Request):
    """Upload documents page."""
    return templates.TemplateResponse("upload.html", {"request": request})


@app.get("/search", response_class=HTMLResponse)
async def search_page(request: Request):
    """Search entities page."""
    return templates.TemplateResponse("search.html", {"request": request})


@app.get("/graph", response_class=HTMLResponse)
async def graph_page(request: Request):
    """Graph visualization page."""
    return templates.TemplateResponse("graph.html", {"request": request})


@app.get("/jobs", response_class=HTMLResponse)
async def jobs_page(request: Request):
    """Processing jobs page."""
    return templates.TemplateResponse("jobs.html", {"request": request})


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "version": "0.1.0"}


@app.post("/api/process", response_model=ProcessingStatus)
async def start_processing(
    background_tasks: BackgroundTasks,
    files: List[UploadFile] = File(...),
    request_data: str = Form(...)
):
    """Start document processing job."""
    try:
        # Parse request data
        request_params = json.loads(request_data)
        processing_request = ProcessingRequest(**request_params)
        
        # Validate files
        if not files:
            raise HTTPException(status_code=400, detail="No files provided")
        
        # Security check for files
        for file in files:
            if not file.filename:
                raise HTTPException(status_code=400, detail="Invalid file")
            
            # Check file extension
            file_ext = Path(file.filename).suffix.lower().lstrip('.')
            if file_ext not in settings.supported_formats:
                raise HTTPException(
                    status_code=400, 
                    detail=f"Unsupported file format: {file_ext}"
                )
        
        # Generate job ID
        import uuid
        job_id = str(uuid.uuid4())
        
        # Save uploaded files
        temp_dir = Path(settings.temp_dir) / job_id
        temp_dir.mkdir(parents=True, exist_ok=True)
        
        saved_files = []
        for file in files:
            # Security check filename
            safe_filename = security_filters.create_safe_filename(file.filename)
            file_path = temp_dir / safe_filename
            
            # Save file
            content = await file.read()
            file_path.write_bytes(content)
            saved_files.append(str(file_path))
        
        # Initialize job status
        processing_jobs[job_id] = {
            "status": "queued",
            "progress": 0.0,
            "message": "Job queued for processing",
            "files": saved_files,
            "request": processing_request.dict(),
            "result": None,
            "error": None
        }
        
        # Start background processing
        background_tasks.add_task(process_documents_background, job_id, saved_files, processing_request)
        
        return ProcessingStatus(
            job_id=job_id,
            status="queued",
            progress=0.0,
            message="Processing started"
        )
        
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid request data format")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start processing: {str(e)}")


@app.get("/api/status/{job_id}", response_model=ProcessingStatus)
async def get_processing_status(job_id: str):
    """Get processing job status."""
    if job_id not in processing_jobs:
        raise HTTPException(status_code=404, detail="Job not found")
    
    job_data = processing_jobs[job_id]
    
    return ProcessingStatus(
        job_id=job_id,
        status=job_data["status"],
        progress=job_data["progress"],
        message=job_data["message"],
        result=job_data.get("result"),
        error=job_data.get("error")
    )


@app.post("/api/search", response_model=List[Dict[str, Any]])
async def search_entities(search_request: SearchRequest):
    """Search entities in the knowledge graph."""
    try:
        # Connect to Elasticsearch and search
        results = await asyncio.to_thread(
            index_connector.search_entities,
            query=search_request.query,
            entity_types=search_request.entity_types,
            size=search_request.size
        )
        
        return results
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")


@app.get("/api/entity/{entity_id}", response_model=EntityResponse)
async def get_entity(entity_id: str):
    """Get detailed entity information."""
    try:
        # Search for specific entity
        results = await asyncio.to_thread(
            index_connector.search_entities,
            query=f"_id:{entity_id}",
            size=1
        )
        
        if not results:
            raise HTTPException(status_code=404, detail="Entity not found")
        
        entity_data = results[0]["source"]
        
        return EntityResponse(
            id=entity_id,
            canonical_name=entity_data["canonical_name"],
            entity_type=entity_data["entity_type"],
            description=entity_data.get("description", ""),
            alternatives=entity_data.get("alternatives", []),
            tags=entity_data.get("tags", []),
            relationships=entity_data.get("relationships", [])
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get entity: {str(e)}")


@app.get("/api/graph/export/{format}")
async def export_graph(format: str):
    """Export knowledge graph in specified format."""
    if format not in ["json", "rdf"]:
        raise HTTPException(status_code=400, detail="Unsupported export format")
    
    try:
        # TODO: Implement graph export from stored data
        # For now, return placeholder
        return {"message": f"Graph export in {format} format not yet implemented"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Export failed: {str(e)}")


@app.delete("/api/jobs/{job_id}")
async def delete_job(job_id: str):
    """Delete processing job and cleanup files."""
    if job_id not in processing_jobs:
        raise HTTPException(status_code=404, detail="Job not found")
    
    try:
        # Cleanup temporary files
        temp_dir = Path(settings.temp_dir) / job_id
        if temp_dir.exists():
            import shutil
            shutil.rmtree(temp_dir)
        
        # Remove job from memory
        del processing_jobs[job_id]
        
        return {"message": "Job deleted successfully"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete job: {str(e)}")


# Background processing function
async def process_documents_background(
    job_id: str, 
    file_paths: List[str], 
    request: ProcessingRequest
):
    """Background task for document processing."""
    try:
        # Update job status
        processing_jobs[job_id]["status"] = "processing"
        processing_jobs[job_id]["message"] = "Processing documents..."
        processing_jobs[job_id]["progress"] = 0.1
        
        # Create shared data structure
        shared = {
            "context": {
                "input_path": file_paths[0] if len(file_paths) == 1 else str(Path(file_paths[0]).parent),
                "output_path": str(Path(settings.temp_dir) / job_id / "output"),
                "processing_mode": request.mode,
                "output_formats": request.output_formats,
                "supported_formats": settings.supported_formats,
                "model_router": settings.get_model_for_index,
            },
            "documents": [],
            "knowledge_graph": {
                "nodes": {},
                "edges": [],
                "index_ready": False
            },
            "outputs": {
                "wiki_pages": {},
                "json_graph": "",
                "search_index": ""
            },
            "task_state": {
                "completed_files": set(),
                "failed_segments": {},
                "retry_count": 0
            }
        }
        
        # Update progress
        processing_jobs[job_id]["progress"] = 0.2
        processing_jobs[job_id]["message"] = "Initializing processing flow..."
        
        # Run main flow
        main_flow = MainFlow()
        result = await main_flow.run_async(shared)
        
        # Update progress
        processing_jobs[job_id]["progress"] = 0.9
        processing_jobs[job_id]["message"] = "Finalizing results..."
        
        # Prepare result
        execution_summary = shared.get("execution_summary", {})
        outputs = shared.get("outputs", {})
        
        processing_jobs[job_id]["status"] = "completed"
        processing_jobs[job_id]["progress"] = 1.0
        processing_jobs[job_id]["message"] = "Processing completed successfully"
        processing_jobs[job_id]["result"] = {
            "summary": execution_summary,
            "outputs": outputs,
            "wiki_path": outputs.get("wiki_site_path"),
            "json_path": outputs.get("json_graph")
        }
        
    except Exception as e:
        processing_jobs[job_id]["status"] = "failed"
        processing_jobs[job_id]["progress"] = 0.0
        processing_jobs[job_id]["message"] = f"Processing failed: {str(e)}"
        processing_jobs[job_id]["error"] = str(e)


# Error handlers
@app.exception_handler(404)
async def not_found_handler(request: Request, exc: HTTPException):
    """Handle 404 errors."""
    return JSONResponse(
        status_code=404,
        content={"detail": "Resource not found"}
    )


@app.exception_handler(500)
async def internal_error_handler(request: Request, exc: Exception):
    """Handle internal server errors."""
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )


if __name__ == "__main__":
    uvicorn.run(
        "web.app:app",
        host=settings.web_host,
        port=settings.web_port,
        reload=settings.debug
    )
