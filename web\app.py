"""
FastAPI web application for knowledge graph visualization and interaction
"""

import json
import asyncio
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

from fastapi import FastAPI, HTTPException, UploadFile, File, Form, BackgroundTasks
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi import Request
from pydantic import BaseModel
import networkx as nx

from config.settings import Settings
from utils.index_connector import create_index_connector
from utils.file_utils import validate_input_files
from flows.main_flow import run_knowledge_graph_builder


class ProcessingRequest(BaseModel):
    """Request model for processing documents"""
    mode: str = "merge"
    output_formats: List[str] = ["json", "html"]
    max_files: int = 10


class SearchRequest(BaseModel):
    """Request model for search"""
    query: str
    entity_types: Optional[List[str]] = None
    limit: int = 10


class GraphStats(BaseModel):
    """Graph statistics model"""
    total_entities: int
    total_relations: int
    entity_types: Dict[str, int]
    relation_types: Dict[str, int]


def create_app(settings: Settings) -> FastAPI:
    """Create and configure FastAPI application"""
    
    app = FastAPI(
        title="Knowledge Graph Builder",
        description="Build and visualize knowledge graphs from text documents",
        version="0.1.0"
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.web.cors_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Mount static files
    if settings.web.static_files:
        static_path = Path("web/static")
        if static_path.exists():
            app.mount("/static", StaticFiles(directory=str(static_path)), name="static")
    
    # Setup templates
    templates = Jinja2Templates(directory="web/templates")
    
    # Global state
    app.state.settings = settings
    app.state.current_graph = None
    app.state.processing_status = "idle"
    app.state.search_connector = None
    
    @app.on_event("startup")
    async def startup_event():
        """Initialize application on startup"""
        try:
            # Initialize search connector
            app.state.search_connector = create_index_connector(
                backend=settings.output.search_backend,
                hosts=settings.storage.elasticsearch_hosts,
                index_name=settings.output.search_index_name
            )
            print(f"Initialized search backend: {settings.output.search_backend}")
        except Exception as e:
            print(f"Failed to initialize search backend: {e}")
            app.state.search_connector = None
    
    @app.on_event("shutdown")
    async def shutdown_event():
        """Cleanup on shutdown"""
        if app.state.search_connector:
            await app.state.search_connector.close()
    
    @app.get("/", response_class=HTMLResponse)
    async def home(request: Request):
        """Home page"""
        return templates.TemplateResponse("index.html", {
            "request": request,
            "title": "Knowledge Graph Builder"
        })
    
    @app.get("/upload", response_class=HTMLResponse)
    async def upload_page(request: Request):
        """Upload page"""
        return templates.TemplateResponse("upload.html", {
            "request": request,
            "title": "Upload Documents"
        })
    
    @app.get("/graph", response_class=HTMLResponse)
    async def graph_page(request: Request):
        """Graph visualization page"""
        return templates.TemplateResponse("graph.html", {
            "request": request,
            "title": "Knowledge Graph"
        })
    
    @app.get("/search", response_class=HTMLResponse)
    async def search_page(request: Request):
        """Search page"""
        return templates.TemplateResponse("search.html", {
            "request": request,
            "title": "Search"
        })
    
    @app.post("/api/upload")
    async def upload_files(
        background_tasks: BackgroundTasks,
        files: List[UploadFile] = File(...),
        mode: str = Form("merge"),
        output_formats: str = Form("json,html")
    ):
        """Upload and process files"""
        
        if app.state.processing_status != "idle":
            raise HTTPException(status_code=400, detail="Processing already in progress")
        
        try:
            # Save uploaded files
            upload_dir = Path("data/uploads")
            upload_dir.mkdir(parents=True, exist_ok=True)
            
            saved_files = []
            for file in files:
                if file.filename:
                    file_path = upload_dir / file.filename
                    with open(file_path, "wb") as f:
                        content = await file.read()
                        f.write(content)
                    saved_files.append(file_path)
            
            if not saved_files:
                raise HTTPException(status_code=400, detail="No files uploaded")
            
            # Validate files
            valid_files = validate_input_files(str(upload_dir), max_files=len(saved_files))
            
            if not valid_files:
                raise HTTPException(status_code=400, detail="No valid files found")
            
            # Parse output formats
            formats = [f.strip() for f in output_formats.split(",")]
            
            # Start background processing
            background_tasks.add_task(
                process_files_background,
                app,
                valid_files,
                mode,
                formats
            )
            
            app.state.processing_status = "processing"
            
            return {
                "message": "Files uploaded successfully, processing started",
                "files": [f.name for f in valid_files],
                "mode": mode,
                "formats": formats
            }
            
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.get("/api/status")
    async def get_status():
        """Get processing status"""
        return {
            "status": app.state.processing_status,
            "has_graph": app.state.current_graph is not None
        }
    
    @app.get("/api/graph/stats")
    async def get_graph_stats():
        """Get graph statistics"""
        
        if not app.state.current_graph:
            raise HTTPException(status_code=404, detail="No graph available")
        
        graph = app.state.current_graph
        
        # Calculate statistics
        entity_types = {}
        for node_id, node_data in graph.nodes(data=True):
            entity_type = node_data.get('type', 'unknown')
            entity_types[entity_type] = entity_types.get(entity_type, 0) + 1
        
        relation_types = {}
        for source, target, edge_data in graph.edges(data=True):
            relation_type = edge_data.get('relation_type', 'unknown')
            relation_types[relation_type] = relation_types.get(relation_type, 0) + 1
        
        return GraphStats(
            total_entities=graph.number_of_nodes(),
            total_relations=graph.number_of_edges(),
            entity_types=entity_types,
            relation_types=relation_types
        )
    
    @app.get("/api/graph/data")
    async def get_graph_data(limit: int = 100):
        """Get graph data for visualization"""
        
        if not app.state.current_graph:
            raise HTTPException(status_code=404, detail="No graph available")
        
        graph = app.state.current_graph
        
        # Convert to JSON format for visualization
        nodes = []
        edges = []
        
        # Limit nodes for performance
        node_list = list(graph.nodes(data=True))[:limit]
        
        for node_id, node_data in node_list:
            nodes.append({
                "id": node_id,
                "name": node_data.get('name', node_id),
                "type": node_data.get('type', 'unknown'),
                "description": node_data.get('description', ''),
                "tags": node_data.get('tags', [])
            })
        
        # Get edges for the limited nodes
        node_ids = {node_id for node_id, _ in node_list}
        
        for source, target, edge_data in graph.edges(data=True):
            if source in node_ids and target in node_ids:
                edges.append({
                    "source": source,
                    "target": target,
                    "relation_type": edge_data.get('relation_type', ''),
                    "description": edge_data.get('description', ''),
                    "weight": edge_data.get('weight', 1)
                })
        
        return {
            "nodes": nodes,
            "edges": edges,
            "total_nodes": graph.number_of_nodes(),
            "total_edges": graph.number_of_edges(),
            "limited": len(node_list) < graph.number_of_nodes()
        }
    
    @app.post("/api/search/entities")
    async def search_entities(request: SearchRequest):
        """Search entities"""
        
        if not app.state.search_connector:
            # Fallback to simple graph search
            if not app.state.current_graph:
                raise HTTPException(status_code=404, detail="No search backend or graph available")
            
            return await simple_graph_search(app.state.current_graph, request.query, request.limit)
        
        try:
            results = await app.state.search_connector.search_entities(
                query=request.query,
                limit=request.limit,
                entity_types=request.entity_types
            )
            
            return {"results": results}
            
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Search failed: {e}")
    
    @app.post("/api/search/relations")
    async def search_relations(request: SearchRequest):
        """Search relations"""
        
        if not app.state.search_connector:
            raise HTTPException(status_code=404, detail="No search backend available")
        
        try:
            results = await app.state.search_connector.search_relations(
                query=request.query,
                limit=request.limit,
                relation_types=request.entity_types  # Reuse for relation types
            )
            
            return {"results": results}
            
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Search failed: {e}")
    
    @app.get("/api/entity/{entity_id}")
    async def get_entity(entity_id: str):
        """Get entity details"""
        
        if not app.state.current_graph:
            raise HTTPException(status_code=404, detail="No graph available")
        
        graph = app.state.current_graph
        
        if entity_id not in graph.nodes:
            raise HTTPException(status_code=404, detail="Entity not found")
        
        entity_data = graph.nodes[entity_id]
        
        # Get related entities
        outgoing = []
        for _, target, edge_data in graph.out_edges(entity_id, data=True):
            target_data = graph.nodes[target]
            outgoing.append({
                "target_id": target,
                "target_name": target_data.get('name', target),
                "relation_type": edge_data.get('relation_type', ''),
                "description": edge_data.get('description', '')
            })
        
        incoming = []
        for source, _, edge_data in graph.in_edges(entity_id, data=True):
            source_data = graph.nodes[source]
            incoming.append({
                "source_id": source,
                "source_name": source_data.get('name', source),
                "relation_type": edge_data.get('relation_type', ''),
                "description": edge_data.get('description', '')
            })
        
        return {
            "entity": entity_data,
            "outgoing_relations": outgoing,
            "incoming_relations": incoming
        }
    
    return app


async def process_files_background(app: FastAPI, files: List[Path], 
                                 mode: str, formats: List[str]):
    """Background task for processing files"""
    
    try:
        app.state.processing_status = "processing"
        
        # Run knowledge graph building
        results = await run_knowledge_graph_builder(
            input_files=files,
            settings=app.state.settings,
            processing_mode=mode,
            output_path=Path("output"),
            output_formats=formats
        )
        
        # Load the generated graph
        if mode == "merge":
            # Try to load the graph from JSON output
            json_path = Path("output/knowledge_graph.json")
            if json_path.exists():
                with open(json_path, 'r', encoding='utf-8') as f:
                    graph_data = json.load(f)
                
                # Convert to NetworkX graph
                graph = nx.DiGraph()
                
                for node in graph_data.get('nodes', {}).values():
                    graph.add_node(node.get('id', ''), **node)
                
                for edge in graph_data.get('edges', []):
                    graph.add_edge(edge['source'], edge['target'], **edge)
                
                app.state.current_graph = graph
        
        # Index in search backend if available
        if app.state.search_connector and app.state.current_graph:
            await app.state.search_connector.index_graph(app.state.current_graph)
        
        app.state.processing_status = "completed"
        
    except Exception as e:
        print(f"Background processing failed: {e}")
        app.state.processing_status = "failed"


async def simple_graph_search(graph: nx.DiGraph, query: str, limit: int) -> Dict[str, Any]:
    """Simple graph search fallback"""
    
    query_lower = query.lower()
    results = []
    
    for node_id, node_data in graph.nodes(data=True):
        score = 0
        
        # Name match
        name = node_data.get('name', '')
        if query_lower in name.lower():
            score += 3
        
        # Description match
        description = node_data.get('description', '')
        if query_lower in description.lower():
            score += 2
        
        # Tag match
        tags = node_data.get('tags', [])
        for tag in tags:
            if query_lower in tag.lower():
                score += 1
        
        if score > 0:
            result = node_data.copy()
            result['id'] = node_id
            result['score'] = score
            results.append(result)
    
    # Sort by score
    results.sort(key=lambda x: x['score'], reverse=True)
    
    return {"results": results[:limit]}


if __name__ == "__main__":
    import uvicorn
    from config.settings import Settings
    
    settings = Settings()
    app = create_app(settings)
    
    uvicorn.run(
        app,
        host=settings.web.host,
        port=settings.web.port,
        reload=True
    )
