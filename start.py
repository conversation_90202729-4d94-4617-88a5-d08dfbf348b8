#!/usr/bin/env python3
"""Quick start script for Knowledge Graph Builder."""

import sys
import subprocess
import os
from pathlib import Path


def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True


def check_dependencies():
    """Check if required dependencies are installed."""
    required_packages = [
        'fastapi', 'uvicorn', 'pocketflow', 'networkx', 
        'loguru', 'python-multipart', 'jinja2'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}")
    
    if missing_packages:
        print(f"\n📦 Installing missing packages: {', '.join(missing_packages)}")
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install'
            ] + missing_packages)
            print("✅ Dependencies installed successfully")
            return True
        except subprocess.CalledProcessError:
            print("❌ Failed to install dependencies")
            print("Please run: pip install -r requirements.txt")
            return False
    
    return True


def setup_environment():
    """Set up environment configuration."""
    env_file = Path('.env')
    env_example = Path('.env.example')
    
    if not env_file.exists():
        if env_example.exists():
            print("📝 Creating .env file from template...")
            env_file.write_text(env_example.read_text())
            print("✅ .env file created")
            print("⚠️  Please edit .env file with your configuration")
        else:
            print("⚠️  No .env file found. Using default configuration.")
    else:
        print("✅ .env file exists")
    
    return True


def run_tests():
    """Run basic tests to verify installation."""
    print("\n🧪 Running basic tests...")
    
    try:
        # Test configuration loading
        from config.settings import Settings
        settings = Settings()
        print("✅ Configuration loading")
        
        # Test basic imports
        from utils.file_utils import FileUtils
        from utils.graph_ops import GraphOperations
        print("✅ Core modules import")
        
        # Test web app creation
        from web.app import app
        print("✅ Web application")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False


def main():
    """Main startup function."""
    print("🚀 Knowledge Graph Builder - Quick Start")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        return 1
    
    # Check and install dependencies
    if not check_dependencies():
        return 1
    
    # Set up environment
    if not setup_environment():
        return 1
    
    # Run basic tests
    if not run_tests():
        print("\n⚠️  Some tests failed, but you can still try to run the application")
    
    print("\n🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Edit .env file with your LLM API configuration")
    print("2. Run the application:")
    print("   python main.py serve")
    print("3. Open http://localhost:8000 in your browser")
    print("\n📚 For more information:")
    print("   - README.md - User guide")
    print("   - DEVELOPMENT.md - Developer guide")
    print("   - PROJECT_SUMMARY.md - Project overview")
    
    # Ask if user wants to start the web server
    try:
        response = input("\n❓ Start the web server now? (y/N): ").strip().lower()
        if response in ['y', 'yes']:
            print("\n🌐 Starting web server...")
            subprocess.run([sys.executable, 'main.py', 'serve'])
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
