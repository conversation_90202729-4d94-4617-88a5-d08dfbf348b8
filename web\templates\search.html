{% extends "base.html" %}

{% block title %}Search Entities - Knowledge Graph Builder{% endblock %}

{% block page_title %}Search Knowledge Graph{% endblock %}

{% block header_actions %}
<div class="btn-group" role="group">
    <a href="/" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i>
        Back to Dashboard
    </a>
    <a href="/graph" class="btn btn-outline-primary">
        <i class="fas fa-sitemap me-1"></i>
        Graph View
    </a>
</div>
{% endblock %}

{% block content %}
<!-- Search Interface -->
<div class="search-container">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="input-group input-group-lg mb-3">
                <input type="text" class="form-control" id="search-input" 
                       placeholder="Search entities, relationships, or descriptions...">
                <button class="btn btn-primary" type="button" id="search-btn">
                    <i class="fas fa-search"></i>
                </button>
            </div>
            
            <!-- Search Filters -->
            <div class="row">
                <div class="col-md-6">
                    <select class="form-select" id="entity-type-filter">
                        <option value="">All Entity Types</option>
                        <option value="person">Person</option>
                        <option value="organization">Organization</option>
                        <option value="location">Location</option>
                        <option value="concept">Concept</option>
                        <option value="event">Event</option>
                        <option value="product">Product</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <select class="form-select" id="results-limit">
                        <option value="10">10 results</option>
                        <option value="25" selected>25 results</option>
                        <option value="50">50 results</option>
                        <option value="100">100 results</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search Results -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-search me-2"></i>
                    Search Results
                </h6>
                <span id="results-count" class="badge bg-secondary">0 results</span>
            </div>
            <div class="card-body">
                <div id="search-results">
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-search fa-3x mb-3"></i>
                        <h5>Search the Knowledge Graph</h5>
                        <p>Enter a search term above to find entities, relationships, and concepts.</p>
                    </div>
                </div>
                
                <!-- Loading indicator -->
                <div id="search-loading" class="text-center py-4" style="display: none;">
                    <div class="loading-spinner me-2"></div>
                    Searching...
                </div>
                
                <!-- Pagination -->
                <nav id="search-pagination" style="display: none;">
                    <ul class="pagination justify-content-center">
                        <!-- Pagination will be generated here -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- Entity Detail Modal -->
<div class="modal fade" id="entity-modal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="entity-modal-title">Entity Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="entity-modal-body">
                <!-- Entity details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="view-in-graph-btn">
                    <i class="fas fa-sitemap me-1"></i>
                    View in Graph
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentResults = [];
let currentPage = 1;
let resultsPerPage = 25;

document.addEventListener('DOMContentLoaded', function() {
    initializeSearch();
});

function initializeSearch() {
    const searchInput = document.getElementById('search-input');
    const searchBtn = document.getElementById('search-btn');
    const entityTypeFilter = document.getElementById('entity-type-filter');
    const resultsLimit = document.getElementById('results-limit');
    
    // Search on button click
    searchBtn.addEventListener('click', performSearch);
    
    // Search on Enter key
    searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            performSearch();
        }
    });
    
    // Search on filter change
    entityTypeFilter.addEventListener('change', performSearch);
    resultsLimit.addEventListener('change', (e) => {
        resultsPerPage = parseInt(e.target.value);
        performSearch();
    });
    
    // Auto-search with debounce
    let searchTimeout;
    searchInput.addEventListener('input', () => {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            if (searchInput.value.length >= 2) {
                performSearch();
            }
        }, 500);
    });
}

async function performSearch() {
    const query = document.getElementById('search-input').value.trim();
    const entityType = document.getElementById('entity-type-filter').value;
    const limit = parseInt(document.getElementById('results-limit').value);
    
    if (!query) {
        displayEmptyState();
        return;
    }
    
    showLoading(true);
    
    try {
        const searchRequest = {
            query: query,
            entity_types: entityType ? [entityType] : null,
            size: limit
        };
        
        const response = await fetch('/api/search', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(searchRequest)
        });
        
        if (!response.ok) {
            throw new Error('Search failed');
        }
        
        const results = await response.json();
        currentResults = results;
        displayResults(results);
        
    } catch (error) {
        showAlert(`Search failed: ${error.message}`, 'danger');
        displayEmptyState();
    } finally {
        showLoading(false);
    }
}

function showLoading(show) {
    const loading = document.getElementById('search-loading');
    const results = document.getElementById('search-results');
    
    if (show) {
        loading.style.display = 'block';
        results.style.display = 'none';
    } else {
        loading.style.display = 'none';
        results.style.display = 'block';
    }
}

function displayResults(results) {
    const container = document.getElementById('search-results');
    const countBadge = document.getElementById('results-count');
    
    countBadge.textContent = `${results.length} results`;
    
    if (results.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-5">
                <i class="fas fa-search fa-3x mb-3"></i>
                <h5>No Results Found</h5>
                <p>Try adjusting your search terms or filters.</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = results.map(result => createResultCard(result)).join('');
}

function createResultCard(result) {
    const entity = result.source;
    const score = result.score;
    const highlights = result.highlight || {};
    
    const entityTypeClass = getEntityTypeClass(entity.entity_type);
    const description = highlights.description ? 
        highlights.description[0] : 
        (entity.description || 'No description available');
    
    return `
        <div class="entity-card card mb-3" onclick="showEntityDetails('${result.id}')">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <h6 class="card-title mb-2">
                            ${highlights.canonical_name ? highlights.canonical_name[0] : entity.canonical_name}
                            <span class="badge ${entityTypeClass} entity-type-badge ms-2">
                                ${entity.entity_type}
                            </span>
                        </h6>
                        <p class="card-text text-muted mb-2">${description}</p>
                        
                        ${entity.alternatives && entity.alternatives.length > 0 ? `
                            <div class="mb-2">
                                <small class="text-muted">Also known as: </small>
                                ${entity.alternatives.slice(0, 3).map(alt => 
                                    `<span class="badge bg-light text-dark me-1">${alt}</span>`
                                ).join('')}
                                ${entity.alternatives.length > 3 ? `<span class="text-muted">+${entity.alternatives.length - 3} more</span>` : ''}
                            </div>
                        ` : ''}
                        
                        ${entity.tags && entity.tags.length > 0 ? `
                            <div class="mb-2">
                                ${entity.tags.slice(0, 5).map(tag => 
                                    `<span class="badge bg-secondary me-1">${tag}</span>`
                                ).join('')}
                            </div>
                        ` : ''}
                        
                        ${entity.relationships && entity.relationships.length > 0 ? `
                            <small class="text-muted">
                                <i class="fas fa-link me-1"></i>
                                ${entity.relationships.length} relationship${entity.relationships.length !== 1 ? 's' : ''}
                            </small>
                        ` : ''}
                    </div>
                    
                    <div class="text-end">
                        <small class="text-muted">Score: ${score.toFixed(2)}</small>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function getEntityTypeClass(entityType) {
    const typeClasses = {
        'person': 'bg-primary',
        'organization': 'bg-success',
        'location': 'bg-info',
        'concept': 'bg-warning',
        'event': 'bg-danger',
        'product': 'bg-secondary'
    };
    return typeClasses[entityType.toLowerCase()] || 'bg-secondary';
}

async function showEntityDetails(entityId) {
    try {
        const response = await fetch(`/api/entity/${entityId}`);
        
        if (!response.ok) {
            throw new Error('Failed to load entity details');
        }
        
        const entity = await response.json();
        displayEntityModal(entity);
        
    } catch (error) {
        showAlert(`Failed to load entity details: ${error.message}`, 'danger');
    }
}

function displayEntityModal(entity) {
    const modal = document.getElementById('entity-modal');
    const title = document.getElementById('entity-modal-title');
    const body = document.getElementById('entity-modal-body');
    const viewInGraphBtn = document.getElementById('view-in-graph-btn');
    
    title.innerHTML = `
        ${entity.canonical_name}
        <span class="badge ${getEntityTypeClass(entity.entity_type)} ms-2">
            ${entity.entity_type}
        </span>
    `;
    
    body.innerHTML = `
        <div class="row">
            <div class="col-md-8">
                <h6>Description</h6>
                <p>${entity.description || 'No description available'}</p>
                
                ${entity.alternatives && entity.alternatives.length > 0 ? `
                    <h6>Alternative Names</h6>
                    <div class="mb-3">
                        ${entity.alternatives.map(alt => 
                            `<span class="badge bg-light text-dark me-1 mb-1">${alt}</span>`
                        ).join('')}
                    </div>
                ` : ''}
                
                ${entity.tags && entity.tags.length > 0 ? `
                    <h6>Tags</h6>
                    <div class="mb-3">
                        ${entity.tags.map(tag => 
                            `<span class="badge bg-secondary me-1 mb-1">${tag}</span>`
                        ).join('')}
                    </div>
                ` : ''}
            </div>
            
            <div class="col-md-4">
                <h6>Relationships</h6>
                ${entity.relationships && entity.relationships.length > 0 ? `
                    <div class="list-group list-group-flush">
                        ${entity.relationships.slice(0, 10).map(rel => `
                            <div class="list-group-item px-0">
                                <div class="fw-bold">${rel.relation_type}</div>
                                <div class="text-muted">${rel.target}</div>
                                ${rel.description ? `<small class="text-muted">${rel.description}</small>` : ''}
                            </div>
                        `).join('')}
                        ${entity.relationships.length > 10 ? `
                            <div class="list-group-item px-0 text-muted">
                                +${entity.relationships.length - 10} more relationships
                            </div>
                        ` : ''}
                    </div>
                ` : '<p class="text-muted">No relationships found</p>'}
            </div>
        </div>
    `;
    
    viewInGraphBtn.onclick = () => {
        window.location.href = `/graph?entity=${entity.id}`;
    };
    
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
}

function displayEmptyState() {
    const container = document.getElementById('search-results');
    const countBadge = document.getElementById('results-count');
    
    countBadge.textContent = '0 results';
    
    container.innerHTML = `
        <div class="text-center text-muted py-5">
            <i class="fas fa-search fa-3x mb-3"></i>
            <h5>Search the Knowledge Graph</h5>
            <p>Enter a search term above to find entities, relationships, and concepts.</p>
        </div>
    `;
}
</script>
{% endblock %}
