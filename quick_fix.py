#!/usr/bin/env python3
"""Quick fix script to install missing dependencies and start the application."""

import sys
import subprocess
import os


def install_package(package):
    """Install a single package."""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False


def main():
    """Quick fix main function."""
    print("🔧 Quick Fix - Installing missing dependencies...")
    
    # List of essential packages
    essential_packages = [
        "rich",
        "click", 
        "fastapi",
        "uvicorn[standard]",
        "python-dotenv",
        "loguru",
        "jinja2",
        "python-multipart",
        "aiofiles",
        "httpx",
        "networkx",
        "beautifulsoup4",
        "bleach"
    ]
    
    failed_packages = []
    
    for package in essential_packages:
        print(f"Installing {package}...")
        if install_package(package):
            print(f"✅ {package} installed")
        else:
            print(f"❌ Failed to install {package}")
            failed_packages.append(package)
    
    if failed_packages:
        print(f"\n⚠️  Failed to install: {', '.join(failed_packages)}")
        print("You may need to install them manually.")
    
    # Create basic .env file
    env_content = """# Basic configuration
LLM_BASE_URL=https://gateway.chat.sensedeal.vip/v1
LLM_API_KEY=974fd8d1c155aa3d04b17bf253176b5e
LLM_MODEL_LOCAL=qwen2.5-32b-instruct-int4
LLM_MODEL_PERFORMANCE=doubao-seed-1.6
PROCESSING_MODE=merge
MAX_WORKERS=5
WEB_HOST=0.0.0.0
WEB_PORT=8000
DEBUG=false
LOG_LEVEL=INFO
"""
    
    if not os.path.exists(".env"):
        with open(".env", "w") as f:
            f.write(env_content)
        print("✅ Created .env file")
    
    print("\n🎉 Quick fix completed!")
    print("Now try running: python main.py serve")


if __name__ == "__main__":
    main()
