#!/usr/bin/env python3
"""Quick fix script to install missing dependencies and start the application."""

import sys
import subprocess
import os


def install_package(package):
    """Install a single package."""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False


def main():
    """Quick fix main function."""
    print("🔧 Knowledge Graph Builder - Quick Setup")
    print("=" * 50)

    # List of essential packages
    essential_packages = [
        "rich>=13.0.0",
        "click>=8.1.0",
        "fastapi>=0.100.0",
        "uvicorn[standard]>=0.23.0",
        "python-dotenv>=1.0.0",
        "loguru>=0.7.0",
        "jinja2>=3.1.0",
        "python-multipart>=0.0.6",
        "aiofiles>=0.8.0",
        "httpx>=0.24.0",
        "networkx>=3.1",
        "beautifulsoup4>=4.12.0",
        "bleach>=6.0.0",
        "numpy>=1.21.0",
        "requests>=2.31.0"
    ]

    print("📦 Installing essential packages...")
    failed_packages = []

    for package in essential_packages:
        package_name = package.split(">=")[0]
        print(f"Installing {package_name}...")
        if install_package(package):
            print(f"✅ {package_name} installed")
        else:
            print(f"❌ Failed to install {package_name}")
            failed_packages.append(package_name)

    if failed_packages:
        print(f"\n⚠️  Failed to install: {', '.join(failed_packages)}")
        print("You may need to install them manually.")

    # Create basic .env file
    env_content = """# LLM Configuration
LLM_BASE_URL=https://gateway.chat.sensedeal.vip/v1
LLM_API_KEY=974fd8d1c155aa3d04b17bf253176b5e
LLM_MODEL_LOCAL=qwen2.5-32b-instruct-int4
LLM_MODEL_PERFORMANCE=doubao-seed-1.6

# Processing Configuration
PROCESSING_MODE=merge
MAX_WORKERS=5
ENTITY_SIMILARITY_THRESHOLD=0.85

# Web Configuration
WEB_HOST=0.0.0.0
WEB_PORT=8000
DEBUG=false

# Logging
LOG_LEVEL=INFO
"""

    if not os.path.exists(".env"):
        with open(".env", "w") as f:
            f.write(env_content)
        print("✅ Created .env file")
    else:
        print("✅ .env file already exists")

    # Test imports
    print("\n🧪 Testing installation...")
    try:
        import rich
        import click
        import fastapi
        import uvicorn
        print("✅ Core packages imported successfully")

        # Test project imports
        from config.settings import Settings
        from core.pipeline import Pipeline
        print("✅ Project modules imported successfully")

        print("\n🎉 Setup completed successfully!")
        print("\n📋 Next steps:")
        print("1. Edit .env file with your API keys if needed")
        print("2. Run the application:")
        print("   • CLI: python main.py process /path/to/documents")
        print("   • Web: python main.py serve")
        print("   • Simple Web: python web_simple.py")
        print("3. Open http://localhost:8000 in your browser")

        # Ask if user wants to start the web server
        try:
            response = input("\n❓ Start the web server now? (y/N): ").strip().lower()
            if response in ['y', 'yes']:
                print("\n🌐 Starting web server...")
                subprocess.run([sys.executable, 'web_simple.py'])
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")

    except ImportError as e:
        print(f"❌ Import test failed: {e}")
        print("Some dependencies may still be missing.")
        print("Try running: pip install -r requirements-working.txt")


if __name__ == "__main__":
    main()
