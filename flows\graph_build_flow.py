"""Graph building flow with mode-switching for batch vs merge strategies."""

from typing import Dict, Any

from pocketflow import AsyncFlow
from loguru import logger

from nodes.graph_nodes import GraphModeSelector, BuildGraphBatch, UpdateGlobalGraph


class GraphBuildFlow(AsyncFlow):
    """Mode-switching flow for graph creation strategy."""
    
    def __init__(self):
        super().__init__()
        
        # Create nodes
        self.mode_selector = GraphModeSelector()
        self.build_graph_batch = BuildGraphBatch()
        self.update_global_graph = UpdateGlobalGraph()
        
        # Set up flow
        self._setup_flow()
    
    def _setup_flow(self):
        """Set up the flow connections with mode switching."""
        
        # Start with mode selection
        self.start(self.mode_selector)
        
        # Connect mode selector to appropriate graph building strategy
        self.mode_selector - "batch_mode" >> self.build_graph_batch
        self.mode_selector - "merge_mode" >> self.update_global_graph
        
        # Both strategies can continue to the same next step
        # (This will be connected by the parent flow)
    
    async def prep_async(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare graph building flow.
        
        Args:
            shared: Shared data structure
            
        Returns:
            Preparation data
        """
        logger.info("Starting graph building flow")
        
        documents = shared.get("documents", [])
        
        if not documents:
            raise ValueError("No documents found for graph building")
        
        # Check for normalized entities
        total_entities = sum(len(doc.get("normalized_entities", [])) for doc in documents)
        total_relations = sum(len(doc.get("relations", [])) for doc in documents)
        
        if total_entities == 0:
            logger.warning("No entities found for graph building")
        
        context = shared.get("context", {})
        processing_mode = context.get("processing_mode", "merge")
        
        logger.info(f"Graph building mode: {processing_mode}")
        logger.info(f"Input: {total_entities} entities, {total_relations} relations from {len(documents)} documents")
        
        return {
            "processing_mode": processing_mode,
            "total_entities": total_entities,
            "total_relations": total_relations,
            "total_documents": len(documents),
            "graph_build_start_time": logger._core.clock()
        }
    
    async def post_async(
        self,
        shared: Dict[str, Any],
        prep_res: Dict[str, Any],
        exec_res: Any
    ) -> str:
        """
        Post-process graph building results.
        
        Args:
            shared: Shared data structure
            prep_res: Preparation results
            exec_res: Execution results
            
        Returns:
            Next action
        """
        graph_build_time = logger._core.clock() - prep_res["graph_build_start_time"]
        processing_mode = prep_res["processing_mode"]
        
        # Validate graph building results
        if processing_mode == "batch":
            individual_graphs = shared.get("individual_graphs", [])
            total_graphs = len(individual_graphs)
            
            logger.info(f"Batch graph building completed in {graph_build_time:.2f} seconds")
            logger.info(f"Created {total_graphs} individual graphs")
            
            # Calculate total nodes and edges across all graphs
            total_nodes = 0
            total_edges = 0
            
            for graph_info in individual_graphs:
                graph_dict = graph_info.get("graph", {})
                total_nodes += len(graph_dict.get("nodes", {}))
                total_edges += len(graph_dict.get("edges", []))
            
            logger.info(f"Total across all graphs: {total_nodes} nodes, {total_edges} edges")
            
        else:  # merge mode
            knowledge_graph = shared.get("knowledge_graph", {})
            total_nodes = len(knowledge_graph.get("nodes", {}))
            total_edges = len(knowledge_graph.get("edges", []))
            
            logger.info(f"Merge graph building completed in {graph_build_time:.2f} seconds")
            logger.info(f"Global graph: {total_nodes} nodes, {total_edges} edges")
        
        # Update task state
        task_state = shared.setdefault("task_state", {})
        task_state["graph_building_completed"] = True
        task_state["graph_build_time"] = graph_build_time
        task_state["graph_processing_mode"] = processing_mode
        
        if processing_mode == "batch":
            task_state["individual_graphs_count"] = len(shared.get("individual_graphs", []))
        else:
            task_state["global_graph_nodes"] = len(shared.get("knowledge_graph", {}).get("nodes", {}))
            task_state["global_graph_edges"] = len(shared.get("knowledge_graph", {}).get("edges", []))
        
        return "default"


class BatchGraphBuildFlow(AsyncFlow):
    """Specialized flow for batch graph building."""
    
    def __init__(self):
        super().__init__()
        
        # Only use batch building
        self.build_graph_batch = BuildGraphBatch()
        
        # Set up flow
        self._setup_flow()
    
    def _setup_flow(self):
        """Set up the batch flow."""
        
        # Start directly with batch building
        self.start(self.build_graph_batch)
    
    async def prep_async(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare batch graph building.
        
        Args:
            shared: Shared data structure
            
        Returns:
            Preparation data
        """
        logger.info("Starting batch graph building flow")
        
        documents = shared.get("documents", [])
        
        # Force batch mode in context
        context = shared.setdefault("context", {})
        context["processing_mode"] = "batch"
        context["selected_graph_mode"] = "batch"
        
        return {
            "total_documents": len(documents),
            "graph_build_start_time": logger._core.clock()
        }
    
    async def post_async(
        self,
        shared: Dict[str, Any],
        prep_res: Dict[str, Any],
        exec_res: Any
    ) -> str:
        """
        Post-process batch graph building results.
        
        Args:
            shared: Shared data structure
            prep_res: Preparation results
            exec_res: Execution results
            
        Returns:
            Next action
        """
        graph_build_time = logger._core.clock() - prep_res["graph_build_start_time"]
        
        individual_graphs = shared.get("individual_graphs", [])
        
        logger.info(f"Batch graph building completed in {graph_build_time:.2f} seconds")
        logger.info(f"Created {len(individual_graphs)} individual graphs")
        
        return "default"


class MergeGraphBuildFlow(AsyncFlow):
    """Specialized flow for merge graph building."""
    
    def __init__(self):
        super().__init__()
        
        # Only use merge building
        self.update_global_graph = UpdateGlobalGraph()
        
        # Set up flow
        self._setup_flow()
    
    def _setup_flow(self):
        """Set up the merge flow."""
        
        # Start directly with merge building
        self.start(self.update_global_graph)
    
    async def prep_async(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare merge graph building.
        
        Args:
            shared: Shared data structure
            
        Returns:
            Preparation data
        """
        logger.info("Starting merge graph building flow")
        
        documents = shared.get("documents", [])
        
        # Force merge mode in context
        context = shared.setdefault("context", {})
        context["processing_mode"] = "merge"
        context["selected_graph_mode"] = "merge"
        
        return {
            "total_documents": len(documents),
            "graph_build_start_time": logger._core.clock()
        }
    
    async def post_async(
        self,
        shared: Dict[str, Any],
        prep_res: Dict[str, Any],
        exec_res: Any
    ) -> str:
        """
        Post-process merge graph building results.
        
        Args:
            shared: Shared data structure
            prep_res: Preparation results
            exec_res: Execution results
            
        Returns:
            Next action
        """
        graph_build_time = logger._core.clock() - prep_res["graph_build_start_time"]
        
        knowledge_graph = shared.get("knowledge_graph", {})
        total_nodes = len(knowledge_graph.get("nodes", {}))
        total_edges = len(knowledge_graph.get("edges", []))
        
        logger.info(f"Merge graph building completed in {graph_build_time:.2f} seconds")
        logger.info(f"Global graph: {total_nodes} nodes, {total_edges} edges")
        
        return "default"


class GraphBuildFlowFactory:
    """Factory for creating graph building flows with different configurations."""
    
    @staticmethod
    def create_standard_flow() -> GraphBuildFlow:
        """Create standard graph building flow with mode selection."""
        return GraphBuildFlow()
    
    @staticmethod
    def create_batch_flow() -> BatchGraphBuildFlow:
        """Create batch-only graph building flow."""
        return BatchGraphBuildFlow()
    
    @staticmethod
    def create_merge_flow() -> MergeGraphBuildFlow:
        """Create merge-only graph building flow."""
        return MergeGraphBuildFlow()
    
    @staticmethod
    def create_flow_for_mode(mode: str) -> AsyncFlow:
        """
        Create graph building flow for specific mode.
        
        Args:
            mode: Processing mode ("batch" or "merge")
            
        Returns:
            Appropriate graph building flow
        """
        if mode == "batch":
            return GraphBuildFlowFactory.create_batch_flow()
        elif mode == "merge":
            return GraphBuildFlowFactory.create_merge_flow()
        else:
            return GraphBuildFlowFactory.create_standard_flow()
