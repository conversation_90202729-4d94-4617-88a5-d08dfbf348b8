"""
Graph building flow for creating knowledge graphs
Handles both batch and merge modes for graph construction
"""

import asyncio
from typing import Dict, List, Any, Optional
import networkx as nx
from loguru import logger

from .base_flow import BaseFlow, ConditionalFlow, SequentialFlow
from utils.graph_ops import GraphOperations, Entity, Relation
from utils.llm_client import LLMClient
from config.settings import Settings


class GraphBuildFlow(BaseFlow):
    """Main flow for building knowledge graphs"""
    
    def __init__(self, shared_context: Dict[str, Any],
                 processing_mode: str,
                 settings: Settings,
                 flow_id: Optional[str] = None):
        super().__init__(shared_context, flow_id or "GraphBuild")
        self.processing_mode = processing_mode
        self.settings = settings
        self.graph_ops = GraphOperations(
            similarity_threshold=settings.processing.entity_similarity_threshold,
            merge_strategy=settings.graph.merge_strategy
        )
    
    async def execute(self) -> Dict[str, Any]:
        """Execute graph building based on processing mode"""
        
        logger.info(f"Building knowledge graph in {self.processing_mode} mode")
        
        if self.processing_mode == "batch":
            return await self._build_batch_graphs()
        elif self.processing_mode == "merge":
            return await self._build_merged_graph()
        else:
            raise ValueError(f"Unknown processing mode: {self.processing_mode}")
    
    async def _build_batch_graphs(self) -> Dict[str, Any]:
        """Build separate graphs for each document"""
        
        logger.info("Building batch graphs (one per document)")
        
        batch_graphs = {}
        total_nodes = 0
        total_edges = 0
        
        for doc_index, document in enumerate(self.shared_context['documents']):
            try:
                # Build graph for this document
                graph = await self._build_document_graph(document, doc_index)
                
                # Store graph
                file_path = document['file_path']
                batch_graphs[file_path] = graph
                
                total_nodes += graph.number_of_nodes()
                total_edges += graph.number_of_edges()
                
                logger.debug(f"Built graph for {file_path}: "
                           f"{graph.number_of_nodes()} nodes, {graph.number_of_edges()} edges")
                
            except Exception as e:
                logger.error(f"Failed to build graph for document {doc_index}: {e}")
                continue
        
        # Store batch graphs in shared context
        self.shared_context['knowledge_graph']['batch_graphs'] = batch_graphs
        self.shared_context['knowledge_graph']['nodes'] = {}  # Not used in batch mode
        self.shared_context['knowledge_graph']['edges'] = []  # Not used in batch mode
        
        logger.info(f"Batch graphs completed: {len(batch_graphs)} graphs, "
                   f"{total_nodes} total nodes, {total_edges} total edges")
        
        return {
            'mode': 'batch',
            'graphs_count': len(batch_graphs),
            'total_nodes': total_nodes,
            'total_edges': total_edges,
            'graphs': batch_graphs
        }
    
    async def _build_merged_graph(self) -> Dict[str, Any]:
        """Build single merged graph from all documents"""
        
        logger.info("Building merged graph from all documents")
        
        # Collect all entities and relations
        all_entities = []
        all_relations = []
        
        for document in self.shared_context['documents']:
            # Convert dict entities back to Entity objects
            for entity_dict in document['normalized_entities']:
                entity = Entity(**entity_dict)
                all_entities.append(entity)
            
            # Convert dict relations back to Relation objects
            for relation_dict in document['relations']:
                relation = Relation(**relation_dict)
                all_relations.append(relation)
        
        logger.debug(f"Collected {len(all_entities)} entities and {len(all_relations)} relations")
        
        # Global entity normalization and merging
        merged_entities = await self._global_entity_normalization(all_entities)
        
        # Update relations with merged entity IDs
        updated_relations = await self._update_relations_with_merged_entities(
            all_relations, merged_entities
        )
        
        # Build NetworkX graph
        merged_graph = self.graph_ops.build_networkx_graph(merged_entities, updated_relations)
        
        # Enhance entities with additional descriptions and tags
        enhanced_graph = await self._enhance_graph_entities(merged_graph)
        
        # Store merged graph in shared context
        self._store_merged_graph(enhanced_graph, merged_entities, updated_relations)
        
        logger.info(f"Merged graph completed: {enhanced_graph.number_of_nodes()} nodes, "
                   f"{enhanced_graph.number_of_edges()} edges")
        
        return {
            'mode': 'merge',
            'nodes_count': enhanced_graph.number_of_nodes(),
            'edges_count': enhanced_graph.number_of_edges(),
            'graph': enhanced_graph
        }
    
    async def _build_document_graph(self, document: Dict[str, Any], doc_index: int) -> nx.DiGraph:
        """Build graph for a single document"""
        
        # Convert dict entities to Entity objects
        entities = []
        for entity_dict in document['normalized_entities']:
            entity = Entity(**entity_dict)
            entities.append(entity)
        
        # Convert dict relations to Relation objects
        relations = []
        for relation_dict in document['relations']:
            relation = Relation(**relation_dict)
            relations.append(relation)
        
        # Build NetworkX graph
        graph = self.graph_ops.build_networkx_graph(entities, relations)
        
        return graph
    
    async def _global_entity_normalization(self, all_entities: List[Entity]) -> List[Entity]:
        """Perform global entity normalization across all documents"""
        
        logger.debug("Performing global entity normalization")
        
        # Group entities by type for better matching
        entities_by_type = {}
        for entity in all_entities:
            entity_type = entity.type
            if entity_type not in entities_by_type:
                entities_by_type[entity_type] = []
            entities_by_type[entity_type].append(entity)
        
        # Normalize within each type
        normalized_entities = []
        
        for entity_type, type_entities in entities_by_type.items():
            logger.debug(f"Normalizing {len(type_entities)} entities of type '{entity_type}'")
            
            # Use graph operations to find and merge similar entities
            type_normalized = []
            
            for entity in type_entities:
                # Find similar entities in current normalized list
                similar = self.graph_ops.find_similar_entities(entity, type_normalized)
                
                if similar:
                    # Merge with most similar entity
                    most_similar, similarity = similar[0]
                    logger.debug(f"Merging entities: {entity.name} -> {most_similar.name} "
                               f"(similarity: {similarity:.3f})")
                    
                    # Remove the similar entity and add merged entity
                    type_normalized = [e for e in type_normalized if e.id != most_similar.id]
                    merged_entity = self.graph_ops.merge_entities(entity, most_similar)
                    type_normalized.append(merged_entity)
                else:
                    # Add as new entity
                    type_normalized.append(entity)
            
            normalized_entities.extend(type_normalized)
        
        logger.debug(f"Global normalization: {len(all_entities)} -> {len(normalized_entities)} entities")
        
        return normalized_entities
    
    async def _update_relations_with_merged_entities(self, all_relations: List[Relation],
                                                   merged_entities: List[Entity]) -> List[Relation]:
        """Update relations to use merged entity IDs"""
        
        # Create mapping from old entity IDs to new entity IDs
        entity_id_map = {}
        
        for entity in merged_entities:
            # Map canonical name to entity ID
            entity_id_map[entity.canonical_name] = entity.id
            
            # Map alternatives to entity ID
            for alt in entity.alternatives:
                canonical_alt = self.graph_ops.canonicalize_name(alt)
                entity_id_map[canonical_alt] = entity.id
        
        # Update relations
        updated_relations = []
        
        for relation in all_relations:
            # Find source and target entities
            source_entity = None
            target_entity = None
            
            for entity in merged_entities:
                if entity.id == relation.source:
                    source_entity = entity
                if entity.id == relation.target:
                    target_entity = entity
            
            if source_entity and target_entity:
                # Update relation with correct entity IDs
                updated_relation = Relation(
                    id=self.graph_ops.generate_relation_id(
                        source_entity.id, target_entity.id, relation.relation_type
                    ),
                    source=source_entity.id,
                    target=target_entity.id,
                    relation_type=relation.relation_type,
                    description=relation.description,
                    confidence=relation.confidence,
                    weight=relation.weight,
                    metadata=relation.metadata
                )
                updated_relations.append(updated_relation)
            else:
                logger.warning(f"Skipping relation with missing entities: {relation.id}")
        
        logger.debug(f"Updated {len(updated_relations)} relations with merged entity IDs")
        
        return updated_relations
    
    async def _enhance_graph_entities(self, graph: nx.DiGraph) -> nx.DiGraph:
        """Enhance entities with better descriptions and tags using LLM"""
        
        logger.info("Enhancing entity descriptions and tags")
        
        # Only enhance entities that need improvement
        entities_to_enhance = []
        
        for node_id, node_data in graph.nodes(data=True):
            description = node_data.get('description', '')
            tags = node_data.get('tags', [])
            
            # Enhance if description is short or no tags
            if len(description) < 50 or len(tags) < 2:
                entities_to_enhance.append((node_id, node_data))
        
        if not entities_to_enhance:
            logger.debug("No entities need enhancement")
            return graph
        
        logger.debug(f"Enhancing {len(entities_to_enhance)} entities")
        
        # Create LLM client for enhancement
        async with LLMClient(
            base_url=self.settings.llm.base_url,
            api_key=self.settings.llm.api_key,
            models=self.settings.llm.models,
            max_tokens=self.settings.llm.max_tokens,
            temperature=self.settings.llm.temperature
        ) as client:
            
            # Enhance entities in batches to avoid overwhelming the LLM
            batch_size = 5
            
            for i in range(0, len(entities_to_enhance), batch_size):
                batch = entities_to_enhance[i:i + batch_size]
                
                for node_id, node_data in batch:
                    try:
                        # Get context for entity (related entities)
                        context = self._get_entity_context(graph, node_id)
                        
                        # Enhance description if needed
                        if len(node_data.get('description', '')) < 50:
                            enhanced_description = await client.enhance_entity_description(
                                entity_name=node_data.get('name', ''),
                                entity_type=node_data.get('type', ''),
                                context=context
                            )
                            graph.nodes[node_id]['description'] = enhanced_description
                        
                        # Generate tags if needed
                        if len(node_data.get('tags', [])) < 2:
                            enhanced_tags = await client.generate_entity_tags(
                                entity_name=node_data.get('name', ''),
                                entity_type=node_data.get('type', ''),
                                description=graph.nodes[node_id].get('description', '')
                            )
                            graph.nodes[node_id]['tags'] = enhanced_tags
                        
                    except Exception as e:
                        logger.warning(f"Failed to enhance entity {node_id}: {e}")
                        continue
                
                # Small delay between batches
                await asyncio.sleep(0.5)
        
        logger.info("Entity enhancement completed")
        return graph
    
    def _get_entity_context(self, graph: nx.DiGraph, node_id: str) -> str:
        """Get context for entity from its relationships"""
        
        context_parts = []
        
        # Get outgoing relations
        for _, target, edge_data in graph.out_edges(node_id, data=True):
            target_name = graph.nodes[target].get('name', target)
            relation_type = edge_data.get('relation_type', '')
            context_parts.append(f"{relation_type} {target_name}")
        
        # Get incoming relations
        for source, _, edge_data in graph.in_edges(node_id, data=True):
            source_name = graph.nodes[source].get('name', source)
            relation_type = edge_data.get('relation_type', '')
            context_parts.append(f"{source_name} {relation_type}")
        
        return "; ".join(context_parts[:10])  # Limit context length
    
    def _store_merged_graph(self, graph: nx.DiGraph, entities: List[Entity], relations: List[Relation]):
        """Store merged graph in shared context"""
        
        # Convert to dictionary format for storage
        nodes_dict = {}
        for entity in entities:
            nodes_dict[entity.id] = entity.__dict__
        
        edges_list = []
        for relation in relations:
            edges_list.append(relation.__dict__)
        
        # Store in shared context
        self.shared_context['knowledge_graph']['nodes'] = nodes_dict
        self.shared_context['knowledge_graph']['edges'] = edges_list
        self.shared_context['knowledge_graph']['networkx_graph'] = graph
