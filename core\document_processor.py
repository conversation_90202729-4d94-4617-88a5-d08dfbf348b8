"""Document processing module."""

import asyncio
from pathlib import Path
from typing import List, Dict, Any, Optional
import re

from loguru import logger


class DocumentProcessor:
    """Process documents and extract text content."""
    
    def __init__(self):
        self.supported_formats = [".txt", ".md"]
    
    async def process_file(self, file_path: str) -> Dict[str, Any]:
        """Process a single file and extract content."""
        path = Path(file_path)
        
        if not path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        if path.suffix.lower() not in self.supported_formats:
            raise ValueError(f"Unsupported file format: {path.suffix}")
        
        try:
            content = await self._read_text_file(path)
            segments = self._split_content(content)
            
            return {
                "file_path": str(path),
                "file_name": path.name,
                "content": content,
                "segments": segments,
                "metadata": {
                    "size": path.stat().st_size,
                    "format": path.suffix.lower()
                }
            }
        except Exception as e:
            logger.error(f"Failed to process file {file_path}: {e}")
            raise
    
    async def _read_text_file(self, path: Path) -> str:
        """Read text file content."""
        try:
            import aiofiles
            async with aiofiles.open(path, 'r', encoding='utf-8') as f:
                return await f.read()
        except ImportError:
            # Fallback to synchronous reading
            return path.read_text(encoding='utf-8')
    
    def _split_content(self, content: str, max_length: int = 2000) -> List[Dict[str, Any]]:
        """Split content into manageable segments."""
        if len(content) <= max_length:
            return [{
                "text": content,
                "start": 0,
                "end": len(content),
                "segment_id": 0
            }]
        
        segments = []
        sentences = re.split(r'[.!?]+', content)
        
        current_segment = ""
        current_start = 0
        segment_id = 0
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
            
            if len(current_segment) + len(sentence) > max_length and current_segment:
                # Save current segment
                segments.append({
                    "text": current_segment.strip(),
                    "start": current_start,
                    "end": current_start + len(current_segment),
                    "segment_id": segment_id
                })
                
                # Start new segment
                current_start += len(current_segment)
                current_segment = sentence + ". "
                segment_id += 1
            else:
                current_segment += sentence + ". "
        
        # Add final segment
        if current_segment.strip():
            segments.append({
                "text": current_segment.strip(),
                "start": current_start,
                "end": current_start + len(current_segment),
                "segment_id": segment_id
            })
        
        return segments
    
    async def process_directory(self, directory_path: str) -> List[Dict[str, Any]]:
        """Process all supported files in a directory."""
        path = Path(directory_path)
        
        if not path.exists():
            raise FileNotFoundError(f"Directory not found: {directory_path}")
        
        if path.is_file():
            return [await self.process_file(str(path))]
        
        files = []
        for file_path in path.rglob("*"):
            if file_path.is_file() and file_path.suffix.lower() in self.supported_formats:
                files.append(str(file_path))
        
        if not files:
            logger.warning(f"No supported files found in {directory_path}")
            return []
        
        logger.info(f"Processing {len(files)} files from {directory_path}")
        
        # Process files concurrently
        tasks = [self.process_file(file_path) for file_path in files]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out exceptions
        processed_docs = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Failed to process {files[i]}: {result}")
            else:
                processed_docs.append(result)
        
        return processed_docs
