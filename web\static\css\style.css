/* Knowledge Graph Builder Custom Styles */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

/* Global Styles */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    border-radius: 15px !important;
    box-shadow: 0 10px 30px rgba(13, 110, 253, 0.3);
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
}

/* Cards */
.card {
    border: none;
    border-radius: 10px;
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
    border-bottom: none;
    font-weight: 600;
}

/* Feature Icons */
.feature-icon {
    padding: 20px;
    border-radius: 50%;
    background: rgba(13, 110, 253, 0.1);
    display: inline-block;
}

/* Navigation */
.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.2s ease-in-out;
}

.navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.btn-lg {
    padding: 12px 30px;
    font-size: 1.1rem;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Forms */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Progress Bars */
.progress {
    height: 10px;
    border-radius: 10px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 10px;
}

/* Alerts */
.alert {
    border: none;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Graph Visualization */
#graph-container {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    background: #fff;
    position: relative;
    overflow: hidden;
}

.graph-controls {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.9);
    padding: 10px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.graph-controls .btn {
    margin: 2px;
    padding: 5px 10px;
    font-size: 0.8rem;
}

/* Node Styles */
.node {
    cursor: pointer;
    stroke: #fff;
    stroke-width: 2px;
    transition: all 0.2s ease-in-out;
}

.node:hover {
    stroke-width: 3px;
    filter: brightness(1.1);
}

.node.selected {
    stroke: var(--warning-color);
    stroke-width: 4px;
}

/* Link Styles */
.link {
    stroke: #999;
    stroke-opacity: 0.6;
    stroke-width: 1px;
    transition: all 0.2s ease-in-out;
}

.link:hover {
    stroke-opacity: 1;
    stroke-width: 2px;
}

.link.highlighted {
    stroke: var(--primary-color);
    stroke-opacity: 1;
    stroke-width: 3px;
}

/* Node Labels */
.node-label {
    font-family: Arial, sans-serif;
    font-size: 12px;
    font-weight: 500;
    fill: var(--dark-color);
    text-anchor: middle;
    pointer-events: none;
    user-select: none;
}

/* Entity Type Colors */
.entity-person { fill: #ff6b6b; }
.entity-organization { fill: #4ecdc4; }
.entity-location { fill: #45b7d1; }
.entity-concept { fill: #96ceb4; }
.entity-event { fill: #feca57; }
.entity-product { fill: #ff9ff3; }
.entity-unknown { fill: #95a5a6; }

/* Search Results */
.search-result {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    transition: all 0.2s ease-in-out;
    cursor: pointer;
}

.search-result:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 10px rgba(13, 110, 253, 0.1);
}

.search-result-title {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.search-result-type {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    background: var(--light-color);
    color: var(--secondary-color);
    margin-right: 10px;
}

.search-result-description {
    color: var(--secondary-color);
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.search-result-score {
    font-size: 0.8rem;
    color: var(--info-color);
}

/* Entity Details */
.entity-detail {
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.entity-header {
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 15px;
    margin-bottom: 20px;
}

.entity-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 5px;
}

.entity-type-badge {
    display: inline-block;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    background: var(--primary-color);
    color: white;
}

.entity-description {
    font-size: 1rem;
    color: var(--secondary-color);
    line-height: 1.6;
    margin-bottom: 15px;
}

.entity-tags {
    margin-bottom: 15px;
}

.entity-tag {
    display: inline-block;
    padding: 3px 8px;
    margin: 2px;
    border-radius: 12px;
    font-size: 0.8rem;
    background: var(--light-color);
    color: var(--secondary-color);
}

/* Relations */
.relation-item {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 8px;
    background: #fff;
}

.relation-type {
    font-weight: 600;
    color: var(--primary-color);
    margin-right: 10px;
}

.relation-entity {
    font-weight: 500;
    color: var(--dark-color);
}

.relation-description {
    font-size: 0.9rem;
    color: var(--secondary-color);
    margin-top: 5px;
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(13, 110, 253, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        text-align: center;
        padding: 30px 20px !important;
    }
    
    .hero-section h1 {
        font-size: 2rem;
    }
    
    .feature-icon {
        padding: 15px;
    }
    
    .graph-controls {
        position: relative;
        top: auto;
        right: auto;
        margin-bottom: 10px;
        background: var(--light-color);
    }
    
    #graph-container {
        height: 400px !important;
    }
}

/* Footer */
footer {
    border-top: 1px solid #e9ecef;
    margin-top: auto;
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-soft {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08) !important;
}

.border-radius-lg {
    border-radius: 15px !important;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.5s ease-in-out;
}

@keyframes slideIn {
    from { opacity: 0; transform: translateX(-20px); }
    to { opacity: 1; transform: translateX(0); }
}
