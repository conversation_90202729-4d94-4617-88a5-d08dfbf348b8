"""
Post processing flow for output generation and cleanup
Handles wiki generation, graph persistence, and search indexing
"""

import asyncio
import json
from pathlib import Path
from typing import Dict, List, Any, Optional
import networkx as nx
from loguru import logger

from .base_flow import BaseFlow, ParallelFlow, SequentialFlow
from utils.wiki_renderer import WikiRenderer
from utils.index_connector import create_index_connector
from config.settings import Settings


class PostProcessingFlow(BaseFlow):
    """Flow for post-processing and output generation"""
    
    def __init__(self, shared_context: Dict[str, Any],
                 settings: Settings,
                 flow_id: Optional[str] = None):
        super().__init__(shared_context, flow_id or "PostProcessing")
        self.settings = settings
    
    async def execute(self) -> Dict[str, Any]:
        """Execute post-processing flow"""
        
        logger.info("Starting post-processing and output generation")
        
        # Get output configuration
        output_path = self.shared_context['context']['output_path']
        output_formats = self.shared_context['context'].get('output_formats', ['json', 'html'])
        
        # Create output directory
        output_path = Path(output_path)
        output_path.mkdir(parents=True, exist_ok=True)
        
        results = {}
        
        # Generate outputs based on requested formats
        if 'json' in output_formats:
            results['json'] = await self._generate_json_output(output_path)
        
        if 'html' in output_formats:
            results['html'] = await self._generate_html_wiki(output_path)
        
        if 'rdf' in output_formats:
            results['rdf'] = await self._generate_rdf_output(output_path)
        
        # Index in search backend if enabled
        if self.settings.output.search_enabled:
            results['search_index'] = await self._index_in_search_backend()
        
        # Persist to Neo4j if enabled
        if self.settings.storage.neo4j_enabled:
            results['neo4j'] = await self._persist_to_neo4j()
        
        # Clean temporary files
        await self._cleanup_temporary_files()
        
        # Store results in shared context
        self.shared_context['outputs'].update(results)
        
        logger.info(f"Post-processing completed: {list(results.keys())}")
        return results
    
    async def _generate_json_output(self, output_path: Path) -> str:
        """Generate JSON output"""
        
        logger.info("Generating JSON output")
        
        try:
            # Get graph data
            processing_mode = self.shared_context['context']['processing_mode']
            
            if processing_mode == 'batch':
                # Export batch graphs
                batch_graphs = self.shared_context['knowledge_graph'].get('batch_graphs', {})
                
                json_data = {
                    'mode': 'batch',
                    'graphs': {}
                }
                
                for file_path, graph in batch_graphs.items():
                    json_data['graphs'][file_path] = self._networkx_to_json(graph)
                
            else:
                # Export merged graph
                nodes = self.shared_context['knowledge_graph']['nodes']
                edges = self.shared_context['knowledge_graph']['edges']
                
                json_data = {
                    'mode': 'merge',
                    'nodes': nodes,
                    'edges': edges,
                    'statistics': {
                        'total_nodes': len(nodes),
                        'total_edges': len(edges)
                    }
                }
            
            # Add metadata
            json_data['metadata'] = {
                'generated_at': self._get_timestamp(),
                'source_files': [doc['file_path'] for doc in self.shared_context['documents']],
                'processing_settings': {
                    'mode': processing_mode,
                    'entity_similarity_threshold': self.settings.processing.entity_similarity_threshold,
                    'models_used': list(self.settings.llm.models.values())
                }
            }
            
            # Write JSON file
            json_path = output_path / 'knowledge_graph.json'
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"JSON output saved: {json_path}")
            return str(json_path)
            
        except Exception as e:
            logger.error(f"JSON generation failed: {e}")
            raise
    
    async def _generate_html_wiki(self, output_path: Path) -> str:
        """Generate HTML wiki"""
        
        logger.info("Generating HTML wiki")
        
        try:
            # Create wiki renderer
            wiki_renderer = WikiRenderer(
                template_dir=self.settings.output.wiki_template_dir,
                static_dir=self.settings.output.wiki_static_dir,
                output_dir=output_path / 'wiki'
            )
            
            # Get graph for wiki generation
            graph = self._get_graph_for_output()
            
            if graph is None:
                raise ValueError("No graph available for wiki generation")
            
            # Generate wiki site
            generated_files = wiki_renderer.generate_wiki_site(graph, output_path / 'wiki')
            
            logger.info(f"HTML wiki generated: {len(generated_files.get('entity_pages', {}))} entity pages")
            return str(output_path / 'wiki')
            
        except Exception as e:
            logger.error(f"HTML wiki generation failed: {e}")
            raise
    
    async def _generate_rdf_output(self, output_path: Path) -> str:
        """Generate RDF output"""
        
        logger.info("Generating RDF output")
        
        try:
            from rdflib import Graph, Namespace, Literal, URIRef
            from rdflib.namespace import RDF, RDFS
            
            # Create RDF graph
            rdf_graph = Graph()
            
            # Define namespaces
            KG = Namespace("http://example.org/kg/")
            rdf_graph.bind("kg", KG)
            
            # Get knowledge graph data
            nodes = self.shared_context['knowledge_graph']['nodes']
            edges = self.shared_context['knowledge_graph']['edges']
            
            # Add entities as RDF resources
            for node_id, node_data in nodes.items():
                entity_uri = KG[node_id]
                
                # Add type
                rdf_graph.add((entity_uri, RDF.type, KG[node_data.get('type', 'Entity')]))
                
                # Add properties
                rdf_graph.add((entity_uri, RDFS.label, Literal(node_data.get('name', ''))))
                
                if node_data.get('description'):
                    rdf_graph.add((entity_uri, RDFS.comment, Literal(node_data['description'])))
                
                # Add tags
                for tag in node_data.get('tags', []):
                    rdf_graph.add((entity_uri, KG.hasTag, Literal(tag)))
            
            # Add relations as RDF triples
            for edge in edges:
                source_uri = KG[edge['source']]
                target_uri = KG[edge['target']]
                relation_uri = KG[edge['relation_type'].replace(' ', '_')]
                
                rdf_graph.add((source_uri, relation_uri, target_uri))
            
            # Write RDF file
            rdf_path = output_path / 'knowledge_graph.ttl'
            rdf_graph.serialize(destination=str(rdf_path), format='turtle')
            
            logger.info(f"RDF output saved: {rdf_path}")
            return str(rdf_path)
            
        except ImportError:
            logger.warning("RDFLib not available, skipping RDF output")
            return ""
        except Exception as e:
            logger.error(f"RDF generation failed: {e}")
            raise
    
    async def _index_in_search_backend(self) -> str:
        """Index graph in search backend"""
        
        logger.info("Indexing in search backend")
        
        try:
            # Create search connector
            connector = create_index_connector(
                backend=self.settings.output.search_backend,
                hosts=self.settings.storage.elasticsearch_hosts,
                index_name=self.settings.output.search_index_name
            )
            
            # Get graph for indexing
            graph = self._get_graph_for_output()
            
            if graph is None:
                raise ValueError("No graph available for indexing")
            
            # Index graph
            success = await connector.index_graph(graph)
            
            if success:
                logger.info("Search indexing completed successfully")
                return f"Indexed in {self.settings.output.search_backend}"
            else:
                raise RuntimeError("Search indexing failed")
                
        except Exception as e:
            logger.error(f"Search indexing failed: {e}")
            raise
    
    async def _persist_to_neo4j(self) -> str:
        """Persist graph to Neo4j"""
        
        logger.info("Persisting to Neo4j")
        
        try:
            from neo4j import AsyncGraphDatabase
            
            # Create Neo4j driver
            driver = AsyncGraphDatabase.driver(
                self.settings.storage.neo4j_uri,
                auth=(self.settings.storage.neo4j_username, self.settings.storage.neo4j_password)
            )
            
            async with driver.session() as session:
                # Clear existing data
                await session.run("MATCH (n) DETACH DELETE n")
                
                # Get graph data
                nodes = self.shared_context['knowledge_graph']['nodes']
                edges = self.shared_context['knowledge_graph']['edges']
                
                # Create nodes
                for node_id, node_data in nodes.items():
                    query = """
                    CREATE (n:Entity {
                        id: $id,
                        name: $name,
                        type: $type,
                        description: $description,
                        tags: $tags,
                        confidence: $confidence
                    })
                    """
                    
                    await session.run(query, {
                        'id': node_id,
                        'name': node_data.get('name', ''),
                        'type': node_data.get('type', ''),
                        'description': node_data.get('description', ''),
                        'tags': node_data.get('tags', []),
                        'confidence': node_data.get('confidence', 1.0)
                    })
                
                # Create relationships
                for edge in edges:
                    query = """
                    MATCH (source:Entity {id: $source_id})
                    MATCH (target:Entity {id: $target_id})
                    CREATE (source)-[r:RELATED {
                        type: $relation_type,
                        description: $description,
                        confidence: $confidence
                    }]->(target)
                    """
                    
                    await session.run(query, {
                        'source_id': edge['source'],
                        'target_id': edge['target'],
                        'relation_type': edge['relation_type'],
                        'description': edge.get('description', ''),
                        'confidence': edge.get('confidence', 1.0)
                    })
            
            await driver.close()
            
            logger.info("Neo4j persistence completed")
            return "Persisted to Neo4j"
            
        except ImportError:
            logger.warning("Neo4j driver not available, skipping Neo4j persistence")
            return ""
        except Exception as e:
            logger.error(f"Neo4j persistence failed: {e}")
            raise
    
    async def _cleanup_temporary_files(self):
        """Clean up temporary files"""
        
        logger.debug("Cleaning up temporary files")
        
        try:
            # Clean up any temporary files created during processing
            # This is a placeholder - implement based on actual temp file usage
            pass
            
        except Exception as e:
            logger.warning(f"Cleanup failed: {e}")
    
    def _get_graph_for_output(self) -> Optional[nx.DiGraph]:
        """Get graph for output generation"""
        
        processing_mode = self.shared_context['context']['processing_mode']
        
        if processing_mode == 'batch':
            # For batch mode, create a combined graph for output
            batch_graphs = self.shared_context['knowledge_graph'].get('batch_graphs', {})
            
            if not batch_graphs:
                return None
            
            # Combine all batch graphs
            combined_graph = nx.DiGraph()
            
            for file_path, graph in batch_graphs.items():
                # Add nodes with file prefix to avoid conflicts
                for node_id, node_data in graph.nodes(data=True):
                    prefixed_id = f"{Path(file_path).stem}_{node_id}"
                    combined_graph.add_node(prefixed_id, **node_data)
                
                # Add edges with prefixed node IDs
                for source, target, edge_data in graph.edges(data=True):
                    prefixed_source = f"{Path(file_path).stem}_{source}"
                    prefixed_target = f"{Path(file_path).stem}_{target}"
                    combined_graph.add_edge(prefixed_source, prefixed_target, **edge_data)
            
            return combined_graph
            
        else:
            # For merge mode, use the stored NetworkX graph
            return self.shared_context['knowledge_graph'].get('networkx_graph')
    
    def _networkx_to_json(self, graph: nx.DiGraph) -> Dict[str, Any]:
        """Convert NetworkX graph to JSON format"""
        
        nodes = []
        for node_id, node_data in graph.nodes(data=True):
            node_dict = {'id': node_id}
            node_dict.update(node_data)
            nodes.append(node_dict)
        
        edges = []
        for source, target, edge_data in graph.edges(data=True):
            edge_dict = {
                'source': source,
                'target': target
            }
            edge_dict.update(edge_data)
            edges.append(edge_dict)
        
        return {
            'nodes': nodes,
            'edges': edges
        }
    
    def _get_timestamp(self) -> str:
        """Get current timestamp"""
        from datetime import datetime
        return datetime.now().isoformat()
