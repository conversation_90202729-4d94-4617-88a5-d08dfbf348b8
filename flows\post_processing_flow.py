"""Post-processing flow for output generation and cleanup."""

from typing import Dict, Any

from pocketflow import AsyncFlow
from loguru import logger

from nodes.output_nodes import GenerateWikiPages, PersistKnowledgeGraph, CleanTemporaryFiles


class PostProcessingFlow(AsyncFlow):
    """Final steps including cleanup and output generation."""
    
    def __init__(self, enable_cleanup: bool = True):
        super().__init__()
        
        self.enable_cleanup = enable_cleanup
        
        # Create nodes
        self.generate_wiki = GenerateWikiPages()
        self.persist_graph = PersistKnowledgeGraph()
        
        if enable_cleanup:
            self.clean_temp = CleanTemporaryFiles()
        else:
            self.clean_temp = None
        
        # Set up flow
        self._setup_flow()
    
    def _setup_flow(self):
        """Set up the flow connections between nodes."""
        
        # Start with wiki generation
        self.start(self.generate_wiki)
        
        # Connect to graph persistence
        self.generate_wiki >> self.persist_graph
        
        # Connect to cleanup if enabled
        if self.clean_temp:
            self.persist_graph >> self.clean_temp
    
    async def prep_async(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare post-processing flow.
        
        Args:
            shared: Shared data structure
            
        Returns:
            Preparation data
        """
        logger.info("Starting post-processing flow")
        
        # Validate that we have a knowledge graph to process
        knowledge_graph = shared.get("knowledge_graph", {})
        individual_graphs = shared.get("individual_graphs", [])
        
        has_global_graph = bool(knowledge_graph.get("nodes"))
        has_individual_graphs = bool(individual_graphs)
        
        if not has_global_graph and not has_individual_graphs:
            raise ValueError("No knowledge graphs found for post-processing")
        
        # Determine processing mode
        context = shared.get("context", {})
        processing_mode = context.get("selected_graph_mode", context.get("processing_mode", "merge"))
        
        # Count entities and relations
        if processing_mode == "batch" and has_individual_graphs:
            total_nodes = sum(len(graph["graph"].get("nodes", {})) for graph in individual_graphs)
            total_edges = sum(len(graph["graph"].get("edges", [])) for graph in individual_graphs)
            graph_count = len(individual_graphs)
        else:
            total_nodes = len(knowledge_graph.get("nodes", {}))
            total_edges = len(knowledge_graph.get("edges", []))
            graph_count = 1 if has_global_graph else 0
        
        logger.info(f"Post-processing {graph_count} graph(s) with {total_nodes} nodes and {total_edges} edges")
        
        return {
            "processing_mode": processing_mode,
            "total_nodes": total_nodes,
            "total_edges": total_edges,
            "graph_count": graph_count,
            "has_global_graph": has_global_graph,
            "has_individual_graphs": has_individual_graphs,
            "post_processing_start_time": logger._core.clock()
        }
    
    async def post_async(
        self,
        shared: Dict[str, Any],
        prep_res: Dict[str, Any],
        exec_res: Any
    ) -> str:
        """
        Post-process the post-processing results.
        
        Args:
            shared: Shared data structure
            prep_res: Preparation results
            exec_res: Execution results
            
        Returns:
            Next action
        """
        post_processing_time = logger._core.clock() - prep_res["post_processing_start_time"]
        
        # Validate outputs
        outputs = shared.get("outputs", {})
        
        wiki_pages = outputs.get("wiki_pages", {})
        json_graph = outputs.get("json_graph", "")
        search_index = outputs.get("search_index", "")
        
        # Count successful outputs
        output_count = 0
        output_types = []
        
        if wiki_pages:
            output_count += len(wiki_pages)
            output_types.append(f"wiki ({len(wiki_pages)} pages)")
        
        if json_graph:
            output_count += 1
            output_types.append("json")
        
        if search_index:
            output_count += 1
            output_types.append("search_index")
        
        logger.info(f"Post-processing completed in {post_processing_time:.2f} seconds")
        logger.info(f"Generated outputs: {', '.join(output_types) if output_types else 'none'}")
        
        # Update task state
        task_state = shared.setdefault("task_state", {})
        task_state["post_processing_completed"] = True
        task_state["post_processing_time"] = post_processing_time
        task_state["output_count"] = output_count
        task_state["output_types"] = output_types
        
        # Check cleanup status
        if self.enable_cleanup:
            cleanup_completed = task_state.get("cleanup_completed", False)
            if cleanup_completed:
                logger.info("Cleanup completed successfully")
            else:
                logger.warning("Cleanup may have failed")
        
        return "default"


class MinimalPostProcessingFlow(AsyncFlow):
    """Minimal post-processing flow with only essential outputs."""
    
    def __init__(self):
        super().__init__()
        
        # Only essential nodes
        self.persist_graph = PersistKnowledgeGraph()
        
        # Set up flow
        self._setup_flow()
    
    def _setup_flow(self):
        """Set up the minimal flow."""
        
        # Start and end with graph persistence
        self.start(self.persist_graph)
    
    async def prep_async(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare minimal post-processing.
        
        Args:
            shared: Shared data structure
            
        Returns:
            Preparation data
        """
        logger.info("Starting minimal post-processing flow")
        
        return {
            "post_processing_start_time": logger._core.clock()
        }
    
    async def post_async(
        self,
        shared: Dict[str, Any],
        prep_res: Dict[str, Any],
        exec_res: Any
    ) -> str:
        """
        Post-process minimal results.
        
        Args:
            shared: Shared data structure
            prep_res: Preparation results
            exec_res: Execution results
            
        Returns:
            Next action
        """
        post_processing_time = logger._core.clock() - prep_res["post_processing_start_time"]
        
        logger.info(f"Minimal post-processing completed in {post_processing_time:.2f} seconds")
        
        return "default"


class ComprehensivePostProcessingFlow(AsyncFlow):
    """Comprehensive post-processing flow with all features enabled."""
    
    def __init__(self):
        super().__init__()
        
        # All nodes
        self.generate_wiki = GenerateWikiPages()
        self.persist_graph = PersistKnowledgeGraph()
        self.clean_temp = CleanTemporaryFiles()
        
        # Set up flow
        self._setup_flow()
    
    def _setup_flow(self):
        """Set up the comprehensive flow."""
        
        # Start with wiki generation
        self.start(self.generate_wiki)
        
        # Connect all nodes
        self.generate_wiki >> self.persist_graph
        self.persist_graph >> self.clean_temp
    
    async def prep_async(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare comprehensive post-processing.
        
        Args:
            shared: Shared data structure
            
        Returns:
            Preparation data
        """
        logger.info("Starting comprehensive post-processing flow")
        
        # Validate all required data
        knowledge_graph = shared.get("knowledge_graph", {})
        
        if not knowledge_graph.get("nodes"):
            raise ValueError("No knowledge graph found for comprehensive post-processing")
        
        # Estimate processing time
        node_count = len(knowledge_graph["nodes"])
        estimated_time = node_count * 0.1 + 30  # Rough estimate
        
        logger.info(f"Comprehensive processing of {node_count} nodes (estimated: {estimated_time:.1f}s)")
        
        return {
            "node_count": node_count,
            "estimated_time": estimated_time,
            "post_processing_start_time": logger._core.clock()
        }
    
    async def post_async(
        self,
        shared: Dict[str, Any],
        prep_res: Dict[str, Any],
        exec_res: Any
    ) -> str:
        """
        Post-process comprehensive results.
        
        Args:
            shared: Shared data structure
            prep_res: Preparation results
            exec_res: Execution results
            
        Returns:
            Next action
        """
        actual_time = logger._core.clock() - prep_res["post_processing_start_time"]
        estimated_time = prep_res["estimated_time"]
        
        # Calculate comprehensive metrics
        outputs = shared.get("outputs", {})
        
        wiki_pages = len(outputs.get("wiki_pages", {}))
        has_json = bool(outputs.get("json_graph"))
        has_search = bool(outputs.get("search_index"))
        
        completeness_score = (wiki_pages > 0) + has_json + has_search
        max_score = 3
        
        logger.info(f"Comprehensive post-processing completed:")
        logger.info(f"  Time: {actual_time:.2f}s (estimated: {estimated_time:.2f}s)")
        logger.info(f"  Wiki pages: {wiki_pages}")
        logger.info(f"  JSON export: {'✓' if has_json else '✗'}")
        logger.info(f"  Search index: {'✓' if has_search else '✗'}")
        logger.info(f"  Completeness: {completeness_score}/{max_score}")
        
        # Store comprehensive metrics
        task_state = shared.setdefault("task_state", {})
        task_state["comprehensive_post_processing_metrics"] = {
            "actual_time": actual_time,
            "estimated_time": estimated_time,
            "wiki_pages": wiki_pages,
            "has_json": has_json,
            "has_search": has_search,
            "completeness_score": completeness_score / max_score
        }
        
        return "default"


class PostProcessingFlowFactory:
    """Factory for creating post-processing flows with different configurations."""
    
    @staticmethod
    def create_standard_flow(enable_cleanup: bool = True) -> PostProcessingFlow:
        """Create standard post-processing flow."""
        return PostProcessingFlow(enable_cleanup=enable_cleanup)
    
    @staticmethod
    def create_minimal_flow() -> MinimalPostProcessingFlow:
        """Create minimal post-processing flow."""
        return MinimalPostProcessingFlow()
    
    @staticmethod
    def create_comprehensive_flow() -> ComprehensivePostProcessingFlow:
        """Create comprehensive post-processing flow."""
        return ComprehensivePostProcessingFlow()
    
    @staticmethod
    def create_custom_flow(
        enable_wiki: bool = True,
        enable_persistence: bool = True,
        enable_cleanup: bool = True
    ) -> PostProcessingFlow:
        """
        Create custom post-processing flow.
        
        Args:
            enable_wiki: Whether to enable wiki generation
            enable_persistence: Whether to enable graph persistence
            enable_cleanup: Whether to enable cleanup
            
        Returns:
            Configured post-processing flow
        """
        # For now, return standard flow
        # TODO: Implement custom configuration
        return PostProcessingFlow(enable_cleanup=enable_cleanup)
