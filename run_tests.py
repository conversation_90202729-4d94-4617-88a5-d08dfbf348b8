#!/usr/bin/env python3
"""Test runner script for Knowledge Graph Builder."""

import sys
import subprocess
import argparse
from pathlib import Path


def run_command(cmd, description):
    """Run a command and handle errors."""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {' '.join(cmd)}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        return True
    except subprocess.CalledProcessError as e:
        print(f"ERROR: {description} failed with exit code {e.returncode}")
        print("STDOUT:", e.stdout)
        print("STDERR:", e.stderr)
        return False


def main():
    """Main test runner."""
    parser = argparse.ArgumentParser(description="Run tests for Knowledge Graph Builder")
    parser.add_argument("--unit", action="store_true", help="Run unit tests only")
    parser.add_argument("--integration", action="store_true", help="Run integration tests only")
    parser.add_argument("--web", action="store_true", help="Run web tests only")
    parser.add_argument("--coverage", action="store_true", help="Generate coverage report")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--fast", action="store_true", help="Skip slow tests")
    parser.add_argument("--parallel", "-n", type=int, help="Number of parallel workers")
    
    args = parser.parse_args()
    
    # Base pytest command
    pytest_cmd = ["python", "-m", "pytest"]
    
    # Add verbosity
    if args.verbose:
        pytest_cmd.append("-v")
    else:
        pytest_cmd.append("-q")
    
    # Add parallel execution
    if args.parallel:
        pytest_cmd.extend(["-n", str(args.parallel)])
    
    # Add coverage if requested
    if args.coverage:
        pytest_cmd.extend([
            "--cov=.",
            "--cov-report=term-missing",
            "--cov-report=html:htmlcov",
            "--cov-fail-under=70"
        ])
    
    # Skip slow tests if requested
    if args.fast:
        pytest_cmd.extend(["-m", "not slow"])
    
    # Determine which tests to run
    test_paths = []
    
    if args.unit:
        test_paths.extend([
            "tests/test_config.py",
            "tests/test_utils.py",
            "tests/test_nodes.py"
        ])
    elif args.integration:
        test_paths.append("tests/test_integration.py")
    elif args.web:
        test_paths.append("tests/test_web.py")
    else:
        # Run all tests
        test_paths.append("tests/")
    
    pytest_cmd.extend(test_paths)
    
    # Run the tests
    success = run_command(pytest_cmd, "Running tests")
    
    if not success:
        print("\n❌ Tests failed!")
        return 1
    
    print("\n✅ All tests passed!")
    
    # Additional checks
    if args.coverage:
        print("\n📊 Coverage report generated in htmlcov/index.html")
    
    # Run linting if available
    try:
        import flake8
        lint_cmd = ["python", "-m", "flake8", ".", "--max-line-length=100", "--ignore=E203,W503"]
        if run_command(lint_cmd, "Running linting"):
            print("✅ Linting passed!")
        else:
            print("⚠️  Linting issues found")
    except ImportError:
        print("ℹ️  Flake8 not installed, skipping linting")
    
    # Run type checking if available
    try:
        import mypy
        type_cmd = ["python", "-m", "mypy", ".", "--ignore-missing-imports"]
        if run_command(type_cmd, "Running type checking"):
            print("✅ Type checking passed!")
        else:
            print("⚠️  Type checking issues found")
    except ImportError:
        print("ℹ️  MyPy not installed, skipping type checking")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
