"""Processing nodes for LLM-based entity and relation extraction."""

import asyncio
from typing import Dict, List, Any, Optional, Tuple

from pocketflow import Async<PERSON><PERSON><PERSON>lBatchNode, BatchNode
from loguru import logger

from utils.llm_client import LLMClient
from utils.security_filters import SecurityFilters
from config.settings import Settings


class ProcessSegmentBatch(AsyncParallelBatchNode):
    """Process document segments in parallel using LLM for entity/relation extraction."""
    
    def __init__(self, max_concurrent: int = 5, timeout: int = 300):
        super().__init__(max_retries=3, wait=5)
        self.max_concurrent = max_concurrent
        self.timeout = timeout
        self.settings = Settings()
        self.security_filters = SecurityFilters()
    
    async def prep_async(self, shared: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Prepare segments for parallel processing.
        
        Args:
            shared: Shared data structure
            
        Returns:
            List of segment processing tasks
        """
        documents = shared.get("documents", [])
        
        if not documents:
            raise ValueError("No documents found for segment processing")
        
        # Collect all segments from all documents
        segment_tasks = []
        
        for doc_idx, document in enumerate(documents):
            segments = document.get("content_segments", [])
            
            for seg_idx, segment in enumerate(segments):
                task = {
                    "document_index": doc_idx,
                    "segment_index": seg_idx,
                    "segment_text": segment["text"],
                    "file_path": document["file_path"],
                    "file_type": document["file_type"],
                    "model_index": len(segment_tasks)  # For model routing
                }
                segment_tasks.append(task)
        
        logger.info(f"Prepared {len(segment_tasks)} segments for parallel processing")
        return segment_tasks
    
    async def exec_async(self, segment_task: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a single segment using LLM.
        
        Args:
            segment_task: Segment processing task
            
        Returns:
            Extracted entities and relations
        """
        segment_text = segment_task["segment_text"]
        model_index = segment_task["model_index"]
        
        # Select model based on index for load balancing
        model = self.settings.get_model_for_index(model_index)
        
        # Security check
        filter_result = self.security_filters.filter_content(
            segment_text,
            redact_pii=False,  # Already done in parsing
            sanitize_html=False,
            validate_input=True
        )
        
        if not filter_result["is_safe"]:
            logger.warning(f"Segment failed security check: {filter_result['warnings']}")
            return {
                "document_index": segment_task["document_index"],
                "segment_index": segment_task["segment_index"],
                "entities": [],
                "relations": [],
                "error": "Security check failed"
            }
        
        try:
            # Use LLM client for extraction
            async with LLMClient(self.settings) as llm_client:
                result = await llm_client.extract_entities_and_relations(
                    text=segment_text,
                    model=model
                )
                
                # Add source information to entities and relations
                for entity in result.get("entities", []):
                    entity["source_file"] = segment_task["file_path"]
                    entity["source_segment"] = segment_task["segment_index"]
                
                for relation in result.get("relations", []):
                    relation["source_file"] = segment_task["file_path"]
                    relation["source_segment"] = segment_task["segment_index"]
                
                logger.debug(f"Processed segment {segment_task['segment_index']} from {segment_task['file_path']}: "
                           f"{len(result.get('entities', []))} entities, {len(result.get('relations', []))} relations")
                
                return {
                    "document_index": segment_task["document_index"],
                    "segment_index": segment_task["segment_index"],
                    "entities": result.get("entities", []),
                    "relations": result.get("relations", []),
                    "model_used": model,
                    "error": None
                }
                
        except Exception as e:
            logger.error(f"Failed to process segment {segment_task['segment_index']} "
                        f"from {segment_task['file_path']}: {e}")
            
            return {
                "document_index": segment_task["document_index"],
                "segment_index": segment_task["segment_index"],
                "entities": [],
                "relations": [],
                "error": str(e)
            }
    
    async def exec_fallback_async(self, segment_task: Dict[str, Any], exc: Exception) -> Dict[str, Any]:
        """
        Fallback execution when segment processing fails.
        
        Args:
            segment_task: Segment processing task
            exc: Exception that caused failure
            
        Returns:
            Empty result with error information
        """
        logger.error(f"Segment processing fallback for {segment_task['file_path']}: {exc}")
        
        return {
            "document_index": segment_task["document_index"],
            "segment_index": segment_task["segment_index"],
            "entities": [],
            "relations": [],
            "error": f"Fallback: {str(exc)}"
        }
    
    async def post_async(
        self,
        shared: Dict[str, Any],
        prep_res: List[Dict[str, Any]],
        exec_res: List[Dict[str, Any]]
    ) -> str:
        """
        Post-process segment results.
        
        Args:
            shared: Shared data structure
            prep_res: Preparation results
            exec_res: Execution results
            
        Returns:
            Next action
        """
        # Group results by document
        documents = shared.get("documents", [])
        
        # Initialize raw entities and relations for each document
        for document in documents:
            document["raw_entities"] = []
            document["relations"] = []
        
        # Process results
        successful_segments = 0
        failed_segments = 0
        
        for result in exec_res:
            doc_idx = result["document_index"]
            seg_idx = result["segment_index"]
            
            if result["error"]:
                failed_segments += 1
                # Track failed segments
                task_state = shared.setdefault("task_state", {})
                failed_segs = task_state.setdefault("failed_segments", {})
                file_path = documents[doc_idx]["file_path"]
                failed_segs.setdefault(file_path, []).append(seg_idx)
            else:
                successful_segments += 1
                # Add entities and relations to document
                documents[doc_idx]["raw_entities"].extend(result["entities"])
                documents[doc_idx]["relations"].extend(result["relations"])
        
        logger.info(f"Segment processing completed: {successful_segments} successful, {failed_segments} failed")
        
        # Update shared data
        shared["documents"] = documents
        
        return "default"


class MergeSegmentResults(BatchNode):
    """Merge and verify results from parallel segment processing."""
    
    def __init__(self):
        super().__init__()
    
    def prep(self, shared: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Prepare documents for result merging.
        
        Args:
            shared: Shared data structure
            
        Returns:
            List of documents to process
        """
        documents = shared.get("documents", [])
        
        if not documents:
            raise ValueError("No documents found for result merging")
        
        return documents
    
    def exec(self, document: Dict[str, Any]) -> Dict[str, Any]:
        """
        Merge and validate results for a single document.
        
        Args:
            document: Document with raw entities and relations
            
        Returns:
            Document with merged and validated results
        """
        raw_entities = document.get("raw_entities", [])
        relations = document.get("relations", [])
        
        # Basic validation and cleanup
        validated_entities = []
        validated_relations = []
        
        # Validate entities
        seen_entities = set()
        for entity in raw_entities:
            name = entity.get("name", "").strip()
            entity_type = entity.get("type", "unknown").strip()
            
            if not name or len(name) < 2:
                continue  # Skip invalid entities
            
            # Avoid duplicates within the same document
            entity_key = (name.lower(), entity_type.lower())
            if entity_key in seen_entities:
                continue
            
            seen_entities.add(entity_key)
            
            # Clean and validate entity
            validated_entity = {
                "name": name,
                "type": entity_type,
                "description": entity.get("description", "").strip(),
                "source_file": entity.get("source_file", document["file_path"]),
                "source_segment": entity.get("source_segment", 0)
            }
            
            validated_entities.append(validated_entity)
        
        # Validate relations
        entity_names = {entity["name"].lower() for entity in validated_entities}
        
        for relation in relations:
            source = relation.get("source", "").strip()
            target = relation.get("target", "").strip()
            relation_type = relation.get("relation", "").strip()
            
            if not all([source, target, relation_type]):
                continue  # Skip invalid relations
            
            # Check if both entities exist
            if source.lower() not in entity_names or target.lower() not in entity_names:
                continue  # Skip relations with missing entities
            
            validated_relation = {
                "source": source,
                "target": target,
                "relation": relation_type,
                "description": relation.get("description", "").strip(),
                "source_file": relation.get("source_file", document["file_path"]),
                "source_segment": relation.get("source_segment", 0)
            }
            
            validated_relations.append(validated_relation)
        
        # Update document
        document["raw_entities"] = validated_entities
        document["relations"] = validated_relations
        
        logger.info(f"Merged results for {document['file_path']}: "
                   f"{len(validated_entities)} entities, {len(validated_relations)} relations")
        
        return document
    
    def post(
        self,
        shared: Dict[str, Any],
        prep_res: List[Dict[str, Any]],
        exec_res: List[Dict[str, Any]]
    ) -> str:
        """
        Post-process merging results.
        
        Args:
            shared: Shared data structure
            prep_res: Preparation results
            exec_res: Execution results
            
        Returns:
            Next action
        """
        # Update documents in shared data
        shared["documents"] = exec_res
        
        # Calculate statistics
        total_entities = sum(len(doc["raw_entities"]) for doc in exec_res)
        total_relations = sum(len(doc["relations"]) for doc in exec_res)
        
        logger.info(f"Result merging completed: {total_entities} total entities, {total_relations} total relations")
        
        return "default"
