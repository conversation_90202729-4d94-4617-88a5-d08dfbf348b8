"""Security filters for PII detection, XSS sanitization, and input validation."""

import re
import html
from typing import List, Dict, Set, Optional, Tuple
from dataclasses import dataclass

from loguru import logger


@dataclass
class PIIDetectionResult:
    """Result of PII detection."""
    has_pii: bool
    detected_types: List[str]
    redacted_text: str
    confidence_scores: Dict[str, float]


class SecurityFilters:
    """Security filters for protecting sensitive information and preventing attacks."""
    
    # PII patterns
    PII_PATTERNS = {
        'email': {
            'pattern': r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            'replacement': '[EMAIL_REDACTED]'
        },
        'phone': {
            'pattern': r'(\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})',
            'replacement': '[PHONE_REDACTED]'
        },
        'ssn': {
            'pattern': r'\b\d{3}-?\d{2}-?\d{4}\b',
            'replacement': '[SSN_REDACTED]'
        },
        'credit_card': {
            'pattern': r'\b(?:\d{4}[-\s]?){3}\d{4}\b',
            'replacement': '[CARD_REDACTED]'
        },
        'ip_address': {
            'pattern': r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b',
            'replacement': '[IP_REDACTED]'
        },
        'chinese_id': {
            'pattern': r'\b\d{17}[\dXx]\b',
            'replacement': '[ID_REDACTED]'
        },
        'chinese_phone': {
            'pattern': r'\b1[3-9]\d{9}\b',
            'replacement': '[PHONE_REDACTED]'
        }
    }
    
    # XSS patterns
    XSS_PATTERNS = [
        r'<script[^>]*>.*?</script>',
        r'javascript:',
        r'on\w+\s*=',
        r'<iframe[^>]*>.*?</iframe>',
        r'<object[^>]*>.*?</object>',
        r'<embed[^>]*>',
        r'<link[^>]*>',
        r'<meta[^>]*>',
        r'<style[^>]*>.*?</style>'
    ]
    
    # Dangerous HTML tags
    DANGEROUS_TAGS = {
        'script', 'iframe', 'object', 'embed', 'link', 'meta', 'style',
        'form', 'input', 'button', 'textarea', 'select', 'option'
    }
    
    def __init__(self, enable_pii_detection: bool = True):
        self.enable_pii_detection = enable_pii_detection
        self.compiled_pii_patterns = {}
        self.compiled_xss_patterns = []
        
        # Compile PII patterns
        for pii_type, config in self.PII_PATTERNS.items():
            self.compiled_pii_patterns[pii_type] = re.compile(config['pattern'], re.IGNORECASE)
        
        # Compile XSS patterns
        for pattern in self.XSS_PATTERNS:
            self.compiled_xss_patterns.append(re.compile(pattern, re.IGNORECASE | re.DOTALL))
    
    def detect_pii(self, text: str) -> PIIDetectionResult:
        """
        Detect personally identifiable information in text.
        
        Args:
            text: Text to analyze
            
        Returns:
            PIIDetectionResult with detection results
        """
        if not self.enable_pii_detection:
            return PIIDetectionResult(
                has_pii=False,
                detected_types=[],
                redacted_text=text,
                confidence_scores={}
            )
        
        detected_types = []
        confidence_scores = {}
        redacted_text = text
        
        for pii_type, pattern in self.compiled_pii_patterns.items():
            matches = pattern.findall(text)
            
            if matches:
                detected_types.append(pii_type)
                confidence_scores[pii_type] = min(1.0, len(matches) * 0.3)  # Simple confidence scoring
                
                # Redact the PII
                replacement = self.PII_PATTERNS[pii_type]['replacement']
                redacted_text = pattern.sub(replacement, redacted_text)
                
                logger.debug(f"Detected {pii_type} PII: {len(matches)} instances")
        
        has_pii = len(detected_types) > 0
        
        if has_pii:
            logger.warning(f"PII detected in text: {detected_types}")
        
        return PIIDetectionResult(
            has_pii=has_pii,
            detected_types=detected_types,
            redacted_text=redacted_text,
            confidence_scores=confidence_scores
        )
    
    def sanitize_html(self, text: str, allow_basic_formatting: bool = True) -> str:
        """
        Sanitize HTML content to prevent XSS attacks.
        
        Args:
            text: HTML text to sanitize
            allow_basic_formatting: Whether to allow basic formatting tags
            
        Returns:
            Sanitized HTML text
        """
        if not text:
            return text
        
        # Remove dangerous scripts and content
        sanitized = text
        
        for pattern in self.compiled_xss_patterns:
            sanitized = pattern.sub('', sanitized)
        
        # Remove dangerous attributes
        sanitized = re.sub(r'on\w+\s*=\s*["\'][^"\']*["\']', '', sanitized, flags=re.IGNORECASE)
        sanitized = re.sub(r'javascript:', '', sanitized, flags=re.IGNORECASE)
        
        if allow_basic_formatting:
            # Allow basic formatting tags
            allowed_tags = {'p', 'br', 'strong', 'b', 'em', 'i', 'u', 'ul', 'ol', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'}
            
            # Remove dangerous tags while keeping allowed ones
            def replace_tag(match):
                tag = match.group(1).lower()
                if tag in self.DANGEROUS_TAGS:
                    return ''
                elif tag in allowed_tags:
                    return match.group(0)
                else:
                    return html.escape(match.group(0))
            
            sanitized = re.sub(r'<(/?)(\w+)[^>]*>', replace_tag, sanitized)
        else:
            # Escape all HTML
            sanitized = html.escape(sanitized)
        
        return sanitized
    
    def validate_input(self, text: str, max_length: int = 10000, min_length: int = 1) -> Tuple[bool, str]:
        """
        Validate input text for basic security and format requirements.
        
        Args:
            text: Text to validate
            max_length: Maximum allowed length
            min_length: Minimum required length
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not isinstance(text, str):
            return False, "Input must be a string"
        
        if len(text) < min_length:
            return False, f"Input too short (minimum {min_length} characters)"
        
        if len(text) > max_length:
            return False, f"Input too long (maximum {max_length} characters)"
        
        # Check for null bytes
        if '\x00' in text:
            return False, "Input contains null bytes"
        
        # Check for excessive control characters
        control_chars = sum(1 for c in text if ord(c) < 32 and c not in '\t\n\r')
        if control_chars > len(text) * 0.1:  # More than 10% control characters
            return False, "Input contains too many control characters"
        
        # Check for potential SQL injection patterns (basic)
        sql_patterns = [
            r'union\s+select',
            r'drop\s+table',
            r'delete\s+from',
            r'insert\s+into',
            r'update\s+set',
            r'exec\s*\(',
            r'script\s*:',
            r'--\s*$'
        ]
        
        for pattern in sql_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                logger.warning(f"Potential SQL injection pattern detected: {pattern}")
                return False, "Input contains potentially dangerous patterns"
        
        return True, ""
    
    def clean_entity_name(self, name: str) -> str:
        """
        Clean entity name for safe use in URLs and file names.
        
        Args:
            name: Entity name to clean
            
        Returns:
            Cleaned entity name
        """
        if not name:
            return ""
        
        # Remove or replace dangerous characters
        cleaned = re.sub(r'[<>:"/\\|?*]', '_', name)
        cleaned = re.sub(r'[^\w\s\-_.]', '', cleaned)
        cleaned = re.sub(r'\s+', '_', cleaned.strip())
        
        # Limit length
        if len(cleaned) > 100:
            cleaned = cleaned[:100]
        
        return cleaned
    
    def filter_content(
        self,
        text: str,
        redact_pii: bool = True,
        sanitize_html: bool = True,
        validate_input: bool = True
    ) -> Dict[str, any]:
        """
        Apply all security filters to content.
        
        Args:
            text: Text content to filter
            redact_pii: Whether to redact PII
            sanitize_html: Whether to sanitize HTML
            validate_input: Whether to validate input
            
        Returns:
            Dictionary with filtered content and metadata
        """
        result = {
            'original_text': text,
            'filtered_text': text,
            'pii_detection': None,
            'validation_result': (True, ""),
            'is_safe': True,
            'warnings': []
        }
        
        # Input validation
        if validate_input:
            is_valid, error_msg = self.validate_input(text)
            result['validation_result'] = (is_valid, error_msg)
            
            if not is_valid:
                result['is_safe'] = False
                result['warnings'].append(f"Validation failed: {error_msg}")
                return result
        
        # PII detection and redaction
        if redact_pii:
            pii_result = self.detect_pii(text)
            result['pii_detection'] = pii_result
            result['filtered_text'] = pii_result.redacted_text
            
            if pii_result.has_pii:
                result['warnings'].append(f"PII detected and redacted: {pii_result.detected_types}")
        
        # HTML sanitization
        if sanitize_html:
            result['filtered_text'] = self.sanitize_html(result['filtered_text'])
        
        # Check if content was significantly modified
        if len(result['filtered_text']) < len(text) * 0.5:
            result['warnings'].append("Content was significantly modified during filtering")
        
        return result
    
    def create_safe_filename(self, filename: str) -> str:
        """
        Create safe filename from potentially unsafe input.
        
        Args:
            filename: Original filename
            
        Returns:
            Safe filename
        """
        if not filename:
            return "unnamed_file"
        
        # Remove path separators and dangerous characters
        safe_name = re.sub(r'[<>:"/\\|?*]', '_', filename)
        safe_name = re.sub(r'[^\w\s\-_.]', '', safe_name)
        safe_name = re.sub(r'\s+', '_', safe_name.strip())
        
        # Ensure it doesn't start with a dot or dash
        safe_name = re.sub(r'^[.-]+', '', safe_name)
        
        # Limit length
        if len(safe_name) > 200:
            name_part, ext_part = safe_name.rsplit('.', 1) if '.' in safe_name else (safe_name, '')
            safe_name = name_part[:190] + ('.' + ext_part if ext_part else '')
        
        # Ensure it's not empty
        if not safe_name:
            safe_name = "unnamed_file"
        
        return safe_name
