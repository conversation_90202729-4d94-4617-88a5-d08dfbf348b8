"""
Security filters for PII detection, XSS sanitization, and input validation
"""

import re
import html
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from loguru import logger


@dataclass
class PIIMatch:
    """PII detection match result"""
    pattern_type: str
    matched_text: str
    start_pos: int
    end_pos: int
    confidence: float


class SecurityFilters:
    """Security filters for content sanitization and validation"""
    
    # PII detection patterns
    PII_PATTERNS = {
        'phone_number': [
            r'1[3-9]\d{9}',  # Chinese mobile numbers
            r'\d{3}-\d{4}-\d{4}',  # US phone format
            r'\(\d{3}\)\s*\d{3}-\d{4}',  # US phone with parentheses
            r'\+\d{1,3}\s*\d{3,4}\s*\d{3,4}\s*\d{3,4}',  # International format
        ],
        'email': [
            r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
        ],
        'id_card': [
            r'\b\d{17}[\dXx]\b',  # Chinese ID card
            r'\b\d{15}\b',  # Old Chinese ID card
            r'\b\d{3}-\d{2}-\d{4}\b',  # US SSN format
        ],
        'credit_card': [
            r'\b4\d{3}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b',  # Visa
            r'\b5[1-5]\d{2}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b',  # MasterCard
            r'\b3[47]\d{2}[\s-]?\d{6}[\s-]?\d{5}\b',  # American Express
        ],
        'bank_account': [
            r'\b\d{16,19}\b',  # Bank account numbers
        ],
        'ip_address': [
            r'\b(?:\d{1,3}\.){3}\d{1,3}\b',  # IPv4
        ],
        'url': [
            r'https?://[^\s<>"{}|\\^`\[\]]+',
        ]
    }
    
    # XSS patterns to detect/remove
    XSS_PATTERNS = [
        r'<script[^>]*>.*?</script>',
        r'javascript:',
        r'on\w+\s*=',
        r'<iframe[^>]*>.*?</iframe>',
        r'<object[^>]*>.*?</object>',
        r'<embed[^>]*>',
        r'<link[^>]*>',
        r'<meta[^>]*>',
        r'<style[^>]*>.*?</style>',
    ]
    
    def __init__(self, enable_pii_detection: bool = True, 
                 enable_xss_protection: bool = True,
                 max_input_size: int = ********):  # 10MB
        self.enable_pii_detection = enable_pii_detection
        self.enable_xss_protection = enable_xss_protection
        self.max_input_size = max_input_size
        
        # Compile regex patterns for better performance
        self.compiled_pii_patterns = {}
        for pattern_type, patterns in self.PII_PATTERNS.items():
            self.compiled_pii_patterns[pattern_type] = [
                re.compile(pattern, re.IGNORECASE) for pattern in patterns
            ]
        
        self.compiled_xss_patterns = [
            re.compile(pattern, re.IGNORECASE | re.DOTALL) for pattern in self.XSS_PATTERNS
        ]
    
    def validate_input_size(self, content: str) -> bool:
        """Validate input size"""
        if len(content.encode('utf-8')) > self.max_input_size:
            logger.warning(f"Input size exceeds limit: {len(content)} bytes")
            return False
        return True
    
    def detect_pii(self, text: str, pattern_types: Optional[List[str]] = None) -> List[PIIMatch]:
        """Detect PII in text"""
        if not self.enable_pii_detection:
            return []
        
        if pattern_types is None:
            pattern_types = list(self.PII_PATTERNS.keys())
        
        matches = []
        
        for pattern_type in pattern_types:
            if pattern_type not in self.compiled_pii_patterns:
                continue
            
            for pattern in self.compiled_pii_patterns[pattern_type]:
                for match in pattern.finditer(text):
                    pii_match = PIIMatch(
                        pattern_type=pattern_type,
                        matched_text=match.group(),
                        start_pos=match.start(),
                        end_pos=match.end(),
                        confidence=self._calculate_pii_confidence(pattern_type, match.group())
                    )
                    matches.append(pii_match)
        
        # Sort by position
        matches.sort(key=lambda x: x.start_pos)
        
        return matches
    
    def _calculate_pii_confidence(self, pattern_type: str, matched_text: str) -> float:
        """Calculate confidence score for PII match"""
        
        # Basic confidence calculation
        base_confidence = 0.8
        
        if pattern_type == 'phone_number':
            # Higher confidence for properly formatted numbers
            if re.match(r'1[3-9]\d{9}', matched_text):
                return 0.95
            elif '-' in matched_text or '(' in matched_text:
                return 0.9
            else:
                return base_confidence
        
        elif pattern_type == 'email':
            # Check for common domains
            common_domains = ['gmail.com', 'qq.com', '163.com', 'outlook.com', 'yahoo.com']
            if any(domain in matched_text.lower() for domain in common_domains):
                return 0.95
            else:
                return 0.85
        
        elif pattern_type == 'id_card':
            # 18-digit Chinese ID has higher confidence
            if len(matched_text) == 18:
                return 0.9
            else:
                return base_confidence
        
        return base_confidence
    
    def redact_pii(self, text: str, pattern_types: Optional[List[str]] = None,
                   redaction_char: str = '*') -> Tuple[str, List[PIIMatch]]:
        """Redact PII from text"""
        
        matches = self.detect_pii(text, pattern_types)
        
        if not matches:
            return text, matches
        
        # Sort matches by position (reverse order for safe replacement)
        matches.sort(key=lambda x: x.start_pos, reverse=True)
        
        redacted_text = text
        
        for match in matches:
            # Create redaction string
            if match.pattern_type == 'phone_number':
                redaction = f"{match.matched_text[:3]}{'*' * (len(match.matched_text) - 6)}{match.matched_text[-3:]}"
            elif match.pattern_type == 'email':
                parts = match.matched_text.split('@')
                if len(parts) == 2:
                    username = parts[0]
                    domain = parts[1]
                    redacted_username = f"{username[:2]}{'*' * max(1, len(username) - 4)}{username[-2:] if len(username) > 4 else ''}"
                    redaction = f"{redacted_username}@{domain}"
                else:
                    redaction = redaction_char * len(match.matched_text)
            elif match.pattern_type == 'id_card':
                redaction = f"{match.matched_text[:4]}{'*' * (len(match.matched_text) - 8)}{match.matched_text[-4:]}"
            else:
                redaction = redaction_char * len(match.matched_text)
            
            # Replace in text
            redacted_text = redacted_text[:match.start_pos] + redaction + redacted_text[match.end_pos:]
        
        logger.info(f"Redacted {len(matches)} PII instances")
        return redacted_text, matches
    
    def detect_xss(self, text: str) -> List[Dict[str, Any]]:
        """Detect potential XSS patterns"""
        if not self.enable_xss_protection:
            return []
        
        xss_matches = []
        
        for i, pattern in enumerate(self.compiled_xss_patterns):
            for match in pattern.finditer(text):
                xss_matches.append({
                    'pattern_index': i,
                    'pattern_type': self.XSS_PATTERNS[i],
                    'matched_text': match.group(),
                    'start_pos': match.start(),
                    'end_pos': match.end()
                })
        
        return xss_matches
    
    def sanitize_xss(self, text: str) -> str:
        """Sanitize text by removing XSS patterns"""
        if not self.enable_xss_protection:
            return text
        
        sanitized = text
        
        # Remove dangerous patterns
        for pattern in self.compiled_xss_patterns:
            sanitized = pattern.sub('', sanitized)
        
        # HTML escape remaining content
        sanitized = html.escape(sanitized)
        
        return sanitized
    
    def sanitize_html(self, text: str, allowed_tags: Optional[List[str]] = None) -> str:
        """Sanitize HTML content"""
        
        if allowed_tags is None:
            allowed_tags = ['p', 'br', 'strong', 'em', 'u', 'ol', 'ul', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6']
        
        # Remove all HTML tags except allowed ones
        allowed_pattern = '|'.join(allowed_tags)
        
        # Remove dangerous tags first
        sanitized = self.sanitize_xss(text)
        
        # Remove all tags except allowed ones
        tag_pattern = re.compile(r'<(?!/?({})\b)[^>]*>'.format(allowed_pattern), re.IGNORECASE)
        sanitized = tag_pattern.sub('', sanitized)
        
        return sanitized
    
    def validate_content(self, content: str, content_type: str = "text") -> Dict[str, Any]:
        """Comprehensive content validation"""
        
        validation_result = {
            'is_valid': True,
            'issues': [],
            'pii_detected': [],
            'xss_detected': [],
            'size_valid': True
        }
        
        # Size validation
        if not self.validate_input_size(content):
            validation_result['is_valid'] = False
            validation_result['size_valid'] = False
            validation_result['issues'].append('Content size exceeds limit')
        
        # PII detection
        if self.enable_pii_detection:
            pii_matches = self.detect_pii(content)
            if pii_matches:
                validation_result['pii_detected'] = pii_matches
                validation_result['issues'].append(f'PII detected: {len(pii_matches)} instances')
        
        # XSS detection
        if self.enable_xss_protection:
            xss_matches = self.detect_xss(content)
            if xss_matches:
                validation_result['is_valid'] = False
                validation_result['xss_detected'] = xss_matches
                validation_result['issues'].append(f'XSS patterns detected: {len(xss_matches)} instances')
        
        # Content type specific validation
        if content_type == "json":
            try:
                import json
                json.loads(content)
            except json.JSONDecodeError as e:
                validation_result['is_valid'] = False
                validation_result['issues'].append(f'Invalid JSON: {e}')
        
        return validation_result
    
    def clean_content(self, content: str, redact_pii: bool = True, 
                     sanitize_html: bool = True) -> Tuple[str, Dict[str, Any]]:
        """Clean content by removing/redacting sensitive information"""
        
        cleaned_content = content
        cleaning_report = {
            'pii_redacted': [],
            'xss_removed': False,
            'html_sanitized': False
        }
        
        # Redact PII if requested
        if redact_pii and self.enable_pii_detection:
            cleaned_content, pii_matches = self.redact_pii(cleaned_content)
            cleaning_report['pii_redacted'] = pii_matches
        
        # Sanitize XSS
        if self.enable_xss_protection:
            xss_matches = self.detect_xss(cleaned_content)
            if xss_matches:
                cleaned_content = self.sanitize_xss(cleaned_content)
                cleaning_report['xss_removed'] = True
        
        # Sanitize HTML if requested
        if sanitize_html:
            original_length = len(cleaned_content)
            cleaned_content = self.sanitize_html(cleaned_content)
            if len(cleaned_content) != original_length:
                cleaning_report['html_sanitized'] = True
        
        return cleaned_content, cleaning_report


def test_security_filters():
    """Test security filters functionality"""
    
    filters = SecurityFilters()
    
    # Test PII detection
    test_text = """
    联系方式：手机号码13812345678，邮箱****************
    身份证号：123456789012345678
    信用卡：4111 1111 1111 1111
    """
    
    pii_matches = filters.detect_pii(test_text)
    print(f"PII detected: {len(pii_matches)} instances")
    for match in pii_matches:
        print(f"  {match.pattern_type}: {match.matched_text}")
    
    # Test PII redaction
    redacted_text, _ = filters.redact_pii(test_text)
    print(f"Redacted text: {redacted_text}")
    
    # Test XSS detection
    xss_text = '<script>alert("xss")</script><p>Normal content</p>'
    xss_matches = filters.detect_xss(xss_text)
    print(f"XSS detected: {len(xss_matches)} instances")
    
    # Test XSS sanitization
    sanitized = filters.sanitize_xss(xss_text)
    print(f"Sanitized: {sanitized}")


if __name__ == "__main__":
    test_security_filters()
