# 🔧 启动服务问题解决方案

## 🚨 问题描述

```
PS G:\work\fanren\planner> python main.py serve
Starting Knowledge Graph Builder Web Interface
Server will be available at: http://0.0.0.0:8000
Failed to start server: No module named 'pocketflow'
```

## 🔍 问题分析

### 根本原因
- `main.py serve` 命令启动 `web.app:app`
- `web/app.py` 导入 `flows.main_flow`
- `flows/main_flow.py` 依赖 `pocketflow` 模块
- `pocketflow` 模块未在依赖文件中包含，也未安装

### 依赖链
```
main.py serve
  └── web/app.py
      └── flows/main_flow.py
          └── from pocketflow import AsyncFlow  ❌ 缺失
```

## ✅ 推荐解决方案：使用 web_simple.py

### 🌟 优势
- ✅ **无依赖问题**: 不依赖 `pocketflow` 模块
- ✅ **功能完整**: 包含所有上传、处理、搜索功能
- ✅ **界面中文化**: 完全中文化的用户界面
- ✅ **已测试验证**: 所有功能都经过完整测试
- ✅ **即开即用**: 无需安装额外依赖

### 🚀 使用方法

#### 方法 1: 直接启动（推荐）
```bash
python web_simple.py
```

#### 方法 2: 后台启动
```bash
# Windows PowerShell
Start-Process python -ArgumentList "web_simple.py" -WindowStyle Hidden

# Linux/Mac
nohup python web_simple.py &
```

#### 方法 3: 指定端口启动
```bash
# 修改 web_simple.py 中的端口号
# 在文件末尾找到：
# uvicorn.run(app, host="0.0.0.0", port=8001)
# 改为你想要的端口，如：
# uvicorn.run(app, host="0.0.0.0", port=8000)
```

### 📊 功能对比

| 功能 | main.py serve | web_simple.py |
|------|---------------|---------------|
| 启动状态 | ❌ 失败 (缺少依赖) | ✅ 正常 |
| 文档上传 | ❌ 无法启动 | ✅ 完整支持 |
| 中文界面 | ❌ 无法启动 | ✅ 完全中文化 |
| 实体搜索 | ❌ 无法启动 | ✅ 智能搜索 |
| 数据导出 | ❌ 无法启动 | ✅ JSON 导出 |
| 拖拽上传 | ❌ 无法启动 | ✅ 支持拖拽 |
| 错误处理 | ❌ 无法启动 | ✅ 友好提示 |

## 🔧 替代解决方案：安装 pocketflow

如果您坚持使用 `main.py serve`，可以尝试安装 `pocketflow`：

### 方法 1: pip 安装
```bash
pip install pocketflow
```

### 方法 2: 从源码安装
```bash
pip install git+https://github.com/pocketflow/pocketflow.git
```

### ⚠️ 注意事项
- `pocketflow` 可能有额外的依赖要求
- 可能需要特定的 Python 版本
- 安装后仍可能遇到其他兼容性问题

## 🎯 推荐使用流程

### 1. 启动服务
```bash
cd G:\work\fanren\planner
python web_simple.py
```

### 2. 访问界面
```
浏览器访问: http://localhost:8001
```

### 3. 使用功能
- **首页**: http://localhost:8001/
- **上传**: http://localhost:8001/upload
- **搜索**: http://localhost:8001/search
- **图谱**: http://localhost:8001/graph
- **作业**: http://localhost:8001/jobs

### 4. 测试功能
```bash
# 测试所有路由
python test_routes.py

# 测试上传功能
python test_upload_functionality.py
```

## 🌟 web_simple.py 特色功能

### 📤 文档上传
- 支持 TXT、MD 格式文件
- 拖拽上传功能
- 多文件同时上传
- 文件大小显示

### ⚙️ 处理模式
- **合并模式**: 统一知识图谱
- **批处理模式**: 独立文档图谱
- 实时处理进度显示

### 📊 结果展示
- 处理统计信息
- 图谱统计数据
- 实体类型分布
- 中文类型显示

### 🔍 搜索功能
- 实体名称搜索
- 类型过滤选择
- 相关度评分
- 详细信息展示

### 💾 数据导出
- JSON 格式下载
- 完整图谱数据
- 一键导出功能

## 🎉 总结

**推荐使用 `web_simple.py`**，因为它：

1. ✅ **立即可用**: 无需解决依赖问题
2. ✅ **功能完整**: 包含所有必要功能
3. ✅ **用户友好**: 完全中文化界面
4. ✅ **稳定可靠**: 经过完整测试验证
5. ✅ **维护简单**: 代码结构清晰

现在就可以开始使用完整的知识图谱构建功能！🚀
