# LLM API Configuration
LLM_BASE_URL=https://gateway.chat.sensedeal.vip/v1
LLM_API_KEY=974fd8d1c155aa3d04b17bf253176b5e
LLM_MODEL_LOCAL=qwen2.5-32b-instruct-int4
LLM_MODEL_PERFORMANCE=doubao-seed-1.6

# Database Configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=password

# Elasticsearch Configuration
ELASTICSEARCH_HOSTS=localhost:9200
ELASTICSEARCH_INDEX=knowledge_graph

# Redis Configuration (for caching)
REDIS_URL=redis://localhost:6379

# Application Configuration
DEBUG=true
LOG_LEVEL=INFO
MAX_WORKERS=4
PROCESSING_MODE=merge  # "batch" or "merge"

# Security
SECRET_KEY=your-secret-key-here
ENABLE_PII_DETECTION=true

# File Processing
MAX_FILE_SIZE_MB=100
TEMP_DIR=./temp
SUPPORTED_FORMATS=txt,docx,pdf,chat

# Web Interface
WEB_HOST=0.0.0.0
WEB_PORT=8000

# Graph Processing
ENTITY_SIMILARITY_THRESHOLD=0.85
MAX_CONCURRENT_SEGMENTS=5
SEGMENT_TIMEOUT_SECONDS=300
