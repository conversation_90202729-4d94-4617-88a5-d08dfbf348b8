# Development Guide

This guide covers development setup, architecture, and contribution guidelines for the Knowledge Graph Builder.

## Development Setup

### Prerequisites

- Python 3.8+
- Git
- Virtual environment tool (venv, conda, etc.)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd planner
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   pip install -r requirements-dev.txt  # Development dependencies
   ```

4. **Set up environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

5. **Run tests**
   ```bash
   python run_tests.py
   ```

## Architecture Overview

### Core Components

```
planner/
├── config/          # Configuration management
├── flows/           # PocketFlow workflow definitions
├── nodes/           # Individual processing nodes
├── utils/           # Utility functions and classes
├── web/             # Web interface
├── tests/           # Test suite
└── main.py          # CLI entry point
```

### Flow Architecture

The system uses PocketFlow for workflow orchestration:

```
MainFlow
├── FileProcessingFlow
│   ├── ParseDocument
│   ├── SplitLongContent
│   ├── SegmentProcessingBatchFlow
│   └── EntityNormalizationFlow
├── GraphBuildFlow
└── PostProcessingFlow
```

### Key Design Patterns

1. **Flow-based Architecture**: Uses PocketFlow for modular, composable workflows
2. **Async Processing**: Leverages asyncio for concurrent LLM calls
3. **Security-first**: Built-in PII detection and input sanitization
4. **Configurable**: Environment-based configuration with sensible defaults
5. **Testable**: Comprehensive test suite with mocking

## Development Workflow

### Code Style

- Follow PEP 8 with 100-character line limit
- Use type hints where possible
- Document all public functions and classes
- Use meaningful variable and function names

### Testing

Run different types of tests:

```bash
# All tests
python run_tests.py

# Unit tests only
python run_tests.py --unit

# Integration tests
python run_tests.py --integration

# Web tests
python run_tests.py --web

# With coverage
python run_tests.py --coverage

# Fast tests (skip slow ones)
python run_tests.py --fast
```

### Adding New Features

1. **Create a new branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Write tests first** (TDD approach)
   - Add unit tests in appropriate test file
   - Add integration tests if needed
   - Ensure tests fail initially

3. **Implement the feature**
   - Follow existing patterns
   - Add proper error handling
   - Update documentation

4. **Run tests and linting**
   ```bash
   python run_tests.py --coverage
   ```

5. **Update documentation**
   - Update README.md if needed
   - Add docstrings to new functions
   - Update this development guide if architecture changes

### Adding New Nodes

To add a new processing node:

1. **Create the node class**
   ```python
   # nodes/my_new_node.py
   from pocketflow import AsyncNode
   
   class MyNewNode(AsyncNode):
       async def prep_async(self, shared):
           # Preparation logic
           return prep_data
       
       async def exec_async(self, prep_res):
           # Execution logic
           return result
       
       async def post_async(self, shared, prep_res, exec_res):
           # Post-processing logic
           return "default"
   ```

2. **Add tests**
   ```python
   # tests/test_my_new_node.py
   import pytest
   from nodes.my_new_node import MyNewNode
   
   class TestMyNewNode:
       @pytest.mark.asyncio
       async def test_my_new_node(self):
           # Test implementation
           pass
   ```

3. **Integrate into flows**
   ```python
   # flows/some_flow.py
   from nodes.my_new_node import MyNewNode
   
   class SomeFlow(AsyncFlow):
       def __init__(self):
           super().__init__()
           self.my_node = MyNewNode()
           # Connect to flow
   ```

### Adding New Utilities

1. **Create utility module**
   ```python
   # utils/my_utility.py
   class MyUtility:
       def __init__(self, config):
           self.config = config
       
       def process(self, data):
           # Implementation
           return result
   ```

2. **Add comprehensive tests**
   ```python
   # tests/test_my_utility.py
   from utils.my_utility import MyUtility
   
   def test_my_utility():
       # Test implementation
       pass
   ```

3. **Update imports**
   ```python
   # utils/__init__.py
   from .my_utility import MyUtility
   ```

## Configuration Management

### Environment Variables

All configuration is managed through environment variables:

```bash
# LLM Configuration
LLM_BASE_URL=https://gateway.chat.sensedeal.vip/v1
LLM_API_KEY=your-api-key
LLM_MODEL_LOCAL=qwen2.5-32b-instruct-int4
LLM_MODEL_PERFORMANCE=doubao-seed-1.6

# Processing Configuration
PROCESSING_MODE=merge
MAX_WORKERS=5
ENTITY_SIMILARITY_THRESHOLD=0.85

# Web Configuration
WEB_HOST=0.0.0.0
WEB_PORT=8000
DEBUG=false

# Storage Configuration
ELASTICSEARCH_HOSTS=localhost:9200
TEMP_DIR=./temp
```

### Adding New Configuration

1. **Add to Settings class**
   ```python
   # config/settings.py
   class Settings:
       def __init__(self):
           self.my_new_setting = os.getenv("MY_NEW_SETTING", "default_value")
   ```

2. **Add validation if needed**
   ```python
   def __init__(self):
       raw_value = os.getenv("MY_NEW_SETTING", "default")
       self.my_new_setting = self._validate_my_setting(raw_value)
   
   def _validate_my_setting(self, value):
       # Validation logic
       return validated_value
   ```

3. **Add tests**
   ```python
   # tests/test_config.py
   def test_my_new_setting():
       with patch.dict(os.environ, {'MY_NEW_SETTING': 'test_value'}):
           settings = Settings()
           assert settings.my_new_setting == 'test_value'
   ```

## Web Development

### Frontend Structure

```
web/
├── app.py              # FastAPI application
├── templates/          # Jinja2 templates
│   ├── base.html      # Base template
│   ├── index.html     # Dashboard
│   ├── upload.html    # File upload
│   ├── search.html    # Entity search
│   ├── graph.html     # Graph visualization
│   └── jobs.html      # Job management
└── static/            # Static assets (if any)
```

### Adding New API Endpoints

1. **Add endpoint to app.py**
   ```python
   @app.get("/api/my-endpoint")
   async def my_endpoint():
       return {"message": "Hello World"}
   ```

2. **Add request/response models**
   ```python
   class MyRequest(BaseModel):
       field1: str
       field2: int
   
   class MyResponse(BaseModel):
       result: str
   
   @app.post("/api/my-endpoint", response_model=MyResponse)
   async def my_endpoint(request: MyRequest):
       return MyResponse(result="processed")
   ```

3. **Add tests**
   ```python
   # tests/test_web.py
   def test_my_endpoint(client):
       response = client.get("/api/my-endpoint")
       assert response.status_code == 200
   ```

### Adding New Pages

1. **Create template**
   ```html
   <!-- web/templates/my_page.html -->
   {% extends "base.html" %}
   
   {% block title %}My Page{% endblock %}
   
   {% block content %}
   <h1>My Page Content</h1>
   {% endblock %}
   ```

2. **Add route**
   ```python
   @app.get("/my-page", response_class=HTMLResponse)
   async def my_page(request: Request):
       return templates.TemplateResponse("my_page.html", {"request": request})
   ```

3. **Update navigation**
   ```html
   <!-- web/templates/base.html -->
   <li class="nav-item">
       <a class="nav-link" href="/my-page">My Page</a>
   </li>
   ```

## Debugging

### Logging

The system uses structured logging:

```python
from loguru import logger

# Different log levels
logger.debug("Debug information")
logger.info("General information")
logger.warning("Warning message")
logger.error("Error occurred")

# With context
logger.info("Processing document", file_path=path, size=size)
```

### Common Issues

1. **LLM API Errors**
   - Check API key and endpoint
   - Verify rate limits
   - Check network connectivity

2. **Memory Issues**
   - Reduce MAX_WORKERS
   - Check for memory leaks in long-running processes
   - Monitor segment processing

3. **Performance Issues**
   - Use local models for development
   - Enable caching
   - Profile with cProfile if needed

### Development Tools

```bash
# Run with debug logging
LOG_LEVEL=DEBUG python main.py process documents/

# Profile performance
python -m cProfile -o profile.stats main.py process documents/

# Memory profiling
pip install memory-profiler
python -m memory_profiler main.py process documents/
```

## Contributing

### Pull Request Process

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Update documentation
6. Run the full test suite
7. Submit a pull request

### Code Review Checklist

- [ ] Tests added and passing
- [ ] Documentation updated
- [ ] Code follows style guidelines
- [ ] No breaking changes (or properly documented)
- [ ] Performance impact considered
- [ ] Security implications reviewed

### Release Process

1. Update version numbers
2. Update CHANGELOG.md
3. Run full test suite
4. Create release tag
5. Deploy to production

## Performance Optimization

### Profiling

```bash
# Profile the main flow
python -m cProfile -o profile.stats main.py process documents/

# Analyze results
python -c "import pstats; pstats.Stats('profile.stats').sort_stats('cumulative').print_stats(20)"
```

### Memory Optimization

- Use generators for large datasets
- Clear intermediate results when possible
- Monitor memory usage in long-running processes
- Use appropriate data structures

### Async Optimization

- Batch LLM calls when possible
- Use appropriate concurrency limits
- Avoid blocking operations in async code
- Use connection pooling for external services

## Security Considerations

### Input Validation

- All user inputs are validated
- File uploads are scanned for malicious content
- PII detection is enabled by default
- SQL injection prevention in search queries

### API Security

- Rate limiting on API endpoints
- Input sanitization
- CORS configuration
- Authentication for sensitive operations

### Data Privacy

- PII redaction in processing pipeline
- Secure temporary file handling
- Data retention policies
- Audit logging for sensitive operations
