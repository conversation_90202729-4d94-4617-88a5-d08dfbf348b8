"""Entity normalization flow for entity quality enhancement."""

from typing import Dict, Any

from pocketflow import AsyncFlow
from loguru import logger

from nodes.entity_nodes import NormalizeEntities, AddNodeTagsAndDescriptions


class EntityNormalizationFlow(AsyncFlow):
    """Async node chain for entity quality enhancement."""
    
    def __init__(self, use_cache: bool = True, enable_enhancement: bool = True):
        super().__init__()
        
        self.use_cache = use_cache
        self.enable_enhancement = enable_enhancement
        
        # Create nodes
        self.normalize_entities = NormalizeEntities(use_cache=use_cache)
        
        if enable_enhancement:
            self.add_tags_descriptions = AddNodeTagsAndDescriptions()
        else:
            self.add_tags_descriptions = None
        
        # Set up flow
        self._setup_flow()
    
    def _setup_flow(self):
        """Set up the flow connections between nodes."""
        
        # Start with entity normalization
        self.start(self.normalize_entities)
        
        # Connect to enhancement if enabled
        if self.add_tags_descriptions:
            self.normalize_entities >> self.add_tags_descriptions
    
    async def prep_async(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare entity normalization flow.
        
        Args:
            shared: Shared data structure
            
        Returns:
            Preparation data
        """
        logger.info("Starting entity normalization flow")
        
        documents = shared.get("documents", [])
        
        if not documents:
            raise ValueError("No documents found for entity normalization")
        
        # Count raw entities
        total_raw_entities = sum(len(doc.get("raw_entities", [])) for doc in documents)
        
        if total_raw_entities == 0:
            logger.warning("No raw entities found for normalization")
            return {
                "total_raw_entities": 0,
                "skip_processing": True,
                "normalization_start_time": logger._core.clock()
            }
        
        logger.info(f"Normalizing {total_raw_entities} raw entities from {len(documents)} documents")
        
        return {
            "total_raw_entities": total_raw_entities,
            "total_documents": len(documents),
            "skip_processing": False,
            "normalization_start_time": logger._core.clock()
        }
    
    async def post_async(
        self,
        shared: Dict[str, Any],
        prep_res: Dict[str, Any],
        exec_res: Any
    ) -> str:
        """
        Post-process entity normalization results.
        
        Args:
            shared: Shared data structure
            prep_res: Preparation results
            exec_res: Execution results
            
        Returns:
            Next action
        """
        normalization_time = logger._core.clock() - prep_res["normalization_start_time"]
        
        if prep_res.get("skip_processing"):
            logger.info("Entity normalization skipped - no entities to process")
            return "default"
        
        # Calculate normalization statistics
        total_raw_entities = prep_res["total_raw_entities"]
        normalized_entities = shared.get("normalized_entities", [])
        total_normalized = len(normalized_entities)
        
        # Calculate reduction ratio
        reduction_ratio = (total_raw_entities - total_normalized) / total_raw_entities if total_raw_entities > 0 else 0
        
        logger.info(f"Entity normalization completed in {normalization_time:.2f} seconds")
        logger.info(f"Normalized {total_raw_entities} raw entities to {total_normalized} unique entities")
        logger.info(f"Reduction ratio: {reduction_ratio:.2%}")
        
        # Update task state
        task_state = shared.setdefault("task_state", {})
        task_state["entity_normalization_completed"] = True
        task_state["normalization_time"] = normalization_time
        task_state["raw_entities_count"] = total_raw_entities
        task_state["normalized_entities_count"] = total_normalized
        task_state["reduction_ratio"] = reduction_ratio
        
        # Check enhancement results if enabled
        if self.enable_enhancement:
            enhanced_entities = [
                entity for entity in normalized_entities
                if entity.get("description") or entity.get("tags")
            ]
            enhancement_ratio = len(enhanced_entities) / total_normalized if total_normalized > 0 else 0
            
            logger.info(f"Enhanced {len(enhanced_entities)} entities ({enhancement_ratio:.2%})")
            task_state["enhanced_entities_count"] = len(enhanced_entities)
            task_state["enhancement_ratio"] = enhancement_ratio
        
        return "default"


class FastEntityNormalizationFlow(AsyncFlow):
    """Fast entity normalization flow with minimal enhancement."""
    
    def __init__(self):
        super().__init__()
        
        # Create only normalization node (no enhancement)
        self.normalize_entities = NormalizeEntities(use_cache=True)
        
        # Set up flow
        self._setup_flow()
    
    def _setup_flow(self):
        """Set up the fast flow."""
        
        # Only normalization
        self.start(self.normalize_entities)
    
    async def prep_async(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare fast entity normalization.
        
        Args:
            shared: Shared data structure
            
        Returns:
            Preparation data
        """
        logger.info("Starting fast entity normalization flow")
        
        documents = shared.get("documents", [])
        total_raw_entities = sum(len(doc.get("raw_entities", [])) for doc in documents)
        
        return {
            "total_raw_entities": total_raw_entities,
            "normalization_start_time": logger._core.clock()
        }
    
    async def post_async(
        self,
        shared: Dict[str, Any],
        prep_res: Dict[str, Any],
        exec_res: Any
    ) -> str:
        """
        Post-process fast normalization results.
        
        Args:
            shared: Shared data structure
            prep_res: Preparation results
            exec_res: Execution results
            
        Returns:
            Next action
        """
        normalization_time = logger._core.clock() - prep_res["normalization_start_time"]
        
        total_raw_entities = prep_res["total_raw_entities"]
        normalized_entities = shared.get("normalized_entities", [])
        total_normalized = len(normalized_entities)
        
        logger.info(f"Fast normalization completed in {normalization_time:.2f} seconds")
        logger.info(f"Processed {total_raw_entities} -> {total_normalized} entities")
        
        return "default"


class ComprehensiveEntityNormalizationFlow(AsyncFlow):
    """Comprehensive entity normalization flow with full enhancement."""
    
    def __init__(self):
        super().__init__()
        
        # Create nodes with comprehensive settings
        self.normalize_entities = NormalizeEntities(use_cache=True)
        self.add_tags_descriptions = AddNodeTagsAndDescriptions()
        
        # Set up flow
        self._setup_flow()
    
    def _setup_flow(self):
        """Set up the comprehensive flow."""
        
        # Start with normalization
        self.start(self.normalize_entities)
        
        # Connect to enhancement
        self.normalize_entities >> self.add_tags_descriptions
    
    async def prep_async(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare comprehensive entity normalization.
        
        Args:
            shared: Shared data structure
            
        Returns:
            Preparation data
        """
        logger.info("Starting comprehensive entity normalization flow")
        
        documents = shared.get("documents", [])
        total_raw_entities = sum(len(doc.get("raw_entities", [])) for doc in documents)
        
        # Estimate processing time based on entity count
        estimated_time = total_raw_entities * 0.5  # Rough estimate
        
        logger.info(f"Processing {total_raw_entities} entities (estimated time: {estimated_time:.1f}s)")
        
        return {
            "total_raw_entities": total_raw_entities,
            "estimated_time": estimated_time,
            "normalization_start_time": logger._core.clock()
        }
    
    async def post_async(
        self,
        shared: Dict[str, Any],
        prep_res: Dict[str, Any],
        exec_res: Any
    ) -> str:
        """
        Post-process comprehensive normalization results.
        
        Args:
            shared: Shared data structure
            prep_res: Preparation results
            exec_res: Execution results
            
        Returns:
            Next action
        """
        actual_time = logger._core.clock() - prep_res["normalization_start_time"]
        estimated_time = prep_res["estimated_time"]
        
        total_raw_entities = prep_res["total_raw_entities"]
        normalized_entities = shared.get("normalized_entities", [])
        total_normalized = len(normalized_entities)
        
        # Calculate quality metrics
        entities_with_descriptions = sum(
            1 for entity in normalized_entities
            if entity.get("description") and len(entity["description"]) > 20
        )
        
        entities_with_tags = sum(
            1 for entity in normalized_entities
            if entity.get("tags") and len(entity["tags"]) > 0
        )
        
        quality_score = (entities_with_descriptions + entities_with_tags) / (2 * total_normalized) if total_normalized > 0 else 0
        
        logger.info(f"Comprehensive normalization completed:")
        logger.info(f"  Time: {actual_time:.2f}s (estimated: {estimated_time:.2f}s)")
        logger.info(f"  Entities: {total_raw_entities} -> {total_normalized}")
        logger.info(f"  With descriptions: {entities_with_descriptions}")
        logger.info(f"  With tags: {entities_with_tags}")
        logger.info(f"  Quality score: {quality_score:.2%}")
        
        # Store comprehensive metrics
        task_state = shared.setdefault("task_state", {})
        task_state["comprehensive_normalization_metrics"] = {
            "actual_time": actual_time,
            "estimated_time": estimated_time,
            "entities_with_descriptions": entities_with_descriptions,
            "entities_with_tags": entities_with_tags,
            "quality_score": quality_score
        }
        
        return "default"


class EntityNormalizationFlowFactory:
    """Factory for creating entity normalization flows with different configurations."""
    
    @staticmethod
    def create_standard_flow(enable_enhancement: bool = True) -> EntityNormalizationFlow:
        """Create standard entity normalization flow."""
        return EntityNormalizationFlow(enable_enhancement=enable_enhancement)
    
    @staticmethod
    def create_fast_flow() -> FastEntityNormalizationFlow:
        """Create fast entity normalization flow."""
        return FastEntityNormalizationFlow()
    
    @staticmethod
    def create_comprehensive_flow() -> ComprehensiveEntityNormalizationFlow:
        """Create comprehensive entity normalization flow."""
        return ComprehensiveEntityNormalizationFlow()
    
    @staticmethod
    def create_custom_flow(
        use_cache: bool = True,
        enable_enhancement: bool = True
    ) -> EntityNormalizationFlow:
        """Create custom entity normalization flow."""
        return EntityNormalizationFlow(
            use_cache=use_cache,
            enable_enhancement=enable_enhancement
        )
