{% extends "base.html" %}

{% block title %}上传文档 - 知识图谱构建器{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-upload me-2"></i>
                    上传文档
                </h4>
            </div>
            
            <div class="card-body">
                <!-- Upload Form -->
                <form id="upload-form" enctype="multipart/form-data">
                    <div class="mb-4">
                        <label for="files" class="form-label">选择文档文件</label>
                        <input type="file" class="form-control" id="files" name="files" multiple 
                               accept=".txt,.docx,.pdf,.md" required>
                        <div class="form-text">
                            支持的格式：TXT, DOCX, PDF, MD。可以选择多个文件。
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label for="mode" class="form-label">处理模式</label>
                            <select class="form-select" id="mode" name="mode">
                                <option value="merge" selected>合并模式 - 统一知识图谱</option>
                                <option value="batch">批处理模式 - 独立图谱</option>
                            </select>
                            <div class="form-text">
                                合并模式将所有文档合并为一个图谱，批处理模式为每个文档创建独立图谱。
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="output-formats" class="form-label">输出格式</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="json" id="format-json" checked>
                                <label class="form-check-label" for="format-json">JSON</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="html" id="format-html" checked>
                                <label class="form-check-label" for="format-html">HTML Wiki</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="rdf" id="format-rdf">
                                <label class="form-check-label" for="format-rdf">RDF</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg" id="upload-btn">
                            <i class="fas fa-upload me-2"></i>
                            开始处理
                        </button>
                    </div>
                </form>
                
                <!-- Progress Section -->
                <div id="progress-section" class="mt-4" style="display: none;">
                    <h5>处理进度</h5>
                    <div class="progress mb-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%" id="progress-bar">
                        </div>
                    </div>
                    <div id="progress-text" class="text-muted">准备中...</div>
                </div>
                
                <!-- Results Section -->
                <div id="results-section" class="mt-4" style="display: none;">
                    <div class="alert alert-success">
                        <h5 class="alert-heading">
                            <i class="fas fa-check-circle me-2"></i>
                            处理完成！
                        </h5>
                        <p id="results-text" class="mb-0"></p>
                        <hr>
                        <div class="d-flex gap-2">
                            <a href="/graph" class="btn btn-success">
                                <i class="fas fa-sitemap me-1"></i>查看图谱
                            </a>
                            <a href="/search" class="btn btn-info">
                                <i class="fas fa-search me-1"></i>搜索探索
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Error Section -->
                <div id="error-section" class="mt-4" style="display: none;">
                    <div class="alert alert-danger">
                        <h5 class="alert-heading">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            处理失败
                        </h5>
                        <p id="error-text" class="mb-0"></p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Instructions -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    使用说明
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>支持的文件格式</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-file-alt text-primary me-2"></i>TXT - 纯文本文件</li>
                            <li><i class="fas fa-file-word text-info me-2"></i>DOCX - Word文档</li>
                            <li><i class="fas fa-file-pdf text-danger me-2"></i>PDF - PDF文档</li>
                            <li><i class="fas fa-file-code text-secondary me-2"></i>MD - Markdown文件</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>处理流程</h6>
                        <ol class="list-unstyled">
                            <li><i class="fas fa-1 text-primary me-2"></i>文档解析和内容提取</li>
                            <li><i class="fas fa-2 text-primary me-2"></i>语义分割和预处理</li>
                            <li><i class="fas fa-3 text-primary me-2"></i>实体关系提取</li>
                            <li><i class="fas fa-4 text-primary me-2"></i>图谱构建和优化</li>
                            <li><i class="fas fa-5 text-primary me-2"></i>输出生成和索引</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.getElementById('upload-form').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const formData = new FormData();
        const files = document.getElementById('files').files;
        const mode = document.getElementById('mode').value;
        
        // Add files
        for (let file of files) {
            formData.append('files', file);
        }
        
        // Add mode
        formData.append('mode', mode);
        
        // Add output formats
        const formats = [];
        if (document.getElementById('format-json').checked) formats.push('json');
        if (document.getElementById('format-html').checked) formats.push('html');
        if (document.getElementById('format-rdf').checked) formats.push('rdf');
        formData.append('output_formats', formats.join(','));
        
        // Show progress
        document.getElementById('progress-section').style.display = 'block';
        document.getElementById('results-section').style.display = 'none';
        document.getElementById('error-section').style.display = 'none';
        document.getElementById('upload-btn').disabled = true;
        
        try {
            const response = await fetch('/api/upload', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (response.ok) {
                // Start monitoring progress
                monitorProgress();
            } else {
                throw new Error(result.detail || 'Upload failed');
            }
            
        } catch (error) {
            showError(error.message);
        }
    });
    
    function monitorProgress() {
        const progressBar = document.getElementById('progress-bar');
        const progressText = document.getElementById('progress-text');
        
        let progress = 0;
        const interval = setInterval(async () => {
            try {
                const response = await fetch('/api/status');
                const status = await response.json();
                
                if (status.status === 'processing') {
                    progress = Math.min(progress + 5, 90);
                    progressBar.style.width = progress + '%';
                    progressText.textContent = '正在处理文档...';
                } else if (status.status === 'completed') {
                    clearInterval(interval);
                    progressBar.style.width = '100%';
                    progressText.textContent = '处理完成！';
                    
                    setTimeout(() => {
                        showResults();
                    }, 1000);
                } else if (status.status === 'failed') {
                    clearInterval(interval);
                    showError('处理过程中发生错误');
                }
            } catch (error) {
                clearInterval(interval);
                showError('无法获取处理状态');
            }
        }, 2000);
    }
    
    function showResults() {
        document.getElementById('progress-section').style.display = 'none';
        document.getElementById('results-section').style.display = 'block';
        document.getElementById('results-text').textContent = '知识图谱构建完成，您可以查看图谱或进行搜索探索。';
        document.getElementById('upload-btn').disabled = false;
    }
    
    function showError(message) {
        document.getElementById('progress-section').style.display = 'none';
        document.getElementById('error-section').style.display = 'block';
        document.getElementById('error-text').textContent = message;
        document.getElementById('upload-btn').disabled = false;
    }
    
    // File input validation
    document.getElementById('files').addEventListener('change', function() {
        const files = this.files;
        const maxSize = 100 * 1024 * 1024; // 100MB
        
        for (let file of files) {
            if (file.size > maxSize) {
                alert(`文件 ${file.name} 超过100MB大小限制`);
                this.value = '';
                return;
            }
        }
    });
</script>
{% endblock %}
