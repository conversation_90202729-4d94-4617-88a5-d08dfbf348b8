{% extends "base.html" %}

{% block title %}Upload Documents - Knowledge Graph Builder{% endblock %}

{% block page_title %}Upload Documents{% endblock %}

{% block header_actions %}
<div class="btn-group" role="group">
    <a href="/" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i>
        Back to Dashboard
    </a>
    <a href="/jobs" class="btn btn-outline-primary">
        <i class="fas fa-tasks me-1"></i>
        View Jobs
    </a>
</div>
{% endblock %}

{% block content %}
<!-- Upload Form -->
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-upload me-2"></i>
                    Upload Documents for Processing
                </h5>
            </div>
            <div class="card-body">
                <form id="upload-form" enctype="multipart/form-data">
                    <!-- File Upload Area -->
                    <div class="mb-4">
                        <label class="form-label">Select Documents</label>
                        <div class="upload-area" id="upload-area">
                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                            <h5>Drag and drop files here</h5>
                            <p class="text-muted">or click to browse</p>
                            <input type="file" id="file-input" multiple accept=".txt,.docx,.pdf" style="display: none;">
                            <div class="mt-3">
                                <small class="text-muted">
                                    Supported formats: TXT, DOCX, PDF, CHAT
                                    <br>
                                    Maximum file size: 100MB per file
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Selected Files -->
                    <div id="selected-files" class="mb-4" style="display: none;">
                        <label class="form-label">Selected Files</label>
                        <div id="file-list" class="border rounded p-3 bg-light">
                            <!-- Files will be listed here -->
                        </div>
                    </div>
                    
                    <!-- Processing Options -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label class="form-label">Processing Mode</label>
                            <select class="form-select" id="processing-mode">
                                <option value="merge" selected>Merge Mode - Unified graph</option>
                                <option value="batch">Batch Mode - Separate graphs</option>
                            </select>
                            <div class="form-text">
                                Merge mode creates one unified knowledge graph. 
                                Batch mode creates separate graphs for each document.
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label">Output Formats</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="format-html" value="html" checked>
                                <label class="form-check-label" for="format-html">
                                    HTML Wiki Pages
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="format-json" value="json" checked>
                                <label class="form-check-label" for="format-json">
                                    JSON Export
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="format-rdf" value="rdf">
                                <label class="form-check-label" for="format-rdf">
                                    RDF/Turtle Export
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Advanced Options -->
                    <div class="mb-4">
                        <button class="btn btn-link p-0" type="button" data-bs-toggle="collapse" data-bs-target="#advanced-options">
                            <i class="fas fa-cog me-1"></i>
                            Advanced Options
                        </button>
                        
                        <div class="collapse mt-3" id="advanced-options">
                            <div class="card card-body bg-light">
                                <div class="row">
                                    <div class="col-md-6">
                                        <label class="form-label">Max Concurrent Workers</label>
                                        <input type="number" class="form-control" id="max-workers" min="1" max="10" value="5">
                                        <div class="form-text">Number of parallel LLM processing workers</div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <label class="form-label">Entity Similarity Threshold</label>
                                        <input type="range" class="form-range" id="similarity-threshold" min="0.5" max="1.0" step="0.05" value="0.85">
                                        <div class="form-text">
                                            Threshold for entity deduplication: <span id="threshold-value">0.85</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Submit Button -->
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg" id="submit-btn">
                            <i class="fas fa-play me-2"></i>
                            Start Processing
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Processing Progress Modal -->
<div class="modal fade" id="progress-modal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-cogs me-2"></i>
                    Processing Documents
                </h5>
            </div>
            <div class="modal-body">
                <div class="progress-container">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span id="progress-label">Initializing...</span>
                        <span id="progress-percentage">0%</span>
                    </div>
                    <div class="progress mb-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             id="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                    
                    <div id="progress-details" class="small text-muted">
                        <!-- Progress details will be shown here -->
                    </div>
                </div>
                
                <div id="processing-logs" class="mt-3" style="max-height: 200px; overflow-y: auto;">
                    <!-- Processing logs will be shown here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancel-btn">Cancel</button>
                <button type="button" class="btn btn-primary" id="view-results-btn" style="display: none;">
                    View Results
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let selectedFiles = [];
let currentJobId = null;
let progressInterval = null;

document.addEventListener('DOMContentLoaded', function() {
    initializeUpload();
    initializeForm();
});

function initializeUpload() {
    const uploadArea = document.getElementById('upload-area');
    const fileInput = document.getElementById('file-input');
    
    // Click to browse
    uploadArea.addEventListener('click', () => fileInput.click());
    
    // File input change
    fileInput.addEventListener('change', handleFileSelect);
    
    // Drag and drop
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });
    
    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
    });
    
    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        handleFileSelect({ target: { files: e.dataTransfer.files } });
    });
}

function initializeForm() {
    // Similarity threshold slider
    const thresholdSlider = document.getElementById('similarity-threshold');
    const thresholdValue = document.getElementById('threshold-value');
    
    thresholdSlider.addEventListener('input', (e) => {
        thresholdValue.textContent = e.target.value;
    });
    
    // Form submission
    document.getElementById('upload-form').addEventListener('submit', handleFormSubmit);
}

function handleFileSelect(event) {
    const files = Array.from(event.target.files);
    
    // Validate files
    const validFiles = files.filter(file => {
        const extension = file.name.split('.').pop().toLowerCase();
        const validExtensions = ['txt', 'docx', 'pdf', 'chat'];
        const maxSize = 100 * 1024 * 1024; // 100MB
        
        if (!validExtensions.includes(extension)) {
            showAlert(`Invalid file type: ${file.name}`, 'warning');
            return false;
        }
        
        if (file.size > maxSize) {
            showAlert(`File too large: ${file.name} (${formatFileSize(file.size)})`, 'warning');
            return false;
        }
        
        return true;
    });
    
    selectedFiles = validFiles;
    displaySelectedFiles();
}

function displaySelectedFiles() {
    const container = document.getElementById('selected-files');
    const fileList = document.getElementById('file-list');
    
    if (selectedFiles.length === 0) {
        container.style.display = 'none';
        return;
    }
    
    container.style.display = 'block';
    
    fileList.innerHTML = selectedFiles.map((file, index) => `
        <div class="d-flex justify-content-between align-items-center mb-2">
            <div>
                <i class="fas fa-file-alt me-2"></i>
                <strong>${file.name}</strong>
                <small class="text-muted ms-2">(${formatFileSize(file.size)})</small>
            </div>
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFile(${index})">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `).join('');
}

function removeFile(index) {
    selectedFiles.splice(index, 1);
    displaySelectedFiles();
}

async function handleFormSubmit(event) {
    event.preventDefault();
    
    if (selectedFiles.length === 0) {
        showAlert('Please select at least one file', 'warning');
        return;
    }
    
    // Collect form data
    const formData = new FormData();
    
    selectedFiles.forEach(file => {
        formData.append('files', file);
    });
    
    const requestData = {
        mode: document.getElementById('processing-mode').value,
        output_formats: getSelectedFormats(),
        max_workers: parseInt(document.getElementById('max-workers').value),
        similarity_threshold: parseFloat(document.getElementById('similarity-threshold').value)
    };
    
    formData.append('request_data', JSON.stringify(requestData));
    
    try {
        // Start processing
        const response = await fetch('/api/process', {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || 'Failed to start processing');
        }
        
        const result = await response.json();
        currentJobId = result.job_id;
        
        // Show progress modal
        showProgressModal();
        startProgressTracking();
        
    } catch (error) {
        showAlert(`Failed to start processing: ${error.message}`, 'danger');
    }
}

function getSelectedFormats() {
    const formats = [];
    if (document.getElementById('format-html').checked) formats.push('html');
    if (document.getElementById('format-json').checked) formats.push('json');
    if (document.getElementById('format-rdf').checked) formats.push('rdf');
    return formats;
}

function showProgressModal() {
    const modal = new bootstrap.Modal(document.getElementById('progress-modal'));
    modal.show();
}

function startProgressTracking() {
    if (!currentJobId) return;
    
    progressInterval = setInterval(async () => {
        try {
            const response = await fetch(`/api/status/${currentJobId}`);
            const status = await response.json();
            
            updateProgress(status);
            
            if (status.status === 'completed' || status.status === 'failed') {
                clearInterval(progressInterval);
                handleProcessingComplete(status);
            }
            
        } catch (error) {
            console.error('Failed to get progress:', error);
        }
    }, 2000);
}

function updateProgress(status) {
    const progressBar = document.getElementById('progress-bar');
    const progressLabel = document.getElementById('progress-label');
    const progressPercentage = document.getElementById('progress-percentage');
    const progressDetails = document.getElementById('progress-details');
    
    const percentage = Math.round(status.progress * 100);
    
    progressBar.style.width = `${percentage}%`;
    progressLabel.textContent = status.message;
    progressPercentage.textContent = `${percentage}%`;
    
    // Update progress bar color based on status
    progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated';
    if (status.status === 'failed') {
        progressBar.classList.add('bg-danger');
    } else if (status.status === 'completed') {
        progressBar.classList.add('bg-success');
        progressBar.classList.remove('progress-bar-animated');
    }
    
    // Show additional details if available
    if (status.result && status.result.summary) {
        const summary = status.result.summary;
        progressDetails.innerHTML = `
            <div class="row">
                <div class="col-md-3">Documents: ${summary.documents_processed || 0}</div>
                <div class="col-md-3">Entities: ${summary.entities_created || 0}</div>
                <div class="col-md-3">Relations: ${summary.relations_created || 0}</div>
                <div class="col-md-3">Time: ${formatDuration(summary.execution_time || 0)}</div>
            </div>
        `;
    }
}

function handleProcessingComplete(status) {
    const cancelBtn = document.getElementById('cancel-btn');
    const viewResultsBtn = document.getElementById('view-results-btn');
    
    if (status.status === 'completed') {
        showAlert('Processing completed successfully!', 'success');
        cancelBtn.textContent = 'Close';
        viewResultsBtn.style.display = 'inline-block';
        
        viewResultsBtn.onclick = () => {
            if (status.result && status.result.wiki_path) {
                window.open(status.result.wiki_path + '/index.html', '_blank');
            } else {
                window.location.href = '/search';
            }
        };
    } else {
        showAlert(`Processing failed: ${status.error || 'Unknown error'}`, 'danger');
        cancelBtn.textContent = 'Close';
    }
}

// Cancel processing
document.getElementById('cancel-btn').addEventListener('click', () => {
    if (progressInterval) {
        clearInterval(progressInterval);
    }
    
    if (currentJobId && document.getElementById('cancel-btn').textContent === 'Cancel') {
        // TODO: Implement job cancellation API
        fetch(`/api/jobs/${currentJobId}`, { method: 'DELETE' })
            .catch(error => console.error('Failed to cancel job:', error));
    }
    
    const modal = bootstrap.Modal.getInstance(document.getElementById('progress-modal'));
    modal.hide();
    
    // Reset form
    selectedFiles = [];
    displaySelectedFiles();
    document.getElementById('upload-form').reset();
});
</script>
{% endblock %}
