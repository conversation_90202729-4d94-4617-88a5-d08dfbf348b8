"""Graph operations for knowledge graph management."""

import hashlib
import json
from typing import Dict, List, Set, Tuple, Optional, Any
from dataclasses import dataclass, asdict
from collections import defaultdict

import networkx as nx
from jaro import jaro_winkler_similarity
from loguru import logger


@dataclass
class Entity:
    """Represents an entity in the knowledge graph."""
    canonical_name: str
    entity_type: str
    description: str = ""
    alternatives: List[str] = None
    tags: List[str] = None
    source_weight: int = 1
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.alternatives is None:
            self.alternatives = []
        if self.tags is None:
            self.tags = []
        if self.metadata is None:
            self.metadata = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert entity to dictionary."""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Entity':
        """Create entity from dictionary."""
        return cls(**data)


@dataclass
class Relation:
    """Represents a relationship in the knowledge graph."""
    source: str
    target: str
    relation_type: str
    description: str = ""
    confidence: float = 1.0
    source_weight: int = 1
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert relation to dictionary."""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Relation':
        """Create relation from dictionary."""
        return cls(**data)


class GraphOperations:
    """Operations for knowledge graph construction and management."""
    
    def __init__(self, similarity_threshold: float = 0.85):
        self.similarity_threshold = similarity_threshold
        self.graph = nx.MultiDiGraph()
    
    @staticmethod
    def generate_stable_id(name: str) -> str:
        """
        Generate stable ID for entity based on canonical name.
        
        Args:
            name: Entity canonical name
            
        Returns:
            Stable ID string
        """
        # Normalize name for ID generation
        normalized = name.lower().strip()
        return hashlib.md5(normalized.encode('utf-8')).hexdigest()[:12]
    
    def calculate_similarity(self, name1: str, name2: str) -> float:
        """
        Calculate similarity between two entity names.
        
        Args:
            name1: First entity name
            name2: Second entity name
            
        Returns:
            Similarity score between 0 and 1
        """
        # Normalize names
        name1 = name1.lower().strip()
        name2 = name2.lower().strip()
        
        if name1 == name2:
            return 1.0
        
        # Use Jaro-Winkler similarity
        return jaro_winkler_similarity(name1, name2)
    
    def find_similar_entities(self, entity: Entity, existing_entities: List[Entity]) -> List[Tuple[Entity, float]]:
        """
        Find similar entities in existing list.
        
        Args:
            entity: Entity to find similarities for
            existing_entities: List of existing entities
            
        Returns:
            List of (entity, similarity_score) tuples above threshold
        """
        similar = []
        
        for existing in existing_entities:
            # Check canonical name similarity
            similarity = self.calculate_similarity(entity.canonical_name, existing.canonical_name)
            
            if similarity >= self.similarity_threshold:
                similar.append((existing, similarity))
                continue
            
            # Check against alternatives
            for alt in existing.alternatives:
                alt_similarity = self.calculate_similarity(entity.canonical_name, alt)
                if alt_similarity >= self.similarity_threshold:
                    similar.append((existing, alt_similarity))
                    break
        
        # Sort by similarity score (descending)
        similar.sort(key=lambda x: x[1], reverse=True)
        return similar
    
    def merge_entities(self, entity1: Entity, entity2: Entity) -> Entity:
        """
        Merge two similar entities.
        
        Args:
            entity1: First entity
            entity2: Second entity
            
        Returns:
            Merged entity
        """
        # Choose canonical name based on source weight and length
        if entity1.source_weight > entity2.source_weight:
            canonical_name = entity1.canonical_name
        elif entity1.source_weight < entity2.source_weight:
            canonical_name = entity2.canonical_name
        else:
            # Same weight, choose longer/more descriptive name
            canonical_name = entity1.canonical_name if len(entity1.canonical_name) >= len(entity2.canonical_name) else entity2.canonical_name
        
        # Merge alternatives
        alternatives = list(set(entity1.alternatives + entity2.alternatives))
        if entity1.canonical_name != canonical_name and entity1.canonical_name not in alternatives:
            alternatives.append(entity1.canonical_name)
        if entity2.canonical_name != canonical_name and entity2.canonical_name not in alternatives:
            alternatives.append(entity2.canonical_name)
        
        # Choose better description
        description = entity1.description if len(entity1.description) > len(entity2.description) else entity2.description
        
        # Merge tags
        tags = list(set(entity1.tags + entity2.tags))
        
        # Choose entity type (prefer more specific)
        entity_type = entity1.entity_type if entity1.entity_type != "unknown" else entity2.entity_type
        
        # Merge metadata
        metadata = {**entity1.metadata, **entity2.metadata}
        
        return Entity(
            canonical_name=canonical_name,
            entity_type=entity_type,
            description=description,
            alternatives=alternatives,
            tags=tags,
            source_weight=max(entity1.source_weight, entity2.source_weight),
            metadata=metadata
        )
    
    def normalize_entities(self, entities: List[Entity]) -> List[Entity]:
        """
        Normalize and deduplicate entities.
        
        Args:
            entities: List of entities to normalize
            
        Returns:
            List of normalized entities
        """
        if not entities:
            return []
        
        normalized = []
        processed_names = set()
        
        for entity in entities:
            if entity.canonical_name.lower() in processed_names:
                continue
            
            # Find similar entities in the current list
            similar = self.find_similar_entities(entity, normalized)
            
            if similar:
                # Merge with most similar entity
                most_similar_entity, _ = similar[0]
                # Remove the similar entity from normalized list
                normalized = [e for e in normalized if e.canonical_name != most_similar_entity.canonical_name]
                # Add merged entity
                merged = self.merge_entities(entity, most_similar_entity)
                normalized.append(merged)
                processed_names.add(merged.canonical_name.lower())
            else:
                normalized.append(entity)
                processed_names.add(entity.canonical_name.lower())
        
        logger.info(f"Normalized {len(entities)} entities to {len(normalized)} entities")
        return normalized
    
    def create_graph_from_entities_relations(
        self,
        entities: List[Entity],
        relations: List[Relation]
    ) -> nx.MultiDiGraph:
        """
        Create NetworkX graph from entities and relations.
        
        Args:
            entities: List of entities
            relations: List of relations
            
        Returns:
            NetworkX MultiDiGraph
        """
        graph = nx.MultiDiGraph()
        
        # Add nodes (entities)
        for entity in entities:
            node_id = self.generate_stable_id(entity.canonical_name)
            graph.add_node(node_id, **entity.to_dict())
        
        # Create name to ID mapping for relations
        name_to_id = {entity.canonical_name: self.generate_stable_id(entity.canonical_name) for entity in entities}
        
        # Add edges (relations)
        for relation in relations:
            source_id = name_to_id.get(relation.source)
            target_id = name_to_id.get(relation.target)
            
            if source_id and target_id:
                graph.add_edge(source_id, target_id, **relation.to_dict())
            else:
                logger.warning(f"Skipping relation {relation.source} -> {relation.target}: entity not found")
        
        logger.info(f"Created graph with {graph.number_of_nodes()} nodes and {graph.number_of_edges()} edges")
        return graph
    
    def merge_graphs(self, graph1: nx.MultiDiGraph, graph2: nx.MultiDiGraph) -> nx.MultiDiGraph:
        """
        Merge two knowledge graphs with conflict resolution.
        
        Args:
            graph1: First graph
            graph2: Second graph
            
        Returns:
            Merged graph
        """
        merged = graph1.copy()
        
        # Merge nodes
        for node_id, node_data in graph2.nodes(data=True):
            if node_id in merged.nodes:
                # Node exists, merge data
                existing_data = merged.nodes[node_id]
                existing_entity = Entity.from_dict(existing_data)
                new_entity = Entity.from_dict(node_data)
                
                merged_entity = self.merge_entities(existing_entity, new_entity)
                merged.nodes[node_id].update(merged_entity.to_dict())
            else:
                # New node
                merged.add_node(node_id, **node_data)
        
        # Merge edges
        for source, target, edge_data in graph2.edges(data=True):
            # Check if similar edge exists
            existing_edges = merged.get_edge_data(source, target)
            
            if existing_edges:
                # Edge exists, check for duplicates
                new_relation = Relation.from_dict(edge_data)
                duplicate_found = False
                
                for key, existing_edge_data in existing_edges.items():
                    existing_relation = Relation.from_dict(existing_edge_data)
                    
                    if (existing_relation.relation_type == new_relation.relation_type and
                        self.calculate_similarity(existing_relation.description, new_relation.description) > 0.8):
                        # Duplicate relation, merge
                        if new_relation.confidence > existing_relation.confidence:
                            merged.edges[source, target, key].update(new_relation.to_dict())
                        duplicate_found = True
                        break
                
                if not duplicate_found:
                    merged.add_edge(source, target, **edge_data)
            else:
                merged.add_edge(source, target, **edge_data)
        
        logger.info(f"Merged graphs: {merged.number_of_nodes()} nodes, {merged.number_of_edges()} edges")
        return merged
    
    def export_to_dict(self, graph: nx.MultiDiGraph) -> Dict[str, Any]:
        """
        Export graph to dictionary format.
        
        Args:
            graph: NetworkX graph
            
        Returns:
            Dictionary representation of graph
        """
        nodes = {}
        edges = []
        
        for node_id, node_data in graph.nodes(data=True):
            nodes[node_id] = node_data
        
        for source, target, edge_data in graph.edges(data=True):
            edges.append({
                "source": source,
                "target": target,
                **edge_data
            })
        
        return {
            "nodes": nodes,
            "edges": edges,
            "metadata": {
                "node_count": graph.number_of_nodes(),
                "edge_count": graph.number_of_edges(),
                "is_directed": graph.is_directed()
            }
        }
    
    def import_from_dict(self, data: Dict[str, Any]) -> nx.MultiDiGraph:
        """
        Import graph from dictionary format.
        
        Args:
            data: Dictionary representation of graph
            
        Returns:
            NetworkX graph
        """
        graph = nx.MultiDiGraph()
        
        # Add nodes
        for node_id, node_data in data["nodes"].items():
            graph.add_node(node_id, **node_data)
        
        # Add edges
        for edge_data in data["edges"]:
            source = edge_data.pop("source")
            target = edge_data.pop("target")
            graph.add_edge(source, target, **edge_data)
        
        return graph
