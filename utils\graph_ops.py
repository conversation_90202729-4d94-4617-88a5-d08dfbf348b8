"""
Knowledge graph operations
Handles entity relation parsing, merge logic, and conflict resolution
"""

import hashlib
import uuid
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, field
from collections import defaultdict
import networkx as nx
from loguru import logger
from jaro import jaro_winkler_similarity


@dataclass
class Entity:
    """Entity data structure"""
    id: str
    name: str
    canonical_name: str
    type: str
    description: str = ""
    tags: List[str] = field(default_factory=list)
    alternatives: List[str] = field(default_factory=list)
    confidence: float = 1.0
    source_weight: int = 1
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Relation:
    """Relation data structure"""
    id: str
    source: str  # Entity ID
    target: str  # Entity ID
    relation_type: str
    description: str = ""
    confidence: float = 1.0
    weight: float = 1.0
    metadata: Dict[str, Any] = field(default_factory=dict)


class GraphOperations:
    """Knowledge graph operations and utilities"""
    
    def __init__(self, similarity_threshold: float = 0.85, 
                 merge_strategy: str = "weighted_priority"):
        self.similarity_threshold = similarity_threshold
        self.merge_strategy = merge_strategy
        self.entity_cache = {}
        self.relation_cache = {}
    
    def generate_entity_id(self, name: str, entity_type: str = "") -> str:
        """Generate stable entity ID"""
        # Use canonical name for consistent IDs
        canonical = self.canonicalize_name(name)
        content = f"{canonical}:{entity_type}".lower()
        return hashlib.md5(content.encode('utf-8')).hexdigest()[:16]
    
    def generate_relation_id(self, source_id: str, target_id: str, 
                           relation_type: str) -> str:
        """Generate stable relation ID"""
        content = f"{source_id}:{target_id}:{relation_type}".lower()
        return hashlib.md5(content.encode('utf-8')).hexdigest()[:16]
    
    def canonicalize_name(self, name: str) -> str:
        """Canonicalize entity name"""
        # Remove extra whitespace and normalize
        canonical = ' '.join(name.strip().split())
        
        # Basic normalization (can be enhanced)
        canonical = canonical.replace('（', '(').replace('）', ')')
        canonical = canonical.replace('【', '[').replace('】', ']')
        
        return canonical
    
    def calculate_similarity(self, name1: str, name2: str) -> float:
        """Calculate similarity between two entity names"""
        # Normalize names
        n1 = self.canonicalize_name(name1).lower()
        n2 = self.canonicalize_name(name2).lower()
        
        # Exact match
        if n1 == n2:
            return 1.0
        
        # Jaro-Winkler similarity
        jw_sim = jaro_winkler_similarity(n1, n2)
        
        # Boost for substring matches
        if n1 in n2 or n2 in n1:
            jw_sim = min(1.0, jw_sim + 0.1)
        
        return jw_sim
    
    def find_similar_entities(self, entity: Entity, 
                            existing_entities: List[Entity]) -> List[Tuple[Entity, float]]:
        """Find similar entities in existing list"""
        similar = []
        
        for existing in existing_entities:
            # Skip if different types (unless one is unknown)
            if (entity.type != existing.type and 
                entity.type != "unknown" and existing.type != "unknown"):
                continue
            
            # Calculate similarity
            similarity = self.calculate_similarity(entity.name, existing.name)
            
            # Check alternatives
            for alt in entity.alternatives:
                alt_sim = self.calculate_similarity(alt, existing.name)
                similarity = max(similarity, alt_sim)
            
            for alt in existing.alternatives:
                alt_sim = self.calculate_similarity(entity.name, alt)
                similarity = max(similarity, alt_sim)
            
            if similarity >= self.similarity_threshold:
                similar.append((existing, similarity))
        
        # Sort by similarity descending
        similar.sort(key=lambda x: x[1], reverse=True)
        return similar
    
    def merge_entities(self, entity1: Entity, entity2: Entity) -> Entity:
        """Merge two similar entities"""
        
        # Determine which entity to use as base
        if self.merge_strategy == "weighted_priority":
            if entity1.source_weight > entity2.source_weight:
                base, other = entity1, entity2
            elif entity1.source_weight < entity2.source_weight:
                base, other = entity2, entity1
            else:
                # Same weight, prefer higher confidence
                base, other = (entity1, entity2) if entity1.confidence >= entity2.confidence else (entity2, entity1)
        else:
            # Default to first entity
            base, other = entity1, entity2
        
        # Create merged entity
        merged = Entity(
            id=base.id,
            name=base.name,
            canonical_name=base.canonical_name,
            type=base.type if base.type != "unknown" else other.type,
            description=self._merge_descriptions(base.description, other.description),
            tags=list(set(base.tags + other.tags)),
            alternatives=list(set(base.alternatives + other.alternatives + [other.name])),
            confidence=max(base.confidence, other.confidence),
            source_weight=max(base.source_weight, other.source_weight),
            metadata={**other.metadata, **base.metadata}  # Base metadata takes precedence
        )
        
        # Remove self-references from alternatives
        merged.alternatives = [alt for alt in merged.alternatives if alt != merged.name]
        
        return merged
    
    def _merge_descriptions(self, desc1: str, desc2: str) -> str:
        """Merge two descriptions"""
        if not desc1:
            return desc2
        if not desc2:
            return desc1
        
        # Use longer description if one is significantly longer
        if len(desc1) > len(desc2) * 1.5:
            return desc1
        elif len(desc2) > len(desc1) * 1.5:
            return desc2
        
        # Combine descriptions
        return f"{desc1}; {desc2}"
    
    def normalize_entities(self, entities: List[Dict[str, Any]], 
                          source_weight: int = 1) -> List[Entity]:
        """Normalize and deduplicate entities"""
        
        normalized_entities = []
        
        for entity_data in entities:
            # Create entity object
            entity = Entity(
                id="",  # Will be set after canonicalization
                name=entity_data.get('name', ''),
                canonical_name=self.canonicalize_name(entity_data.get('name', '')),
                type=entity_data.get('type', 'unknown'),
                description=entity_data.get('description', ''),
                confidence=entity_data.get('confidence', 1.0),
                source_weight=source_weight,
                tags=entity_data.get('tags', []),
                alternatives=entity_data.get('alternatives', [])
            )
            
            # Generate ID
            entity.id = self.generate_entity_id(entity.canonical_name, entity.type)
            
            # Find similar entities
            similar = self.find_similar_entities(entity, normalized_entities)
            
            if similar:
                # Merge with most similar entity
                most_similar, similarity = similar[0]
                logger.debug(f"Merging entities: {entity.name} -> {most_similar.name} (similarity: {similarity:.3f})")
                
                # Remove the similar entity and add merged entity
                normalized_entities = [e for e in normalized_entities if e.id != most_similar.id]
                merged_entity = self.merge_entities(entity, most_similar)
                normalized_entities.append(merged_entity)
            else:
                # Add as new entity
                normalized_entities.append(entity)
        
        return normalized_entities
    
    def normalize_relations(self, relations: List[Dict[str, Any]], 
                          entity_map: Dict[str, Entity]) -> List[Relation]:
        """Normalize relations with entity mapping"""
        
        normalized_relations = []
        
        for relation_data in relations:
            source_name = relation_data.get('source', '')
            target_name = relation_data.get('target', '')
            
            # Find corresponding entities
            source_entity = self._find_entity_by_name(source_name, entity_map)
            target_entity = self._find_entity_by_name(target_name, entity_map)
            
            if not source_entity or not target_entity:
                logger.warning(f"Skipping relation: {source_name} -> {target_name} (entities not found)")
                continue
            
            # Create relation
            relation = Relation(
                id=self.generate_relation_id(source_entity.id, target_entity.id, 
                                           relation_data.get('relation', '')),
                source=source_entity.id,
                target=target_entity.id,
                relation_type=relation_data.get('relation', ''),
                description=relation_data.get('description', ''),
                confidence=relation_data.get('confidence', 1.0)
            )
            
            normalized_relations.append(relation)
        
        return normalized_relations
    
    def _find_entity_by_name(self, name: str, entity_map: Dict[str, Entity]) -> Optional[Entity]:
        """Find entity by name or alternative names"""
        canonical_name = self.canonicalize_name(name)
        
        # Direct lookup
        for entity in entity_map.values():
            if entity.canonical_name == canonical_name:
                return entity
            
            # Check alternatives
            for alt in entity.alternatives:
                if self.canonicalize_name(alt) == canonical_name:
                    return entity
        
        return None
    
    def build_networkx_graph(self, entities: List[Entity], 
                           relations: List[Relation]) -> nx.DiGraph:
        """Build NetworkX graph from entities and relations"""
        
        graph = nx.DiGraph()
        
        # Add nodes
        for entity in entities:
            graph.add_node(entity.id, **{
                'name': entity.name,
                'canonical_name': entity.canonical_name,
                'type': entity.type,
                'description': entity.description,
                'tags': entity.tags,
                'alternatives': entity.alternatives,
                'confidence': entity.confidence,
                'source_weight': entity.source_weight
            })
        
        # Add edges
        for relation in relations:
            if graph.has_node(relation.source) and graph.has_node(relation.target):
                graph.add_edge(relation.source, relation.target, **{
                    'relation_type': relation.relation_type,
                    'description': relation.description,
                    'confidence': relation.confidence,
                    'weight': relation.weight
                })
        
        return graph
    
    def merge_graphs(self, graph1: nx.DiGraph, graph2: nx.DiGraph) -> nx.DiGraph:
        """Merge two NetworkX graphs"""
        
        merged = graph1.copy()
        
        # Merge nodes
        for node_id, node_data in graph2.nodes(data=True):
            if node_id in merged.nodes:
                # Merge node data
                existing_data = merged.nodes[node_id]
                merged_data = self._merge_node_data(existing_data, node_data)
                merged.nodes[node_id].update(merged_data)
            else:
                merged.add_node(node_id, **node_data)
        
        # Merge edges
        for source, target, edge_data in graph2.edges(data=True):
            if merged.has_edge(source, target):
                # Merge edge data
                existing_data = merged.edges[source, target]
                merged_data = self._merge_edge_data(existing_data, edge_data)
                merged.edges[source, target].update(merged_data)
            else:
                merged.add_edge(source, target, **edge_data)
        
        return merged
    
    def _merge_node_data(self, data1: Dict[str, Any], data2: Dict[str, Any]) -> Dict[str, Any]:
        """Merge node data"""
        merged = data1.copy()
        
        # Merge tags
        if 'tags' in data2:
            merged['tags'] = list(set(merged.get('tags', []) + data2['tags']))
        
        # Merge alternatives
        if 'alternatives' in data2:
            merged['alternatives'] = list(set(merged.get('alternatives', []) + data2['alternatives']))
        
        # Use better description
        if 'description' in data2 and len(data2['description']) > len(merged.get('description', '')):
            merged['description'] = data2['description']
        
        # Use higher confidence
        if 'confidence' in data2:
            merged['confidence'] = max(merged.get('confidence', 0), data2['confidence'])
        
        return merged
    
    def _merge_edge_data(self, data1: Dict[str, Any], data2: Dict[str, Any]) -> Dict[str, Any]:
        """Merge edge data"""
        merged = data1.copy()
        
        # Increase weight for repeated relations
        merged['weight'] = merged.get('weight', 1.0) + data2.get('weight', 1.0)
        
        # Use higher confidence
        if 'confidence' in data2:
            merged['confidence'] = max(merged.get('confidence', 0), data2['confidence'])
        
        return merged
