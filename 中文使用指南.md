# 🌐 知识图谱构建器 - 中文界面使用指南

## 🎉 界面已完全中文化！

### 📱 Web 界面功能

现在的中文界面包含以下功能：

#### 🏠 主页面
- **导航栏**: "知识图谱构建器" + "帮助"按钮
- **欢迎信息**: 功能介绍和使用说明
- **文档上传区**: 支持拖拽上传，文件格式提示
- **处理模式选择**: 合并模式 vs 批处理模式
- **功能说明**: 文档处理、实体提取、关系发现

#### 📤 文件上传
- **拖拽上传**: 将文件拖拽到上传区域
- **点击上传**: 点击"浏览文件"按钮选择文件
- **文件列表**: 显示已选择的文件名和大小
- **格式支持**: TXT、MD 文件

#### ⚙️ 处理选项
- **合并模式**: 将所有文档合并为一个统一的知识图谱
- **批处理模式**: 为每个文档创建独立的知识图谱
- **详细说明**: 每种模式的用途和效果

#### 📊 结果展示
- **处理统计**: 文档数、实体数、关系数、处理时间
- **图谱统计**: 总实体数、总关系数、实体类型分布
- **实体类型**: 人员、组织、地点、概念、事件、产品
- **操作按钮**: 下载数据、搜索实体、重新处理

#### 🔍 搜索功能
- **实体搜索**: 输入关键词搜索相关实体
- **结果展示**: 实体名称、类型、描述、相关度
- **中文类型**: 人员、组织、地点等中文显示

### 🚀 使用步骤

1. **打开浏览器**: 访问 http://localhost:8001
2. **上传文件**: 拖拽或选择 TXT/MD 文件
3. **选择模式**: 合并模式或批处理模式
4. **开始处理**: 点击"开始处理"按钮
5. **查看结果**: 查看统计信息和图谱数据
6. **下载数据**: 下载 JSON 格式的知识图谱
7. **搜索实体**: 搜索特定的实体信息

### 📁 测试文件

已准备了中文测试文件：

#### `test_documents/中文示例.txt`
- 包含：张三、李四、北京大学、清华大学等
- 领域：学术研究、人工智能
- 关系：合作、研究、位于等

#### `test_documents/企业案例.txt`
- 包含：王五、赵六、华为、小米、字节跳动等
- 领域：科技企业、产品开发
- 关系：工作、负责、开发等

### 🎯 界面特色

#### 🎨 视觉设计
- **响应式布局**: 适配不同屏幕尺寸
- **Bootstrap 5**: 现代化的 UI 组件
- **Font Awesome**: 丰富的图标系统
- **渐变效果**: 拖拽上传的视觉反馈

#### 💬 中文本地化
- **完整中文**: 所有界面文本都已中文化
- **错误提示**: 中文错误信息和提示
- **帮助说明**: 中文使用说明和功能介绍
- **实体类型**: 中文实体类型显示

#### 🔧 交互功能
- **拖拽上传**: 直观的文件上传方式
- **实时反馈**: 处理进度和状态提示
- **搜索功能**: 智能实体搜索
- **数据导出**: 一键下载 JSON 数据

### 📊 API 端点 (中文化)

所有 API 端点的错误消息都已中文化：

- `POST /api/process` - 文档处理
  - 错误：未上传任何文件、不支持的文件格式、处理失败
- `POST /api/search` - 实体搜索
  - 错误：暂无可用图谱、搜索关键词不能为空、搜索失败
- `GET /api/entity/{name}` - 实体详情
  - 错误：暂无可用图谱、未找到该实体、获取信息失败
- `GET /api/statistics` - 统计信息
  - 错误：暂无可用图谱、获取统计信息失败

### 🎉 完成的改进

#### ✅ 界面中文化
- [x] 页面标题和导航栏
- [x] 表单标签和按钮
- [x] 提示信息和说明文字
- [x] 错误消息和警告
- [x] 结果展示和统计信息

#### ✅ 功能增强
- [x] 拖拽上传功能
- [x] 文件列表显示
- [x] 处理进度提示
- [x] 搜索结果优化
- [x] 帮助说明功能

#### ✅ 用户体验
- [x] 响应式设计
- [x] 视觉反馈效果
- [x] 操作流程优化
- [x] 错误处理改进
- [x] 成功提示消息

### 🚀 立即体验

1. **访问中文界面**: http://localhost:8001
2. **上传中文测试文件**: 使用 `test_documents/中文示例.txt`
3. **体验完整流程**: 上传 → 处理 → 查看结果 → 搜索实体
4. **下载中文数据**: 获取包含中文实体的 JSON 文件

现在您可以享受完全中文化的知识图谱构建体验！🎊
