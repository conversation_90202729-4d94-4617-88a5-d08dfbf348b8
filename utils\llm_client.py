"""LLM client for communication with language models."""

import asyncio
import json
import time
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass

import aiohttp
import tiktoken
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
from loguru import logger

from config.settings import Settings


@dataclass
class LLMResponse:
    """Response from LLM API."""
    content: str
    model: str
    usage: Optional[Dict] = None
    finish_reason: Optional[str] = None
    response_time: Optional[float] = None


class LLMClient:
    """Client for communicating with LLM APIs."""
    
    def __init__(self, settings: Optional[Settings] = None):
        self.settings = settings or Settings()
        self.session: Optional[aiohttp.ClientSession] = None
        self.rate_limiter = asyncio.Semaphore(self.settings.max_workers)
        
        # Initialize tokenizer for token counting
        try:
            self.tokenizer = tiktoken.get_encoding("cl100k_base")
        except Exception:
            self.tokenizer = None
            logger.warning("Could not initialize tokenizer, token counting will be approximate")
    
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.settings.segment_timeout_seconds),
            headers={
                "Authorization": f"Bearer {self.settings.llm_api_key}",
                "Content-Type": "application/json"
            }
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    def count_tokens(self, text: str) -> int:
        """
        Count tokens in text.
        
        Args:
            text: Text to count tokens for
            
        Returns:
            Number of tokens
        """
        if self.tokenizer:
            return len(self.tokenizer.encode(text))
        else:
            # Rough approximation
            return len(text.split()) * 1.3
    
    def format_messages(self, prompt: str, system_prompt: Optional[str] = None) -> List[Dict]:
        """
        Format messages for OpenAI-compatible API.
        
        Args:
            prompt: User prompt
            system_prompt: Optional system prompt
            
        Returns:
            List of message dictionaries
        """
        messages = []
        
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        
        messages.append({"role": "user", "content": prompt})
        
        return messages
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((aiohttp.ClientError, asyncio.TimeoutError))
    )
    async def call_llm(
        self,
        prompt: str,
        model: Optional[str] = None,
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> LLMResponse:
        """
        Call LLM API with retry logic.
        
        Args:
            prompt: User prompt
            model: Model name (if None, uses default)
            system_prompt: Optional system prompt
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            **kwargs: Additional parameters
            
        Returns:
            LLMResponse object
            
        Raises:
            Exception: If API call fails after retries
        """
        if not self.session:
            raise RuntimeError("LLMClient must be used as async context manager")
        
        model = model or self.settings.llm_model_local
        messages = self.format_messages(prompt, system_prompt)
        
        # Prepare request payload
        payload = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            **kwargs
        }
        
        if max_tokens:
            payload["max_tokens"] = max_tokens
        
        start_time = time.time()
        
        async with self.rate_limiter:
            try:
                async with self.session.post(
                    f"{self.settings.llm_base_url}/chat/completions",
                    json=payload
                ) as response:
                    response.raise_for_status()
                    data = await response.json()
                    
                    response_time = time.time() - start_time
                    
                    # Extract response content
                    choice = data["choices"][0]
                    content = choice["message"]["content"]
                    finish_reason = choice.get("finish_reason")
                    usage = data.get("usage")
                    
                    logger.debug(f"LLM call successful: {model}, {response_time:.2f}s")
                    
                    return LLMResponse(
                        content=content,
                        model=model,
                        usage=usage,
                        finish_reason=finish_reason,
                        response_time=response_time
                    )
                    
            except aiohttp.ClientResponseError as e:
                logger.error(f"LLM API error: {e.status} - {e.message}")
                raise
            except Exception as e:
                logger.error(f"LLM call failed: {e}")
                raise
    
    async def call_llm_batch(
        self,
        prompts: List[str],
        model: Optional[str] = None,
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> List[LLMResponse]:
        """
        Call LLM API for multiple prompts in parallel.
        
        Args:
            prompts: List of prompts
            model: Model name
            system_prompt: Optional system prompt
            **kwargs: Additional parameters
            
        Returns:
            List of LLMResponse objects
        """
        tasks = []
        
        for i, prompt in enumerate(prompts):
            # Alternate between models for load balancing
            selected_model = model or self.settings.get_model_for_index(i)
            
            task = self.call_llm(
                prompt=prompt,
                model=selected_model,
                system_prompt=system_prompt,
                **kwargs
            )
            tasks.append(task)
        
        return await asyncio.gather(*tasks)
    
    def validate_response(self, response: LLMResponse, expected_format: Optional[str] = None) -> bool:
        """
        Validate LLM response.
        
        Args:
            response: LLM response to validate
            expected_format: Expected format (json, yaml, etc.)
            
        Returns:
            True if response is valid
        """
        if not response.content or not response.content.strip():
            return False
        
        if expected_format == "json":
            try:
                json.loads(response.content)
                return True
            except json.JSONDecodeError:
                return False
        
        # Add more format validations as needed
        
        return True
    
    async def extract_entities_and_relations(
        self,
        text: str,
        model: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Extract entities and relations from text using LLM.
        
        Args:
            text: Text to process
            model: Model to use
            
        Returns:
            Dictionary with entities and relations
        """
        system_prompt = """You are an expert knowledge graph builder. Extract entities and relationships from the given text.

Return your response in the following JSON format:
{
    "entities": [
        {
            "name": "entity name",
            "type": "entity type (person, organization, concept, etc.)",
            "description": "brief description"
        }
    ],
    "relations": [
        {
            "source": "source entity name",
            "target": "target entity name", 
            "relation": "relationship type",
            "description": "brief description of the relationship"
        }
    ]
}

Focus on extracting meaningful entities and relationships. Avoid overly generic entities."""
        
        prompt = f"Extract entities and relationships from the following text:\n\n{text}"
        
        response = await self.call_llm(
            prompt=prompt,
            system_prompt=system_prompt,
            model=model,
            temperature=0.3  # Lower temperature for more consistent extraction
        )
        
        try:
            # Try to parse JSON response
            result = json.loads(response.content)
            
            # Validate structure
            if "entities" in result and "relations" in result:
                return result
            else:
                logger.warning("LLM response missing required fields")
                return {"entities": [], "relations": []}
                
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse LLM response as JSON: {e}")
            logger.debug(f"Response content: {response.content}")
            return {"entities": [], "relations": []}
    
    async def enhance_entity_description(
        self,
        entity_name: str,
        entity_type: str,
        context: str,
        model: Optional[str] = None
    ) -> str:
        """
        Enhance entity description using LLM.
        
        Args:
            entity_name: Name of the entity
            entity_type: Type of the entity
            context: Context where entity appears
            model: Model to use
            
        Returns:
            Enhanced description
        """
        prompt = f"""Given the entity "{entity_name}" of type "{entity_type}" that appears in the following context:

{context}

Provide a concise but informative description of this entity (maximum 2 sentences). Focus on what makes this entity significant or notable based on the context."""
        
        response = await self.call_llm(
            prompt=prompt,
            model=model,
            temperature=0.5
        )
        
        return response.content.strip()
