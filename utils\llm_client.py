"""
LLM client for OpenAI-compatible API
Handles model requests, rate limiting, retry logic, and response parsing
"""

import asyncio
import json
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import aiohttp
from loguru import logger
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type


@dataclass
class LLMResponse:
    """LLM response data structure"""
    content: str
    model: str
    usage: Dict[str, int]
    finish_reason: str
    response_time: float


class LLMClient:
    """OpenAI-compatible LLM client with rate limiting and retry logic"""
    
    def __init__(self, base_url: str, api_key: str, models: Dict[str, str], 
                 max_tokens: int = 4000, temperature: float = 0.1, 
                 timeout: int = 300, max_retries: int = 3):
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.models = models
        self.max_tokens = max_tokens
        self.temperature = temperature
        self.timeout = timeout
        self.max_retries = max_retries
        
        # Rate limiting
        self.request_times = []
        self.max_requests_per_minute = 60
        
        # Session for connection pooling
        self.session = None
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.timeout),
            headers={'Authorization': f'Bearer {self.api_key}'}
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    def _rate_limit(self):
        """Simple rate limiting"""
        now = time.time()
        # Remove requests older than 1 minute
        self.request_times = [t for t in self.request_times if now - t < 60]
        
        if len(self.request_times) >= self.max_requests_per_minute:
            sleep_time = 60 - (now - self.request_times[0])
            if sleep_time > 0:
                logger.info(f"Rate limiting: sleeping for {sleep_time:.2f} seconds")
                time.sleep(sleep_time)
        
        self.request_times.append(now)
    
    def get_model_name(self, model_key: str) -> str:
        """Get model name from key"""
        return self.models.get(model_key, self.models.get('primary', 'gpt-3.5-turbo'))
    
    def route_model(self, index: int) -> str:
        """Route model based on index for load balancing"""
        if index % 2 == 0:
            return self.get_model_name('primary')
        else:
            return self.get_model_name('secondary')
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((aiohttp.ClientError, asyncio.TimeoutError))
    )
    async def _make_request(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Make HTTP request with retry logic"""
        if not self.session:
            raise RuntimeError("LLMClient must be used as async context manager")
        
        self._rate_limit()
        
        url = f"{self.base_url}/chat/completions"
        
        try:
            async with self.session.post(url, json=payload) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    logger.error(f"LLM API error {response.status}: {error_text}")
                    raise aiohttp.ClientResponseError(
                        request_info=response.request_info,
                        history=response.history,
                        status=response.status,
                        message=error_text
                    )
        except asyncio.TimeoutError:
            logger.error(f"Request timeout for model {payload.get('model')}")
            raise
        except Exception as e:
            logger.error(f"Request failed: {e}")
            raise
    
    async def chat_completion(self, messages: List[Dict[str, str]], 
                            model: Optional[str] = None,
                            system_prompt: Optional[str] = None) -> LLMResponse:
        """Send chat completion request"""
        
        if model is None:
            model = self.get_model_name('primary')
        
        # Prepare messages
        formatted_messages = []
        
        if system_prompt:
            formatted_messages.append({
                "role": "system",
                "content": system_prompt
            })
        
        formatted_messages.extend(messages)
        
        # Prepare payload
        payload = {
            "model": model,
            "messages": formatted_messages,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "stream": False
        }
        
        start_time = time.time()
        
        try:
            response_data = await self._make_request(payload)
            response_time = time.time() - start_time
            
            # Parse response
            choice = response_data['choices'][0]
            content = choice['message']['content']
            finish_reason = choice['finish_reason']
            usage = response_data.get('usage', {})
            
            logger.info(f"LLM response received in {response_time:.2f}s from {model}")
            
            return LLMResponse(
                content=content,
                model=model,
                usage=usage,
                finish_reason=finish_reason,
                response_time=response_time
            )
            
        except Exception as e:
            logger.error(f"Chat completion failed for model {model}: {e}")
            raise

    async def extract_entities_and_relations(self, text: str,
                                           model: Optional[str] = None) -> Dict[str, Any]:
        """Extract entities and relations from text"""

        system_prompt = """你是一个专业的知识图谱构建助手。请从给定的文本中提取实体和关系。

要求：
1. 识别文本中的重要实体（人物、地点、组织、概念、事件等）
2. 识别实体之间的关系
3. 为每个实体提供类型和简短描述
4. 确保提取的信息准确且有意义

请以JSON格式返回结果：
{
  "entities": [
    {
      "name": "实体名称",
      "type": "实体类型",
      "description": "实体描述",
      "confidence": 0.95
    }
  ],
  "relations": [
    {
      "source": "源实体",
      "target": "目标实体",
      "relation": "关系类型",
      "description": "关系描述",
      "confidence": 0.90
    }
  ]
}"""

        messages = [{
            "role": "user",
            "content": f"请从以下文本中提取实体和关系：\n\n{text}"
        }]

        try:
            response = await self.chat_completion(
                messages=messages,
                model=model,
                system_prompt=system_prompt
            )

            # Parse JSON response
            try:
                result = json.loads(response.content)

                # Validate structure
                if 'entities' not in result or 'relations' not in result:
                    raise ValueError("Invalid response structure")

                return result

            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON response: {e}")
                logger.debug(f"Raw response: {response.content}")

                # Try to extract JSON from response
                import re
                json_match = re.search(r'\{.*\}', response.content, re.DOTALL)
                if json_match:
                    try:
                        result = json.loads(json_match.group())
                        return result
                    except:
                        pass

                # Return empty result if parsing fails
                return {"entities": [], "relations": []}

        except Exception as e:
            logger.error(f"Entity extraction failed: {e}")
            return {"entities": [], "relations": []}

    async def enhance_entity_description(self, entity_name: str, entity_type: str,
                                       context: str, model: Optional[str] = None) -> str:
        """Enhance entity description with context"""

        system_prompt = """你是一个知识图谱专家。请为给定的实体生成详细、准确的描述。

要求：
1. 基于提供的上下文信息
2. 描述应该简洁但信息丰富
3. 突出实体的关键特征和重要性
4. 避免重复和冗余信息"""

        messages = [{
            "role": "user",
            "content": f"实体名称：{entity_name}\n实体类型：{entity_type}\n上下文：{context}\n\n请为这个实体生成一个详细的描述："
        }]

        try:
            response = await self.chat_completion(
                messages=messages,
                model=model,
                system_prompt=system_prompt
            )

            return response.content.strip()

        except Exception as e:
            logger.error(f"Entity description enhancement failed: {e}")
            return f"{entity_type}: {entity_name}"

    async def generate_entity_tags(self, entity_name: str, entity_type: str,
                                 description: str, model: Optional[str] = None) -> List[str]:
        """Generate tags for entity"""

        system_prompt = """你是一个标签生成专家。请为给定的实体生成相关的标签。

要求：
1. 标签应该简洁且具有描述性
2. 每个标签1-3个词
3. 返回3-8个最相关的标签
4. 标签应该有助于分类和搜索

请以JSON数组格式返回标签：["标签1", "标签2", "标签3"]"""

        messages = [{
            "role": "user",
            "content": f"实体名称：{entity_name}\n实体类型：{entity_type}\n描述：{description}\n\n请生成相关标签："
        }]

        try:
            response = await self.chat_completion(
                messages=messages,
                model=model,
                system_prompt=system_prompt
            )

            # Parse JSON response
            try:
                tags = json.loads(response.content)
                if isinstance(tags, list):
                    return [tag.strip() for tag in tags if isinstance(tag, str)]
                else:
                    return []
            except json.JSONDecodeError:
                # Extract tags from text response
                import re
                tags = re.findall(r'"([^"]+)"', response.content)
                return tags[:8] if tags else []

        except Exception as e:
            logger.error(f"Tag generation failed: {e}")
            return [entity_type.lower()]


async def test_llm_client():
    """Test LLM client functionality"""
    from config.settings import Settings

    settings = Settings()

    async with LLMClient(
        base_url=settings.llm.base_url,
        api_key=settings.llm.api_key,
        models=settings.llm.models
    ) as client:

        # Test basic chat
        messages = [{"role": "user", "content": "Hello, how are you?"}]
        response = await client.chat_completion(messages)
        print(f"Chat response: {response.content}")

        # Test entity extraction
        text = "苹果公司的CEO蒂姆·库克在加利福尼亚州库比蒂诺的苹果园区发布了新的iPhone。"
        entities = await client.extract_entities_and_relations(text)
        print(f"Entities: {entities}")


if __name__ == "__main__":
    asyncio.run(test_llm_client())
