{% extends "base.html" %}

{% block title %}Graph Visualization - Knowledge Graph Builder{% endblock %}

{% block page_title %}Graph Visualization{% endblock %}

{% block header_actions %}
<div class="btn-group" role="group">
    <a href="/" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i>
        Back to Dashboard
    </a>
    <a href="/search" class="btn btn-outline-primary">
        <i class="fas fa-search me-1"></i>
        Search
    </a>
</div>
{% endblock %}

{% block extra_css %}
<!-- D3.js for graph visualization -->
<style>
.graph-container {
    background: white;
    border-radius: 0.5rem;
    border: 1px solid #dee2e6;
    position: relative;
    overflow: hidden;
}

.graph-controls {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1000;
}

.graph-legend {
    position: absolute;
    bottom: 10px;
    left: 10px;
    background: rgba(255, 255, 255, 0.9);
    padding: 10px;
    border-radius: 0.25rem;
    border: 1px solid #dee2e6;
    z-index: 1000;
}

.node {
    cursor: pointer;
    stroke: #fff;
    stroke-width: 2px;
}

.node:hover {
    stroke-width: 3px;
}

.link {
    stroke: #999;
    stroke-opacity: 0.6;
    stroke-width: 1px;
}

.link:hover {
    stroke-opacity: 1;
    stroke-width: 2px;
}

.node-label {
    font-family: Arial, sans-serif;
    font-size: 12px;
    pointer-events: none;
    text-anchor: middle;
    dominant-baseline: central;
}

.tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px;
    border-radius: 4px;
    font-size: 12px;
    pointer-events: none;
    z-index: 1001;
}

.graph-stats {
    background: rgba(255, 255, 255, 0.9);
    padding: 10px;
    border-radius: 0.25rem;
    border: 1px solid #dee2e6;
}
</style>
{% endblock %}

{% block content %}
<!-- Graph Controls -->
<div class="row mb-3">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body py-2">
                <div class="row align-items-center">
                    <div class="col-md-3">
                        <label class="form-label mb-0">Layout</label>
                        <select class="form-select form-select-sm" id="layout-select">
                            <option value="force">Force-directed</option>
                            <option value="circular">Circular</option>
                            <option value="hierarchical">Hierarchical</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label mb-0">Entity Type</label>
                        <select class="form-select form-select-sm" id="entity-filter">
                            <option value="">All Types</option>
                            <option value="person">Person</option>
                            <option value="organization">Organization</option>
                            <option value="location">Location</option>
                            <option value="concept">Concept</option>
                            <option value="event">Event</option>
                            <option value="product">Product</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label mb-0">Max Nodes</label>
                        <select class="form-select form-select-sm" id="node-limit">
                            <option value="50">50 nodes</option>
                            <option value="100" selected>100 nodes</option>
                            <option value="200">200 nodes</option>
                            <option value="500">500 nodes</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <div class="btn-group w-100" role="group">
                            <button class="btn btn-sm btn-outline-primary" id="zoom-in-btn">
                                <i class="fas fa-search-plus"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-primary" id="zoom-out-btn">
                                <i class="fas fa-search-minus"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-secondary" id="reset-view-btn">
                                <i class="fas fa-expand-arrows-alt"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-body py-2">
                <div class="graph-stats">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="fw-bold" id="nodes-count">0</div>
                            <small class="text-muted">Nodes</small>
                        </div>
                        <div class="col-4">
                            <div class="fw-bold" id="edges-count">0</div>
                            <small class="text-muted">Edges</small>
                        </div>
                        <div class="col-4">
                            <div class="fw-bold" id="selected-count">0</div>
                            <small class="text-muted">Selected</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Graph Visualization -->
<div class="row">
    <div class="col-lg-9">
        <div class="card">
            <div class="card-body p-0">
                <div class="graph-container" id="graph-container" style="height: 600px;">
                    <svg id="graph-svg" width="100%" height="100%"></svg>
                    
                    <!-- Graph Controls -->
                    <div class="graph-controls">
                        <div class="btn-group-vertical" role="group">
                            <button class="btn btn-sm btn-light" id="fullscreen-btn" title="Fullscreen">
                                <i class="fas fa-expand"></i>
                            </button>
                            <button class="btn btn-sm btn-light" id="export-btn" title="Export">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Graph Legend -->
                    <div class="graph-legend">
                        <div class="fw-bold mb-2">Entity Types</div>
                        <div id="legend-content">
                            <!-- Legend will be populated dynamically -->
                        </div>
                    </div>
                    
                    <!-- Loading indicator -->
                    <div id="graph-loading" class="position-absolute top-50 start-50 translate-middle">
                        <div class="text-center">
                            <div class="loading-spinner mb-2"></div>
                            <div>Loading graph...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Node Details Panel -->
    <div class="col-lg-3">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Node Details
                </h6>
            </div>
            <div class="card-body" id="node-details">
                <div class="text-center text-muted">
                    <i class="fas fa-mouse-pointer fa-2x mb-3"></i>
                    <p>Click on a node to view details</p>
                </div>
            </div>
        </div>
        
        <!-- Neighbors Panel -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-sitemap me-2"></i>
                    Connected Nodes
                </h6>
            </div>
            <div class="card-body" id="neighbors-panel">
                <div class="text-center text-muted">
                    <p>Select a node to see connections</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- D3.js -->
<script src="https://d3js.org/d3.v7.min.js"></script>

<script>
let graphData = { nodes: [], links: [] };
let simulation;
let svg, g;
let selectedNode = null;
let zoom;

// Entity type colors
const entityColors = {
    'person': '#3498db',
    'organization': '#2ecc71',
    'location': '#e74c3c',
    'concept': '#f39c12',
    'event': '#9b59b6',
    'product': '#34495e',
    'default': '#95a5a6'
};

document.addEventListener('DOMContentLoaded', function() {
    initializeGraph();
    loadGraphData();
    setupEventListeners();
});

function initializeGraph() {
    const container = document.getElementById('graph-container');
    const width = container.clientWidth;
    const height = container.clientHeight;
    
    svg = d3.select('#graph-svg')
        .attr('width', width)
        .attr('height', height);
    
    // Setup zoom
    zoom = d3.zoom()
        .scaleExtent([0.1, 4])
        .on('zoom', (event) => {
            g.attr('transform', event.transform);
        });
    
    svg.call(zoom);
    
    // Create main group
    g = svg.append('g');
    
    // Setup simulation
    simulation = d3.forceSimulation()
        .force('link', d3.forceLink().id(d => d.id).distance(100))
        .force('charge', d3.forceManyBody().strength(-300))
        .force('center', d3.forceCenter(width / 2, height / 2))
        .force('collision', d3.forceCollide().radius(30));
}

function setupEventListeners() {
    // Layout change
    document.getElementById('layout-select').addEventListener('change', updateLayout);
    
    // Filters
    document.getElementById('entity-filter').addEventListener('change', applyFilters);
    document.getElementById('node-limit').addEventListener('change', applyFilters);
    
    // Zoom controls
    document.getElementById('zoom-in-btn').addEventListener('click', () => {
        svg.transition().call(zoom.scaleBy, 1.5);
    });
    
    document.getElementById('zoom-out-btn').addEventListener('click', () => {
        svg.transition().call(zoom.scaleBy, 1 / 1.5);
    });
    
    document.getElementById('reset-view-btn').addEventListener('click', resetView);
    
    // Fullscreen
    document.getElementById('fullscreen-btn').addEventListener('click', toggleFullscreen);
    
    // Export
    document.getElementById('export-btn').addEventListener('click', exportGraph);
    
    // Window resize
    window.addEventListener('resize', handleResize);
}

async function loadGraphData() {
    try {
        showLoading(true);
        
        // TODO: Replace with actual API endpoint
        // For now, generate sample data
        graphData = generateSampleData();
        
        updateGraph();
        updateLegend();
        updateStats();
        
    } catch (error) {
        showAlert(`Failed to load graph data: ${error.message}`, 'danger');
    } finally {
        showLoading(false);
    }
}

function generateSampleData() {
    // Generate sample data for demonstration
    const nodes = [
        { id: '1', name: 'John Doe', type: 'person' },
        { id: '2', name: 'Acme Corp', type: 'organization' },
        { id: '3', name: 'New York', type: 'location' },
        { id: '4', name: 'Machine Learning', type: 'concept' },
        { id: '5', name: 'Conference 2023', type: 'event' }
    ];
    
    const links = [
        { source: '1', target: '2', type: 'works_at' },
        { source: '1', target: '3', type: 'lives_in' },
        { source: '2', target: '3', type: 'located_in' },
        { source: '1', target: '4', type: 'interested_in' },
        { source: '1', target: '5', type: 'attended' }
    ];
    
    return { nodes, links };
}

function updateGraph() {
    const { nodes, links } = graphData;
    
    // Clear existing elements
    g.selectAll('*').remove();
    
    // Create links
    const link = g.append('g')
        .selectAll('line')
        .data(links)
        .enter().append('line')
        .attr('class', 'link')
        .attr('stroke-width', d => Math.sqrt(d.value || 1));
    
    // Create nodes
    const node = g.append('g')
        .selectAll('circle')
        .data(nodes)
        .enter().append('circle')
        .attr('class', 'node')
        .attr('r', 20)
        .attr('fill', d => entityColors[d.type] || entityColors.default)
        .call(d3.drag()
            .on('start', dragstarted)
            .on('drag', dragged)
            .on('end', dragended))
        .on('click', (event, d) => selectNode(d))
        .on('mouseover', (event, d) => showTooltip(event, d))
        .on('mouseout', hideTooltip);
    
    // Create labels
    const label = g.append('g')
        .selectAll('text')
        .data(nodes)
        .enter().append('text')
        .attr('class', 'node-label')
        .text(d => d.name)
        .attr('dy', 35);
    
    // Update simulation
    simulation.nodes(nodes);
    simulation.force('link').links(links);
    
    simulation.on('tick', () => {
        link
            .attr('x1', d => d.source.x)
            .attr('y1', d => d.source.y)
            .attr('x2', d => d.target.x)
            .attr('y2', d => d.target.y);
        
        node
            .attr('cx', d => d.x)
            .attr('cy', d => d.y);
        
        label
            .attr('x', d => d.x)
            .attr('y', d => d.y);
    });
    
    simulation.restart();
}

function selectNode(node) {
    selectedNode = node;
    
    // Highlight selected node
    g.selectAll('.node')
        .attr('stroke', d => d === node ? '#ff6b6b' : '#fff')
        .attr('stroke-width', d => d === node ? 4 : 2);
    
    // Show node details
    showNodeDetails(node);
    
    // Show neighbors
    showNeighbors(node);
    
    // Update selected count
    document.getElementById('selected-count').textContent = '1';
}

function showNodeDetails(node) {
    const panel = document.getElementById('node-details');
    
    panel.innerHTML = `
        <div class="mb-3">
            <h6 class="fw-bold">${node.name}</h6>
            <span class="badge" style="background-color: ${entityColors[node.type] || entityColors.default}">
                ${node.type}
            </span>
        </div>
        
        <div class="mb-3">
            <small class="text-muted">Description</small>
            <p class="mb-0">${node.description || 'No description available'}</p>
        </div>
        
        ${node.tags && node.tags.length > 0 ? `
            <div class="mb-3">
                <small class="text-muted">Tags</small>
                <div>
                    ${node.tags.map(tag => `<span class="badge bg-secondary me-1">${tag}</span>`).join('')}
                </div>
            </div>
        ` : ''}
        
        <div class="d-grid">
            <button class="btn btn-sm btn-outline-primary" onclick="viewEntityDetails('${node.id}')">
                View Full Details
            </button>
        </div>
    `;
}

function showNeighbors(node) {
    const panel = document.getElementById('neighbors-panel');
    const neighbors = getNeighbors(node);
    
    if (neighbors.length === 0) {
        panel.innerHTML = '<p class="text-muted">No connections found</p>';
        return;
    }
    
    panel.innerHTML = `
        <div class="list-group list-group-flush">
            ${neighbors.map(neighbor => `
                <div class="list-group-item px-0 py-2">
                    <div class="d-flex align-items-center">
                        <div class="rounded-circle me-2" 
                             style="width: 12px; height: 12px; background-color: ${entityColors[neighbor.node.type] || entityColors.default}">
                        </div>
                        <div class="flex-grow-1">
                            <div class="fw-bold">${neighbor.node.name}</div>
                            <small class="text-muted">${neighbor.relation}</small>
                        </div>
                    </div>
                </div>
            `).join('')}
        </div>
    `;
}

function getNeighbors(node) {
    const neighbors = [];
    
    graphData.links.forEach(link => {
        if (link.source.id === node.id) {
            neighbors.push({
                node: link.target,
                relation: link.type || 'connected to'
            });
        } else if (link.target.id === node.id) {
            neighbors.push({
                node: link.source,
                relation: link.type || 'connected to'
            });
        }
    });
    
    return neighbors;
}

function updateLegend() {
    const legendContent = document.getElementById('legend-content');
    const types = [...new Set(graphData.nodes.map(n => n.type))];
    
    legendContent.innerHTML = types.map(type => `
        <div class="d-flex align-items-center mb-1">
            <div class="rounded-circle me-2" 
                 style="width: 12px; height: 12px; background-color: ${entityColors[type] || entityColors.default}">
            </div>
            <small>${type}</small>
        </div>
    `).join('');
}

function updateStats() {
    document.getElementById('nodes-count').textContent = graphData.nodes.length;
    document.getElementById('edges-count').textContent = graphData.links.length;
    document.getElementById('selected-count').textContent = selectedNode ? '1' : '0';
}

function showLoading(show) {
    const loading = document.getElementById('graph-loading');
    loading.style.display = show ? 'block' : 'none';
}

function showTooltip(event, node) {
    const tooltip = d3.select('body').append('div')
        .attr('class', 'tooltip')
        .style('left', (event.pageX + 10) + 'px')
        .style('top', (event.pageY - 10) + 'px')
        .html(`<strong>${node.name}</strong><br>Type: ${node.type}`);
}

function hideTooltip() {
    d3.selectAll('.tooltip').remove();
}

// Drag functions
function dragstarted(event, d) {
    if (!event.active) simulation.alphaTarget(0.3).restart();
    d.fx = d.x;
    d.fy = d.y;
}

function dragged(event, d) {
    d.fx = event.x;
    d.fy = event.y;
}

function dragended(event, d) {
    if (!event.active) simulation.alphaTarget(0);
    d.fx = null;
    d.fy = null;
}

// Utility functions
function resetView() {
    svg.transition().call(zoom.transform, d3.zoomIdentity);
}

function handleResize() {
    const container = document.getElementById('graph-container');
    const width = container.clientWidth;
    const height = container.clientHeight;
    
    svg.attr('width', width).attr('height', height);
    simulation.force('center', d3.forceCenter(width / 2, height / 2));
    simulation.restart();
}

function updateLayout() {
    // TODO: Implement different layout algorithms
    simulation.restart();
}

function applyFilters() {
    // TODO: Implement filtering
    updateGraph();
}

function toggleFullscreen() {
    // TODO: Implement fullscreen mode
}

function exportGraph() {
    // TODO: Implement graph export
    showAlert('Export functionality coming soon!', 'info');
}

function viewEntityDetails(entityId) {
    window.location.href = `/search?entity=${entityId}`;
}
</script>
{% endblock %}
