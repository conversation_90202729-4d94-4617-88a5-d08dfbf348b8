#!/usr/bin/env python3
"""测试上传页面功能的脚本"""

import requests
import sys

def test_upload_page():
    """测试上传页面是否正常工作"""
    print("🔍 测试上传页面功能...")
    
    try:
        # 测试上传页面
        response = requests.get("http://localhost:8001/upload", timeout=10)
        
        print(f"状态码: {response.status_code}")
        print(f"内容长度: {len(response.text)} 字符")
        
        if response.status_code == 200:
            # 检查页面内容
            content = response.text
            
            # 检查关键元素
            checks = [
                ("HTML 文档", "<!DOCTYPE html>" in content),
                ("页面标题", "知识图谱构建器" in content),
                ("上传表单", "upload-form" in content),
                ("文件输入", 'type="file"' in content),
                ("处理按钮", "开始处理" in content),
                ("JavaScript", "<script" in content)
            ]
            
            print("\n页面内容检查:")
            all_good = True
            for check_name, result in checks:
                status = "✅" if result else "❌"
                print(f"  {status} {check_name}: {'通过' if result else '失败'}")
                if not result:
                    all_good = False
            
            if all_good:
                print("\n🎉 上传页面功能正常！")
                return True
            else:
                print("\n⚠️ 上传页面存在问题")
                
                # 显示页面内容的前500个字符用于调试
                print("\n页面内容预览:")
                print("-" * 50)
                print(content[:500])
                print("-" * 50)
                return False
        else:
            print(f"❌ 上传页面返回错误状态码: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 连接失败: {e}")
        return False

def main():
    """主函数"""
    success = test_upload_page()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
