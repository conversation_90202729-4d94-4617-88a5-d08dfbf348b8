"""
File utilities for document processing
Handles format detection, parsing, and content splitting
"""

import os
import re
import mimetypes
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import asyncio
import aiofiles

from loguru import logger
import magic
from docx import Document
import PyPDF2
import pdfplumber


class FileProcessor:
    """File processing utilities"""
    
    SUPPORTED_FORMATS = {
        '.txt': 'text/plain',
        '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        '.pdf': 'application/pdf',
        '.md': 'text/markdown',
        '.rtf': 'application/rtf'
    }
    
    def __init__(self, max_file_size_mb: int = 100, encoding: str = 'utf-8'):
        self.max_file_size = max_file_size_mb * 1024 * 1024  # Convert to bytes
        self.encoding = encoding
    
    def validate_file(self, file_path: Path) -> bool:
        """Validate file format and size"""
        try:
            # Check if file exists
            if not file_path.exists():
                logger.warning(f"File does not exist: {file_path}")
                return False
            
            # Check file size
            file_size = file_path.stat().st_size
            if file_size > self.max_file_size:
                logger.warning(f"File too large: {file_path} ({file_size} bytes)")
                return False
            
            # Check file format
            file_ext = file_path.suffix.lower()
            if file_ext not in self.SUPPORTED_FORMATS:
                logger.warning(f"Unsupported format: {file_path} ({file_ext})")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating file {file_path}: {e}")
            return False
    
    def detect_format(self, file_path: Path) -> Optional[str]:
        """Detect file format using multiple methods"""
        try:
            # First try file extension
            file_ext = file_path.suffix.lower()
            if file_ext in self.SUPPORTED_FORMATS:
                return file_ext
            
            # Try MIME type detection
            mime_type, _ = mimetypes.guess_type(str(file_path))
            for ext, supported_mime in self.SUPPORTED_FORMATS.items():
                if mime_type == supported_mime:
                    return ext
            
            # Try magic number detection
            try:
                file_type = magic.from_file(str(file_path), mime=True)
                for ext, supported_mime in self.SUPPORTED_FORMATS.items():
                    if file_type == supported_mime:
                        return ext
            except:
                pass
            
            return None
            
        except Exception as e:
            logger.error(f"Error detecting format for {file_path}: {e}")
            return None
    
    async def parse_text_file(self, file_path: Path) -> Dict[str, Any]:
        """Parse plain text file"""
        try:
            async with aiofiles.open(file_path, 'r', encoding=self.encoding) as f:
                content = await f.read()
            
            return {
                'content': content,
                'metadata': {
                    'file_type': 'txt',
                    'encoding': self.encoding,
                    'size': len(content),
                    'lines': content.count('\n') + 1
                }
            }
            
        except UnicodeDecodeError:
            # Try different encodings
            for encoding in ['utf-8', 'gbk', 'gb2312', 'latin-1']:
                try:
                    async with aiofiles.open(file_path, 'r', encoding=encoding) as f:
                        content = await f.read()
                    
                    logger.info(f"Successfully decoded {file_path} with {encoding}")
                    return {
                        'content': content,
                        'metadata': {
                            'file_type': 'txt',
                            'encoding': encoding,
                            'size': len(content),
                            'lines': content.count('\n') + 1
                        }
                    }
                except:
                    continue
            
            raise ValueError(f"Could not decode text file: {file_path}")
    
    def parse_docx_file(self, file_path: Path) -> Dict[str, Any]:
        """Parse DOCX file"""
        try:
            doc = Document(file_path)
            
            # Extract text from paragraphs
            paragraphs = []
            for para in doc.paragraphs:
                if para.text.strip():
                    paragraphs.append(para.text.strip())
            
            content = '\n\n'.join(paragraphs)
            
            # Extract metadata
            props = doc.core_properties
            metadata = {
                'file_type': 'docx',
                'title': props.title or '',
                'author': props.author or '',
                'created': props.created.isoformat() if props.created else '',
                'modified': props.modified.isoformat() if props.modified else '',
                'paragraphs': len(paragraphs),
                'size': len(content)
            }
            
            return {
                'content': content,
                'metadata': metadata
            }
            
        except Exception as e:
            logger.error(f"Error parsing DOCX file {file_path}: {e}")
            raise
    
    def parse_pdf_file(self, file_path: Path) -> Dict[str, Any]:
        """Parse PDF file using multiple methods"""
        content = ""
        metadata = {'file_type': 'pdf'}
        
        try:
            # Try pdfplumber first (better for complex layouts)
            with pdfplumber.open(file_path) as pdf:
                pages = []
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        pages.append(page_text.strip())
                
                content = '\n\n'.join(pages)
                metadata.update({
                    'pages': len(pdf.pages),
                    'method': 'pdfplumber'
                })
            
        except Exception as e:
            logger.warning(f"pdfplumber failed for {file_path}: {e}, trying PyPDF2")
            
            try:
                # Fallback to PyPDF2
                with open(file_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    pages = []
                    
                    for page in pdf_reader.pages:
                        page_text = page.extract_text()
                        if page_text:
                            pages.append(page_text.strip())
                    
                    content = '\n\n'.join(pages)
                    metadata.update({
                        'pages': len(pdf_reader.pages),
                        'method': 'PyPDF2'
                    })
                    
            except Exception as e2:
                logger.error(f"Both PDF parsers failed for {file_path}: {e2}")
                raise
        
        metadata['size'] = len(content)
        
        return {
            'content': content,
            'metadata': metadata
        }
    
    async def parse_file(self, file_path: Path) -> Dict[str, Any]:
        """Parse file based on its format"""
        if not self.validate_file(file_path):
            raise ValueError(f"Invalid file: {file_path}")
        
        file_format = self.detect_format(file_path)
        if not file_format:
            raise ValueError(f"Unsupported file format: {file_path}")
        
        logger.info(f"Parsing {file_format} file: {file_path}")
        
        if file_format in ['.txt', '.md']:
            return await self.parse_text_file(file_path)
        elif file_format == '.docx':
            # Run in thread pool for CPU-bound operation
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, self.parse_docx_file, file_path)
        elif file_format == '.pdf':
            # Run in thread pool for CPU-bound operation
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, self.parse_pdf_file, file_path)
        else:
            raise ValueError(f"Parser not implemented for format: {file_format}")


class ContentSplitter:
    """Split content into semantic chunks"""
    
    def __init__(self, chunk_size: int = 2000, chunk_overlap: int = 200):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
    
    def split_by_sentences(self, text: str) -> List[str]:
        """Split text by sentences"""
        # Simple sentence splitting (can be improved with NLTK)
        sentence_endings = r'[.!?。！？]+'
        sentences = re.split(sentence_endings, text)
        return [s.strip() for s in sentences if s.strip()]
    
    def split_by_paragraphs(self, text: str) -> List[str]:
        """Split text by paragraphs"""
        paragraphs = text.split('\n\n')
        return [p.strip() for p in paragraphs if p.strip()]
    
    def split_by_semantic_boundaries(self, text: str) -> List[Dict[str, Any]]:
        """Split text by semantic boundaries with overlap"""
        if len(text) <= self.chunk_size:
            return [{
                'text': text,
                'start_pos': 0,
                'end_pos': len(text),
                'chunk_id': 0
            }]
        
        chunks = []
        chunk_id = 0
        start = 0
        
        while start < len(text):
            # Calculate end position
            end = min(start + self.chunk_size, len(text))
            
            # Try to find a good breaking point
            if end < len(text):
                # Look for paragraph break
                para_break = text.rfind('\n\n', start, end)
                if para_break > start:
                    end = para_break + 2
                else:
                    # Look for sentence break
                    sent_break = max(
                        text.rfind('.', start, end),
                        text.rfind('!', start, end),
                        text.rfind('?', start, end),
                        text.rfind('。', start, end),
                        text.rfind('！', start, end),
                        text.rfind('？', start, end)
                    )
                    if sent_break > start:
                        end = sent_break + 1
                    else:
                        # Look for space
                        space_break = text.rfind(' ', start, end)
                        if space_break > start:
                            end = space_break
            
            chunk_text = text[start:end].strip()
            if chunk_text:
                chunks.append({
                    'text': chunk_text,
                    'start_pos': start,
                    'end_pos': end,
                    'chunk_id': chunk_id
                })
                chunk_id += 1
            
            # Move start position with overlap
            start = max(start + 1, end - self.chunk_overlap)
        
        return chunks


def validate_input_files(input_path: str, max_files: int = 100) -> List[Path]:
    """Validate and collect input files"""
    input_path = Path(input_path)
    processor = FileProcessor()
    
    if input_path.is_file():
        if processor.validate_file(input_path):
            return [input_path]
        else:
            return []
    
    elif input_path.is_dir():
        valid_files = []
        
        # Recursively find all supported files
        for file_path in input_path.rglob('*'):
            if file_path.is_file() and processor.validate_file(file_path):
                valid_files.append(file_path)
                
                if len(valid_files) >= max_files:
                    logger.warning(f"Reached maximum file limit ({max_files})")
                    break
        
        return valid_files
    
    else:
        logger.error(f"Input path does not exist: {input_path}")
        return []
