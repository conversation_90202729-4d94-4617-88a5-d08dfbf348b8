"""File utilities for document parsing and content processing."""

import os
import re
import magic
from pathlib import Path
from typing import List, Dict, Tu<PERSON>, Optional, Union
from dataclasses import dataclass

import docx
import PyPDF2
import pdfplumber
from loguru import logger


@dataclass
class ContentSegment:
    """Represents a segment of document content."""
    text: str
    start_pos: int
    end_pos: int
    metadata: Optional[Dict] = None


class FileUtils:
    """Utilities for file format detection, parsing, and content segmentation."""
    
    SUPPORTED_FORMATS = {
        'txt': 'text/plain',
        'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'pdf': 'application/pdf',
        'chat': 'text/plain'  # Chat logs are typically plain text
    }
    
    @staticmethod
    def detect_file_format(file_path: Union[str, Path]) -> str:
        """
        Detect file format using python-magic.
        
        Args:
            file_path: Path to the file
            
        Returns:
            File format string (txt, docx, pdf, chat)
            
        Raises:
            ValueError: If file format is not supported
        """
        file_path = Path(file_path)
        
        # First try by extension
        extension = file_path.suffix.lower().lstrip('.')
        if extension in FileUtils.SUPPORTED_FORMATS:
            return extension
        
        # Then try by MIME type
        try:
            mime_type = magic.from_file(str(file_path), mime=True)
            for fmt, expected_mime in FileUtils.SUPPORTED_FORMATS.items():
                if mime_type == expected_mime:
                    return fmt
        except Exception as e:
            logger.warning(f"Could not detect MIME type for {file_path}: {e}")
        
        # Fallback to extension-based detection
        if extension:
            logger.warning(f"Unknown file format for {file_path}, treating as {extension}")
            return extension
        
        raise ValueError(f"Unsupported file format: {file_path}")
    
    @staticmethod
    def parse_txt_file(file_path: Union[str, Path]) -> str:
        """
        Parse plain text file.
        
        Args:
            file_path: Path to the text file
            
        Returns:
            File content as string
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except UnicodeDecodeError:
            # Try with different encodings
            for encoding in ['gbk', 'gb2312', 'latin1']:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        return f.read()
                except UnicodeDecodeError:
                    continue
            raise ValueError(f"Could not decode file {file_path} with any supported encoding")
    
    @staticmethod
    def parse_docx_file(file_path: Union[str, Path]) -> str:
        """
        Parse DOCX file.
        
        Args:
            file_path: Path to the DOCX file
            
        Returns:
            File content as string
        """
        try:
            doc = docx.Document(file_path)
            paragraphs = []
            
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    paragraphs.append(paragraph.text)
            
            return '\n'.join(paragraphs)
        except Exception as e:
            raise ValueError(f"Could not parse DOCX file {file_path}: {e}")
    
    @staticmethod
    def parse_pdf_file(file_path: Union[str, Path]) -> str:
        """
        Parse PDF file using pdfplumber for better text extraction.
        
        Args:
            file_path: Path to the PDF file
            
        Returns:
            File content as string
        """
        try:
            text_content = []
            
            with pdfplumber.open(file_path) as pdf:
                for page in pdf.pages:
                    text = page.extract_text()
                    if text:
                        text_content.append(text)
            
            if not text_content:
                # Fallback to PyPDF2
                logger.warning(f"pdfplumber failed for {file_path}, trying PyPDF2")
                return FileUtils._parse_pdf_with_pypdf2(file_path)
            
            return '\n'.join(text_content)
            
        except Exception as e:
            logger.warning(f"pdfplumber failed for {file_path}: {e}, trying PyPDF2")
            return FileUtils._parse_pdf_with_pypdf2(file_path)
    
    @staticmethod
    def _parse_pdf_with_pypdf2(file_path: Union[str, Path]) -> str:
        """Fallback PDF parser using PyPDF2."""
        try:
            text_content = []
            
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                for page in pdf_reader.pages:
                    text = page.extract_text()
                    if text:
                        text_content.append(text)
            
            return '\n'.join(text_content)
            
        except Exception as e:
            raise ValueError(f"Could not parse PDF file {file_path}: {e}")
    
    @staticmethod
    def parse_chat_file(file_path: Union[str, Path]) -> str:
        """
        Parse chat log file (WeChat, etc.).
        
        Args:
            file_path: Path to the chat file
            
        Returns:
            Cleaned chat content as string
        """
        content = FileUtils.parse_txt_file(file_path)
        
        # Basic chat log cleaning
        lines = content.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Remove timestamp patterns (basic patterns)
            line = re.sub(r'\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}', '', line)
            line = re.sub(r'\d{2}:\d{2}:\d{2}', '', line)
            
            # Remove common chat prefixes
            line = re.sub(r'^[^\s]+:', '', line)  # Remove "username:"
            
            line = line.strip()
            if line:
                cleaned_lines.append(line)
        
        return '\n'.join(cleaned_lines)
    
    @staticmethod
    def parse_file(file_path: Union[str, Path]) -> Tuple[str, str]:
        """
        Parse file based on its format.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Tuple of (file_type, content)
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        file_type = FileUtils.detect_file_format(file_path)
        
        parsers = {
            'txt': FileUtils.parse_txt_file,
            'docx': FileUtils.parse_docx_file,
            'pdf': FileUtils.parse_pdf_file,
            'chat': FileUtils.parse_chat_file
        }
        
        parser = parsers.get(file_type)
        if not parser:
            raise ValueError(f"No parser available for file type: {file_type}")
        
        content = parser(file_path)
        logger.info(f"Parsed {file_type} file: {file_path} ({len(content)} characters)")
        
        return file_type, content
    
    @staticmethod
    def estimate_optimal_chunk_size(content: str, target_tokens: int = 2000) -> int:
        """
        Estimate optimal chunk size based on content characteristics.
        
        Args:
            content: Text content to analyze
            target_tokens: Target number of tokens per chunk
            
        Returns:
            Estimated chunk size in characters
        """
        # Rough estimation: 1 token ≈ 4 characters for English, 1.5 for Chinese
        avg_chars_per_token = 3.0  # Conservative estimate
        
        # Analyze content for language characteristics
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', content))
        total_chars = len(content)
        
        if chinese_chars > total_chars * 0.3:  # Mostly Chinese
            avg_chars_per_token = 1.5
        elif chinese_chars > total_chars * 0.1:  # Mixed
            avg_chars_per_token = 2.5
        
        return int(target_tokens * avg_chars_per_token)
    
    @staticmethod
    def split_by_semantic_boundaries(content: str, chunk_size: int) -> List[ContentSegment]:
        """
        Split content by semantic boundaries (paragraphs, sentences).
        
        Args:
            content: Text content to split
            chunk_size: Target chunk size in characters
            
        Returns:
            List of ContentSegment objects
        """
        if len(content) <= chunk_size:
            return [ContentSegment(content, 0, len(content))]
        
        segments = []
        current_pos = 0
        
        # Split by paragraphs first
        paragraphs = content.split('\n\n')
        current_chunk = ""
        chunk_start = 0
        
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue
            
            # If adding this paragraph would exceed chunk size
            if len(current_chunk) + len(paragraph) > chunk_size and current_chunk:
                # Save current chunk
                segments.append(ContentSegment(
                    current_chunk.strip(),
                    chunk_start,
                    chunk_start + len(current_chunk)
                ))
                
                # Start new chunk
                chunk_start = current_pos
                current_chunk = paragraph
            else:
                if current_chunk:
                    current_chunk += '\n\n' + paragraph
                else:
                    current_chunk = paragraph
                    chunk_start = current_pos
            
            current_pos += len(paragraph) + 2  # +2 for \n\n
        
        # Add final chunk
        if current_chunk:
            segments.append(ContentSegment(
                current_chunk.strip(),
                chunk_start,
                current_pos
            ))
        
        logger.info(f"Split content into {len(segments)} segments")
        return segments
