# Knowledge Graph Builder

A system that builds knowledge graphs from text documents and chat records using PocketFlow framework.

## Features

- **Multi-format Support**: Process TXT, DOCX, PDF files and chat logs
- **Parallel Processing**: Distribute segments to multiple LLMs for efficient processing
- **Graph Management**: Support both independent per-file graphs (Batch mode) and unified global graph (Merge mode)
- **Quality Enhancement**: Entity normalization, deduplication, and description enrichment
- **Multiple Outputs**: Static HTML Wiki, JSON/RDF exports, Elasticsearch indexing
- **Web Interface**: Interactive graph visualization and search

## Quick Start

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd planner

# Install dependencies
pip install -r requirements.txt

# Copy environment configuration
cp .env.example .env
# Edit .env with your settings
```

### Basic Usage

```bash
# Process documents and build knowledge graph
python main.py process /path/to/documents --mode merge --output ./output

# Start web interface
python main.py serve --host 0.0.0.0 --port 8000

# List available models
python main.py models

# Show configuration
python main.py config
```

### Configuration

The system uses environment variables for configuration. Key settings:

```bash
# LLM Configuration
LLM_BASE_URL=https://gateway.chat.sensedeal.vip/v1
LLM_API_KEY=974fd8d1c155aa3d04b17bf253176b5e
LLM_MODEL_LOCAL=qwen2.5-32b-instruct-int4
LLM_MODEL_PERFORMANCE=doubao-seed-1.6

# Processing Configuration
PROCESSING_MODE=merge  # "batch" or "merge"
MAX_CONCURRENT_SEGMENTS=5
ENTITY_SIMILARITY_THRESHOLD=0.85

# Output Configuration
WEB_HOST=0.0.0.0
WEB_PORT=8000
```

## Architecture

The system is built using the PocketFlow framework with the following components:

### Flow Structure

```
MainFlow
├── FileProcessingFlow
│   ├── ParseDocument
│   ├── SplitLongContent
│   ├── SegmentProcessingBatchFlow
│   │   ├── ProcessSegmentBatch (Parallel LLM)
│   │   └── MergeSegmentResults
│   └── EntityNormalizationFlow
│       ├── NormalizeEntities
│       └── AddNodeTagsAndDescriptions
├── GraphBuildFlow
│   ├── GraphModeSelector
│   ├── BuildGraphBatch (Batch mode)
│   └── UpdateGlobalGraph (Merge mode)
└── PostProcessingFlow
    ├── GenerateWikiPages
    ├── PersistKnowledgeGraph
    └── CleanTemporaryFiles
```

### Processing Modes

- **Batch Mode**: Creates independent knowledge graphs for each document
- **Merge Mode**: Creates a unified knowledge graph combining all documents

### Supported Models

The system supports OpenAI-compatible APIs with the following recommended models:

- `qwen2.5-32b-instruct-int4`: Local model, cost-effective
- `doubao-seed-1.6`: High-performance model

## Examples

### Process a single document

```bash
python main.py process document.pdf --mode merge --format html json
```

### Process a directory of documents

```bash
python main.py process /path/to/docs --mode batch --output ./results
```

### Start web interface with custom settings

```bash
python main.py serve --host localhost --port 3000 --reload
```

## Output Formats

- **HTML Wiki**: Interactive web pages with entity relationships
- **JSON**: Structured graph data for programmatic access
- **RDF**: Semantic web format (Turtle)
- **Elasticsearch**: Search index for advanced querying

## Development

### Project Structure

```
planner/
├── config/          # Configuration management
├── flows/           # PocketFlow workflow definitions
├── nodes/           # Individual processing nodes
├── utils/           # Utility functions
├── web/             # Web interface (future)
├── main.py          # CLI entry point
└── requirements.txt # Dependencies
```

### Adding New Features

1. Create new nodes in the `nodes/` directory
2. Define flows in the `flows/` directory
3. Add utility functions in `utils/`
4. Update configuration in `config/settings.py`

### Testing

```bash
# Run tests
pytest

# Run with coverage
pytest --cov=. --cov-report=html
```

## Troubleshooting

### Common Issues

1. **LLM API Errors**: Check your API key and endpoint configuration
2. **Memory Issues**: Reduce `MAX_CONCURRENT_SEGMENTS` for large documents
3. **Slow Processing**: Use local model (`qwen2.5-32b-instruct-int4`) for faster processing

### Logging

Set log level in environment:

```bash
LOG_LEVEL=DEBUG python main.py process documents/
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
