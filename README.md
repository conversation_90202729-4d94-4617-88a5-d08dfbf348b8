# Knowledge Graph Builder from Text Documents

一个从文本文档构建知识图谱的系统，支持文档解析、语义分割、并行LLM处理、实体关系提取、去重、图合并、HTML wiki生成和持久化存储。

## 功能特性

- **输入支持**: TXT/DOCX/PDF文件和聊天记录（如微信）
- **并行处理**: 分割长文本并分发到多个LLM（`qwen2.5-32b-instruct-int4`, `doubao-seed-1.6`）
- **图管理**:
  - 独立的每文件图谱（`Batch`模式）
  - 合并的统一图谱（`Merge`模式）
- **质量增强**: 实体规范化、去重、描述/标签丰富
- **输出选项**:
  - 静态HTML Wiki页面
  - JSON/RDF图格式
  - Neo4j + Cubeflow/Elasticsearch索引存储
- **Web界面**: 可搜索、可过滤的交互式图可视化

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境

复制环境变量模板并配置：

```bash
cp .env.example .env
# 编辑 .env 文件设置你的配置
```

### 3. 运行处理

处理单个文件或目录：

```bash
# 处理单个文件
python main.py process document.pdf

# 处理目录中的所有文件
python main.py process ./documents --mode merge --output ./output

# 指定输出格式
python main.py process ./documents -f json -f html -f rdf
```

### 4. 启动Web界面

```bash
python main.py serve --host 0.0.0.0 --port 8000
```

然后访问 http://localhost:8000 查看知识图谱。

## 项目结构

```
planner/
├── main.py                 # 主入口文件
├── config/                 # 配置文件
│   ├── settings.py        # 设置管理
│   └── config.yaml        # 默认配置
├── utils/                  # 工具函数
│   ├── file_utils.py      # 文件处理工具
│   ├── llm_client.py      # LLM客户端
│   ├── graph_ops.py       # 图操作
│   ├── wiki_renderer.py   # Wiki渲染
│   ├── index_connector.py # 搜索索引连接
│   ├── security_filters.py # 安全过滤
│   └── task_queue.py      # 异步任务管理
├── flows/                  # 流程定义
│   ├── main_flow.py       # 主流程
│   ├── file_processing_flow.py # 文件处理流程
│   └── segment_processing_flow.py # 分段处理流程
├── nodes/                  # 节点实现
│   ├── parse_document.py  # 文档解析
│   ├── split_content.py   # 内容分割
│   ├── process_segment.py # 分段处理
│   ├── normalize_entities.py # 实体规范化
│   ├── build_graph.py     # 图构建
│   └── generate_wiki.py   # Wiki生成
├── web/                    # Web界面
│   ├── app.py             # FastAPI应用
│   ├── templates/         # HTML模板
│   └── static/            # 静态文件
├── tests/                  # 测试文件
├── docs/                   # 文档
├── data/                   # 数据目录
└── output/                 # 输出目录
```

## 配置说明

主要配置在 `config/config.yaml` 中：

### LLM配置

```yaml
llm:
  base_url: "https://gateway.chat.sensedeal.vip/v1"
  api_key: "974fd8d1c155aa3d04b17bf253176b5e"
  models:
    primary: "qwen2.5-32b-instruct-int4"  # 本地模型，成本低
    secondary: "doubao-seed-1.6"          # 备用模型，性能好
```

### 处理配置

```yaml
processing:
  mode: "merge"  # "batch" 或 "merge"
  max_concurrent_files: 3
  max_concurrent_segments: 5
  chunk_size: 2000
```

## 使用示例

### 命令行使用

```bash
# 基本处理
python main.py process ./documents

# 批处理模式（每个文件独立图谱）
python main.py process ./documents --mode batch

# 合并模式（统一图谱）
python main.py process ./documents --mode merge

# 指定并行度
python main.py process ./documents --parallel 5

# 限制文件数量
python main.py process ./documents --max-files 10
```

### Python API使用

```python
from flows.main_flow import MainFlow
from config.settings import Settings

# 初始化设置
settings = Settings()

# 创建共享上下文
shared_context = {
    "context": {
        "input_files": ["document1.pdf", "document2.txt"],
        "processing_mode": "merge",
        "output_path": "output/",
    },
    # ... 其他配置
}

# 运行主流程
main_flow = MainFlow(shared_context, settings)
await main_flow.run()
```

## 开发

### 运行测试

```bash
python main.py test
```

或直接使用pytest：

```bash
pytest tests/ -v
```

### 代码格式化

```bash
black .
flake8 .
```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
