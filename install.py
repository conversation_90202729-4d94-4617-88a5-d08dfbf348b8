#!/usr/bin/env python3
"""Installation script for Knowledge Graph Builder."""

import sys
import subprocess
import os
from pathlib import Path


def run_command(cmd, description):
    """Run a command and return success status."""
    print(f"📦 {description}...")
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        return False


def check_python_version():
    """Check Python version."""
    if sys.version_info < (3, 8):
        print(f"❌ Python 3.8+ required, found {sys.version}")
        return False
    print(f"✅ Python {sys.version.split()[0]}")
    return True


def install_core_dependencies():
    """Install core dependencies first."""
    core_deps = [
        "rich>=13.0.0",
        "click>=8.1.0", 
        "fastapi>=0.100.0",
        "uvicorn[standard]>=0.23.0",
        "pydantic>=2.0.0",
        "python-dotenv>=1.0.0",
        "loguru>=0.7.0",
        "jinja2>=3.1.0",
        "python-multipart>=0.0.6",
        "aiofiles>=0.8.0",
        "httpx>=0.24.0"
    ]
    
    cmd = [sys.executable, "-m", "pip", "install"] + core_deps
    return run_command(cmd, "Installing core dependencies")


def install_all_dependencies():
    """Install all dependencies from requirements.txt."""
    if not Path("requirements.txt").exists():
        print("❌ requirements.txt not found")
        return False
    
    cmd = [sys.executable, "-m", "pip", "install", "-r", "requirements.txt"]
    return run_command(cmd, "Installing all dependencies")


def create_env_file():
    """Create .env file if it doesn't exist."""
    env_file = Path(".env")
    if env_file.exists():
        print("✅ .env file already exists")
        return True
    
    env_content = """# LLM Configuration
LLM_BASE_URL=https://gateway.chat.sensedeal.vip/v1
LLM_API_KEY=974fd8d1c155aa3d04b17bf253176b5e
LLM_MODEL_LOCAL=qwen2.5-32b-instruct-int4
LLM_MODEL_PERFORMANCE=doubao-seed-1.6

# Processing Configuration
PROCESSING_MODE=merge
MAX_WORKERS=5
ENTITY_SIMILARITY_THRESHOLD=0.85

# Web Configuration
WEB_HOST=0.0.0.0
WEB_PORT=8000
DEBUG=false

# Storage Configuration
ELASTICSEARCH_HOSTS=localhost:9200
TEMP_DIR=./temp

# Logging
LOG_LEVEL=INFO
"""
    
    try:
        env_file.write_text(env_content)
        print("✅ Created .env file")
        return True
    except Exception as e:
        print(f"❌ Failed to create .env file: {e}")
        return False


def test_installation():
    """Test if installation was successful."""
    print("🧪 Testing installation...")
    
    try:
        # Test core imports
        import rich
        import click
        import fastapi
        import uvicorn
        print("✅ Core packages imported successfully")
        
        # Test project imports
        sys.path.insert(0, str(Path.cwd()))
        from config.settings import Settings
        print("✅ Project configuration loaded")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import test failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False


def main():
    """Main installation function."""
    print("🚀 Knowledge Graph Builder - Installation")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        return 1
    
    # Install core dependencies first
    if not install_core_dependencies():
        print("❌ Failed to install core dependencies")
        return 1
    
    # Install all dependencies
    if not install_all_dependencies():
        print("⚠️  Some dependencies failed to install, but core ones are available")
    
    # Create environment file
    create_env_file()
    
    # Test installation
    if test_installation():
        print("\n🎉 Installation completed successfully!")
        print("\n📋 Next steps:")
        print("1. Edit .env file with your API keys if needed")
        print("2. Run: python main.py serve")
        print("3. Open http://localhost:8000 in your browser")
    else:
        print("\n⚠️  Installation completed with some issues")
        print("You may need to install additional dependencies manually")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
