"""Output nodes for generating wiki pages, persisting graphs, and cleanup."""

import asyncio
import json
import shutil
from pathlib import Path
from typing import Dict, List, Any, Optional

import networkx as nx
from pocketflow import BatchNode, AsyncParallelBatchNode, Node
from loguru import logger

from utils.wiki_renderer import WikiRenderer
from utils.index_connector import IndexConnector
from utils.graph_ops import GraphOperations, Entity
from config.settings import Settings


class GenerateWikiPages(BatchNode):
    """Generate static HTML wiki pages from knowledge graph."""
    
    def __init__(self, template_engine: str = "Jinja2"):
        super().__init__()
        self.template_engine = template_engine
        self.wiki_renderer = WikiRenderer()
        self.settings = Settings()
    
    def prep(self, shared: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Prepare for wiki generation.
        
        Args:
            shared: Shared data structure
            
        Returns:
            List of wiki generation tasks
        """
        # Get the knowledge graph
        graph_dict = shared.get("knowledge_graph", {})
        
        if not graph_dict.get("nodes"):
            logger.warning("No knowledge graph found for wiki generation")
            return []
        
        # Get output path
        context = shared.get("context", {})
        output_path = Path(context.get("output_path", "./output"))
        wiki_output_path = output_path / "wiki"
        
        # Create wiki generation tasks
        tasks = []
        
        # Task for index page
        tasks.append({
            "type": "index",
            "graph_dict": graph_dict,
            "output_path": wiki_output_path / "index.html"
        })
        
        # Task for entities list page
        tasks.append({
            "type": "entities_list",
            "graph_dict": graph_dict,
            "output_path": wiki_output_path / "entities.html"
        })
        
        # Tasks for individual entity pages
        for node_id, node_data in graph_dict["nodes"].items():
            tasks.append({
                "type": "entity_page",
                "node_id": node_id,
                "node_data": node_data,
                "graph_dict": graph_dict,
                "output_path": wiki_output_path / f"{node_id}.html"
            })
        
        logger.info(f"Prepared {len(tasks)} wiki generation tasks")
        return tasks
    
    def exec(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute wiki page generation task.
        
        Args:
            task: Wiki generation task
            
        Returns:
            Task result
        """
        task_type = task["type"]
        output_path = Path(task["output_path"])
        
        # Ensure output directory exists
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            if task_type == "index":
                # Generate index page
                graph_dict = task["graph_dict"]
                graph_ops = GraphOperations()
                graph = graph_ops.import_from_dict(graph_dict)
                
                self.wiki_renderer.render_index_page(graph, output_path)
                
            elif task_type == "entities_list":
                # Generate entities list page
                graph_dict = task["graph_dict"]
                graph_ops = GraphOperations()
                graph = graph_ops.import_from_dict(graph_dict)
                
                self.wiki_renderer.render_all_entities_page(graph, output_path)
                
            elif task_type == "entity_page":
                # Generate individual entity page
                node_id = task["node_id"]
                node_data = task["node_data"]
                graph_dict = task["graph_dict"]
                
                entity = Entity.from_dict(node_data)
                
                # Get relationships for this entity
                relationships = []
                for edge in graph_dict["edges"]:
                    if edge["source"] == node_id:
                        target_data = graph_dict["nodes"].get(edge["target"], {})
                        target_entity = Entity.from_dict(target_data)
                        
                        relationships.append({
                            "relation_type": edge["relation_type"],
                            "target_id": edge["target"],
                            "target_name": target_entity.canonical_name,
                            "description": edge.get("description", "")
                        })
                
                self.wiki_renderer.render_entity_page(entity, node_id, relationships, output_path)
            
            return {
                "task_type": task_type,
                "output_path": str(output_path),
                "success": True,
                "error": None
            }
            
        except Exception as e:
            logger.error(f"Failed to generate {task_type}: {e}")
            return {
                "task_type": task_type,
                "output_path": str(output_path),
                "success": False,
                "error": str(e)
            }
    
    def post(
        self,
        shared: Dict[str, Any],
        prep_res: List[Dict[str, Any]],
        exec_res: List[Dict[str, Any]]
    ) -> str:
        """
        Post-process wiki generation results.
        
        Args:
            shared: Shared data structure
            prep_res: Preparation results
            exec_res: Execution results
            
        Returns:
            Next action
        """
        successful_tasks = [r for r in exec_res if r["success"]]
        failed_tasks = [r for r in exec_res if not r["success"]]
        
        # Store wiki pages in outputs
        outputs = shared.setdefault("outputs", {})
        wiki_pages = {}
        
        for result in successful_tasks:
            if result["task_type"] == "entity_page":
                # Extract entity name from the task
                entity_name = Path(result["output_path"]).stem
                wiki_pages[entity_name] = result["output_path"]
        
        outputs["wiki_pages"] = wiki_pages
        
        # Generate complete wiki site
        if successful_tasks:
            try:
                context = shared.get("context", {})
                output_path = Path(context.get("output_path", "./output"))
                wiki_output_path = output_path / "wiki"
                
                graph_dict = shared.get("knowledge_graph", {})
                if graph_dict.get("nodes"):
                    graph_ops = GraphOperations()
                    graph = graph_ops.import_from_dict(graph_dict)
                    
                    generated_pages = self.wiki_renderer.generate_wiki_site(
                        graph, wiki_output_path, include_css=True, include_js=True
                    )
                    
                    outputs["wiki_site_path"] = str(wiki_output_path)
                    outputs["generated_pages"] = generated_pages
                    
            except Exception as e:
                logger.error(f"Failed to generate complete wiki site: {e}")
        
        logger.info(f"Wiki generation completed: {len(successful_tasks)} successful, {len(failed_tasks)} failed")
        
        return "default"


class PersistKnowledgeGraph(AsyncParallelBatchNode):
    """Store final knowledge graph in multiple formats."""
    
    def __init__(self):
        super().__init__(max_retries=2, wait=5)
        self.settings = Settings()
        self.index_connector = IndexConnector(self.settings)
    
    async def prep_async(self, shared: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Prepare for graph persistence.
        
        Args:
            shared: Shared data structure
            
        Returns:
            List of persistence tasks
        """
        graph_dict = shared.get("knowledge_graph", {})
        
        if not graph_dict.get("nodes"):
            logger.warning("No knowledge graph found for persistence")
            return []
        
        context = shared.get("context", {})
        output_path = Path(context.get("output_path", "./output"))
        output_formats = context.get("output_formats", ["json", "html"])
        
        # Create persistence tasks
        tasks = []
        
        if "json" in output_formats:
            tasks.append({
                "format": "json",
                "output_path": output_path / "knowledge_graph.json",
                "graph_dict": graph_dict
            })
        
        if "rdf" in output_formats:
            tasks.append({
                "format": "rdf",
                "output_path": output_path / "knowledge_graph.ttl",
                "graph_dict": graph_dict
            })
        
        if "neo4j" in output_formats:
            tasks.append({
                "format": "neo4j",
                "output_path": None,  # Neo4j doesn't use file output
                "graph_dict": graph_dict
            })
        
        # Always create Elasticsearch index if configured
        tasks.append({
            "format": "elasticsearch",
            "output_path": None,
            "graph_dict": graph_dict
        })
        
        logger.info(f"Prepared {len(tasks)} persistence tasks")
        return tasks
    
    async def exec_async(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute graph persistence task.
        
        Args:
            task: Persistence task
            
        Returns:
            Task result
        """
        format_type = task["format"]
        output_path = task.get("output_path")
        graph_dict = task["graph_dict"]
        
        try:
            graph_ops = GraphOperations()
            graph = graph_ops.import_from_dict(graph_dict)
            
            if format_type == "json":
                # Export to JSON
                json_data = self.index_connector.export_to_json(graph)
                
                if output_path:
                    output_path = Path(output_path)
                    output_path.parent.mkdir(parents=True, exist_ok=True)
                    output_path.write_text(json_data, encoding='utf-8')
                
                return {
                    "format": format_type,
                    "success": True,
                    "output_path": str(output_path) if output_path else None,
                    "data": json_data,
                    "error": None
                }
                
            elif format_type == "rdf":
                # Export to RDF
                rdf_data = self.index_connector.export_to_rdf(graph, format="turtle")
                
                if output_path and rdf_data:
                    output_path = Path(output_path)
                    output_path.parent.mkdir(parents=True, exist_ok=True)
                    output_path.write_text(rdf_data, encoding='utf-8')
                
                return {
                    "format": format_type,
                    "success": bool(rdf_data),
                    "output_path": str(output_path) if output_path else None,
                    "data": rdf_data,
                    "error": None if rdf_data else "RDF export failed"
                }
                
            elif format_type == "elasticsearch":
                # Index to Elasticsearch
                success = await asyncio.create_task(
                    asyncio.to_thread(
                        self.index_connector.index_knowledge_graph_to_elasticsearch,
                        graph
                    )
                )
                
                return {
                    "format": format_type,
                    "success": success,
                    "output_path": None,
                    "data": None,
                    "error": None if success else "Elasticsearch indexing failed"
                }
                
            elif format_type == "neo4j":
                # TODO: Implement Neo4j export
                logger.warning("Neo4j export not yet implemented")
                return {
                    "format": format_type,
                    "success": False,
                    "output_path": None,
                    "data": None,
                    "error": "Neo4j export not implemented"
                }
            
            else:
                return {
                    "format": format_type,
                    "success": False,
                    "output_path": None,
                    "data": None,
                    "error": f"Unknown format: {format_type}"
                }
                
        except Exception as e:
            logger.error(f"Failed to persist graph in {format_type} format: {e}")
            return {
                "format": format_type,
                "success": False,
                "output_path": str(output_path) if output_path else None,
                "data": None,
                "error": str(e)
            }
    
    async def post_async(
        self,
        shared: Dict[str, Any],
        prep_res: List[Dict[str, Any]],
        exec_res: List[Dict[str, Any]]
    ) -> str:
        """
        Post-process persistence results.
        
        Args:
            shared: Shared data structure
            prep_res: Preparation results
            exec_res: Execution results
            
        Returns:
            Next action
        """
        successful_tasks = [r for r in exec_res if r["success"]]
        failed_tasks = [r for r in exec_res if not r["success"]]
        
        # Store results in outputs
        outputs = shared.setdefault("outputs", {})
        
        for result in successful_tasks:
            if result["format"] == "json" and result["data"]:
                outputs["json_graph"] = result["data"]
            elif result["format"] == "elasticsearch":
                outputs["search_index"] = "elasticsearch"
        
        # Log results
        for result in successful_tasks:
            logger.info(f"Successfully persisted graph in {result['format']} format")
        
        for result in failed_tasks:
            logger.error(f"Failed to persist graph in {result['format']} format: {result['error']}")
        
        logger.info(f"Graph persistence completed: {len(successful_tasks)} successful, {len(failed_tasks)} failed")
        
        return "default"


class CleanTemporaryFiles(Node):
    """Remove intermediate processing artifacts."""
    
    def __init__(self):
        super().__init__()
        self.settings = Settings()
    
    def prep(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare for cleanup.
        
        Args:
            shared: Shared data structure
            
        Returns:
            Preparation data
        """
        # Check if final output was saved
        outputs = shared.get("outputs", {})
        
        final_output_saved = bool(
            outputs.get("json_graph") or
            outputs.get("wiki_pages") or
            outputs.get("search_index")
        )
        
        if not final_output_saved:
            raise RuntimeError("Attempted cleanup before output persistence completed")
        
        # Get temporary directories to clean
        temp_dirs = []
        
        # Add configured temp directory
        temp_dir = Path(self.settings.temp_dir)
        if temp_dir.exists():
            temp_dirs.append(temp_dir)
        
        # Add any temporary files created during processing
        context = shared.get("context", {})
        if "temp_files" in context:
            temp_dirs.extend(context["temp_files"])
        
        return {
            "temp_dirs": temp_dirs,
            "final_output_saved": final_output_saved
        }
    
    def exec(self, prep_res: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute cleanup.
        
        Args:
            prep_res: Preparation results
            
        Returns:
            Cleanup results
        """
        temp_dirs = prep_res["temp_dirs"]
        cleaned_dirs = []
        failed_dirs = []
        
        for temp_dir in temp_dirs:
            try:
                temp_path = Path(temp_dir)
                
                if temp_path.exists() and temp_path.is_dir():
                    # Only clean if it's actually a temp directory
                    if "temp" in temp_path.name.lower() or "tmp" in temp_path.name.lower():
                        shutil.rmtree(temp_path)
                        cleaned_dirs.append(str(temp_path))
                        logger.info(f"Cleaned temporary directory: {temp_path}")
                    else:
                        logger.warning(f"Skipped cleanup of non-temp directory: {temp_path}")
                
            except Exception as e:
                logger.error(f"Failed to clean {temp_dir}: {e}")
                failed_dirs.append(str(temp_dir))
        
        return {
            "cleaned_dirs": cleaned_dirs,
            "failed_dirs": failed_dirs
        }
    
    def post(
        self,
        shared: Dict[str, Any],
        prep_res: Dict[str, Any],
        exec_res: Dict[str, Any]
    ) -> str:
        """
        Post-process cleanup results.
        
        Args:
            shared: Shared data structure
            prep_res: Preparation results
            exec_res: Execution results
            
        Returns:
            Next action
        """
        cleaned_dirs = exec_res["cleaned_dirs"]
        failed_dirs = exec_res["failed_dirs"]
        
        # Update task state
        task_state = shared.setdefault("task_state", {})
        task_state["cleanup_completed"] = True
        task_state["cleaned_directories"] = cleaned_dirs
        task_state["cleanup_failures"] = failed_dirs
        
        logger.info(f"Cleanup completed: {len(cleaned_dirs)} directories cleaned, "
                   f"{len(failed_dirs)} failures")
        
        return "default"
