<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}知识图谱构建器{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/static/css/style.css" rel="stylesheet">
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-project-diagram me-2"></i>
                知识图谱构建器
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/upload">
                            <i class="fas fa-upload me-1"></i>上传文档
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/graph">
                            <i class="fas fa-sitemap me-1"></i>图谱可视化
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/search">
                            <i class="fas fa-search me-1"></i>搜索
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <span class="navbar-text" id="status-indicator">
                            <i class="fas fa-circle text-success me-1"></i>
                            <span id="status-text">就绪</span>
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid py-4">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 知识图谱构建器. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="mb-0">
                        <small class="text-muted">
                            Powered by FastAPI & NetworkX
                        </small>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- D3.js for graph visualization -->
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <!-- Custom JS -->
    <script src="/static/js/app.js"></script>
    
    {% block extra_js %}{% endblock %}

    <script>
        // Global status checking
        function updateStatus() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    const statusIndicator = document.getElementById('status-indicator');
                    const statusText = document.getElementById('status-text');
                    
                    if (data.status === 'processing') {
                        statusIndicator.innerHTML = '<i class="fas fa-spinner fa-spin text-warning me-1"></i>';
                        statusText.textContent = '处理中...';
                    } else if (data.status === 'completed') {
                        statusIndicator.innerHTML = '<i class="fas fa-check-circle text-success me-1"></i>';
                        statusText.textContent = '完成';
                    } else if (data.status === 'failed') {
                        statusIndicator.innerHTML = '<i class="fas fa-exclamation-circle text-danger me-1"></i>';
                        statusText.textContent = '失败';
                    } else {
                        statusIndicator.innerHTML = '<i class="fas fa-circle text-success me-1"></i>';
                        statusText.textContent = '就绪';
                    }
                })
                .catch(error => {
                    console.error('Status check failed:', error);
                });
        }

        // Update status every 5 seconds
        setInterval(updateStatus, 5000);
        
        // Initial status check
        updateStatus();
    </script>
</body>
</html>
