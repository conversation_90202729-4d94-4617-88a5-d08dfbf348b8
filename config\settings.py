"""Application settings and configuration."""

from typing import List, Optional
from pydantic import BaseSettings, Field


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""
    
    # LLM Configuration
    llm_base_url: str = Field(default="https://gateway.chat.sensedeal.vip/v1", env="LLM_BASE_URL")
    llm_api_key: str = Field(default="974fd8d1c155aa3d04b17bf253176b5e", env="LLM_API_KEY")
    llm_model_local: str = Field(default="qwen2.5-32b-instruct-int4", env="LLM_MODEL_LOCAL")
    llm_model_performance: str = Field(default="doubao-seed-1.6", env="LLM_MODEL_PERFORMANCE")
    
    # Database Configuration
    neo4j_uri: str = Field(default="bolt://localhost:7687", env="NEO4J_URI")
    neo4j_username: str = Field(default="neo4j", env="NEO4J_USERNAME")
    neo4j_password: str = Field(default="password", env="NEO4J_PASSWORD")
    
    # Elasticsearch Configuration
    elasticsearch_hosts: str = Field(default="localhost:9200", env="ELASTICSEARCH_HOSTS")
    elasticsearch_index: str = Field(default="knowledge_graph", env="ELASTICSEARCH_INDEX")
    
    # Redis Configuration
    redis_url: str = Field(default="redis://localhost:6379", env="REDIS_URL")
    
    # Application Configuration
    debug: bool = Field(default=True, env="DEBUG")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    max_workers: int = Field(default=4, env="MAX_WORKERS")
    processing_mode: str = Field(default="merge", env="PROCESSING_MODE")  # "batch" or "merge"
    
    # Security
    secret_key: str = Field(default="your-secret-key-here", env="SECRET_KEY")
    enable_pii_detection: bool = Field(default=True, env="ENABLE_PII_DETECTION")
    
    # File Processing
    max_file_size_mb: int = Field(default=100, env="MAX_FILE_SIZE_MB")
    temp_dir: str = Field(default="./temp", env="TEMP_DIR")
    supported_formats: List[str] = Field(default=["txt", "docx", "pdf", "chat"], env="SUPPORTED_FORMATS")
    
    # Web Interface
    web_host: str = Field(default="0.0.0.0", env="WEB_HOST")
    web_port: int = Field(default=8000, env="WEB_PORT")
    
    # Graph Processing
    entity_similarity_threshold: float = Field(default=0.85, env="ENTITY_SIMILARITY_THRESHOLD")
    max_concurrent_segments: int = Field(default=5, env="MAX_CONCURRENT_SEGMENTS")
    segment_timeout_seconds: int = Field(default=300, env="SEGMENT_TIMEOUT_SECONDS")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
    
    def get_model_for_index(self, index: int) -> str:
        """Get model based on index for load balancing."""
        return self.llm_model_local if index % 2 == 0 else self.llm_model_performance
    
    @property
    def elasticsearch_hosts_list(self) -> List[str]:
        """Convert elasticsearch hosts string to list."""
        return [host.strip() for host in self.elasticsearch_hosts.split(",")]
