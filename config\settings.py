"""
Configuration settings for the Knowledge Graph Builder
"""

import os
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
import yaml
from pydantic import BaseSettings, Field


@dataclass
class LLMConfig:
    """LLM configuration"""
    base_url: str = "https://gateway.chat.sensedeal.vip/v1"
    api_key: str = "974fd8d1c155aa3d04b17bf253176b5e"
    models: Dict[str, str] = field(default_factory=lambda: {
        "primary": "qwen2.5-32b-instruct-int4",
        "secondary": "doubao-seed-1.6",
        "fallback": "gpt-3.5-turbo"
    })
    max_tokens: int = 4000
    temperature: float = 0.1
    timeout: int = 300
    max_retries: int = 3
    retry_delay: int = 2


@dataclass
class ProcessingConfig:
    """Processing configuration"""
    mode: str = "merge"  # "batch" or "merge"
    max_concurrent_files: int = 3
    max_concurrent_segments: int = 5
    chunk_size: int = 2000
    chunk_overlap: int = 200
    entity_similarity_threshold: float = 0.85
    min_entity_confidence: float = 0.7
    max_entities_per_segment: int = 50


@dataclass
class FilesConfig:
    """File processing configuration"""
    supported_formats: List[str] = field(default_factory=lambda: ["txt", "docx", "pdf"])
    max_file_size_mb: int = 100
    encoding: str = "utf-8"
    min_content_length: int = 100
    max_content_length: int = 1000000


@dataclass
class GraphConfig:
    """Graph configuration"""
    node_id_strategy: str = "canonical_name"
    edge_weight_strategy: str = "frequency"
    max_nodes: int = 10000
    max_edges: int = 50000
    merge_strategy: str = "weighted_priority"
    source_weights: Dict[str, int] = field(default_factory=lambda: {
        "pdf": 3,
        "docx": 2,
        "txt": 1,
        "chat": 1
    })


@dataclass
class OutputConfig:
    """Output configuration"""
    formats: List[str] = field(default_factory=lambda: ["json", "html", "rdf"])
    wiki_template_dir: str = "web/templates"
    wiki_static_dir: str = "web/static"
    wiki_output_dir: str = "output/wiki"
    search_enabled: bool = True
    search_backend: str = "elasticsearch"
    search_index_name: str = "knowledge_graph"


@dataclass
class StorageConfig:
    """Storage configuration"""
    neo4j_enabled: bool = False
    neo4j_uri: str = "bolt://localhost:7687"
    neo4j_username: str = "neo4j"
    neo4j_password: str = "password"
    
    elasticsearch_enabled: bool = True
    elasticsearch_hosts: List[str] = field(default_factory=lambda: ["localhost:9200"])
    elasticsearch_index_settings: Dict[str, int] = field(default_factory=lambda: {
        "number_of_shards": 1,
        "number_of_replicas": 0
    })


@dataclass
class SecurityConfig:
    """Security configuration"""
    enable_pii_detection: bool = True
    enable_xss_protection: bool = True
    max_input_size: int = 10485760  # 10MB
    pii_patterns: List[str] = field(default_factory=lambda: [
        "phone_number", "email", "id_card", "credit_card"
    ])


@dataclass
class LoggingConfig:
    """Logging configuration"""
    level: str = "INFO"
    format: str = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
    file: str = "logs/app.log"
    rotation: str = "1 day"
    retention: str = "30 days"


@dataclass
class WebConfig:
    """Web interface configuration"""
    host: str = "0.0.0.0"
    port: int = 8000
    cors_origins: List[str] = field(default_factory=lambda: ["*"])
    static_files: bool = True


class Settings:
    """Main settings class"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.llm = LLMConfig()
        self.processing = ProcessingConfig()
        self.files = FilesConfig()
        self.graph = GraphConfig()
        self.output = OutputConfig()
        self.storage = StorageConfig()
        self.security = SecurityConfig()
        self.logging = LoggingConfig()
        self.web = WebConfig()
        
        # Load from config file if provided
        if config_file:
            self.load_from_file(config_file)
        else:
            # Try to load default config
            default_config = Path("config/config.yaml")
            if default_config.exists():
                self.load_from_file(str(default_config))
        
        # Override with environment variables
        self._load_from_env()
    
    def load_from_file(self, config_file: str):
        """Load configuration from YAML file"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            # Update configurations
            if 'llm' in config_data:
                self._update_dataclass(self.llm, config_data['llm'])
            if 'processing' in config_data:
                self._update_dataclass(self.processing, config_data['processing'])
            if 'files' in config_data:
                self._update_dataclass(self.files, config_data['files'])
            if 'graph' in config_data:
                self._update_dataclass(self.graph, config_data['graph'])
            if 'output' in config_data:
                self._update_dataclass(self.output, config_data['output'])
            if 'storage' in config_data:
                self._update_dataclass(self.storage, config_data['storage'])
            if 'security' in config_data:
                self._update_dataclass(self.security, config_data['security'])
            if 'logging' in config_data:
                self._update_dataclass(self.logging, config_data['logging'])
            if 'web' in config_data:
                self._update_dataclass(self.web, config_data['web'])
                
        except Exception as e:
            raise ValueError(f"Failed to load config file {config_file}: {e}")
    
    def _update_dataclass(self, obj: Any, data: Dict[str, Any]):
        """Update dataclass fields from dictionary"""
        for key, value in data.items():
            if hasattr(obj, key):
                setattr(obj, key, value)
    
    def _load_from_env(self):
        """Load configuration from environment variables"""
        # LLM configuration
        if os.getenv('LLM_BASE_URL'):
            self.llm.base_url = os.getenv('LLM_BASE_URL')
        if os.getenv('LLM_API_KEY'):
            self.llm.api_key = os.getenv('LLM_API_KEY')
        
        # Storage configuration
        if os.getenv('NEO4J_URI'):
            self.storage.neo4j_uri = os.getenv('NEO4J_URI')
        if os.getenv('NEO4J_USERNAME'):
            self.storage.neo4j_username = os.getenv('NEO4J_USERNAME')
        if os.getenv('NEO4J_PASSWORD'):
            self.storage.neo4j_password = os.getenv('NEO4J_PASSWORD')
        
        if os.getenv('ELASTICSEARCH_HOSTS'):
            self.storage.elasticsearch_hosts = os.getenv('ELASTICSEARCH_HOSTS').split(',')
        
        # Web configuration
        if os.getenv('WEB_HOST'):
            self.web.host = os.getenv('WEB_HOST')
        if os.getenv('WEB_PORT'):
            self.web.port = int(os.getenv('WEB_PORT'))
        
        # Logging configuration
        if os.getenv('LOG_LEVEL'):
            self.logging.level = os.getenv('LOG_LEVEL')
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert settings to dictionary"""
        return {
            'llm': self.llm.__dict__,
            'processing': self.processing.__dict__,
            'files': self.files.__dict__,
            'graph': self.graph.__dict__,
            'output': self.output.__dict__,
            'storage': self.storage.__dict__,
            'security': self.security.__dict__,
            'logging': self.logging.__dict__,
            'web': self.web.__dict__,
        }
