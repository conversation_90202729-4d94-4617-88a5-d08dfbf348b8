# Quick Start Guide - Knowledge Graph Builder

## 🚨 If you're seeing "ModuleNotFoundError"

Don't worry! This is a common issue. Here are several ways to fix it:

### Option 1: Quick Fix (Recommended)
```bash
python quick_fix.py
```
This will automatically install the essential dependencies.

### Option 2: Manual Installation
```bash
pip install rich click fastapi uvicorn python-dotenv loguru jinja2 python-multipart aiofiles httpx networkx beautifulsoup4 bleach
```

### Option 3: Full Installation
```bash
pip install -r requirements.txt
```

### Option 4: Minimal Installation
```bash
pip install -r requirements-minimal.txt
```

## 🚀 Starting the Application

After installing dependencies, you have several options:

### Full Application (Recommended)
```bash
python main.py serve
```

### Simple Server (Fallback)
If the main application doesn't work:
```bash
python simple_server.py
```

### Web Interface
Open your browser and go to: http://localhost:8000

## 📋 Step-by-Step Setup

1. **Install Python 3.8+** (if not already installed)

2. **Clone/Download the project**
   ```bash
   cd planner
   ```

3. **Install dependencies**
   ```bash
   python quick_fix.py
   ```

4. **Configure the application**
   - Edit the `.env` file (created automatically)
   - Add your LLM API key if needed

5. **Start the server**
   ```bash
   python main.py serve
   ```

6. **Open your browser**
   - Go to http://localhost:8000
   - Upload documents and start building knowledge graphs!

## 🔧 Troubleshooting

### "No module named 'rich'"
```bash
pip install rich
```

### "No module named 'click'"
```bash
pip install click
```

### "No module named 'fastapi'"
```bash
pip install fastapi uvicorn
```

### General dependency issues
```bash
python quick_fix.py
```

### Still having issues?
Try the simple server:
```bash
python simple_server.py
```

## 📁 Project Structure

```
planner/
├── main.py              # Main CLI application
├── simple_server.py     # Fallback simple server
├── quick_fix.py         # Dependency installer
├── install.py           # Full installer
├── requirements.txt     # All dependencies
├── requirements-minimal.txt  # Essential dependencies only
├── .env                 # Configuration (auto-created)
└── web/                 # Web interface files
```

## 🎯 What This Application Does

1. **Document Processing**: Upload TXT, DOCX, PDF files
2. **Entity Extraction**: AI-powered extraction of people, organizations, concepts
3. **Relationship Discovery**: Find connections between entities
4. **Knowledge Graph**: Build interactive graph visualizations
5. **Search & Explore**: Search entities and explore relationships

## 🔑 Configuration

The `.env` file contains important settings:

```bash
# LLM API Configuration
LLM_BASE_URL=https://gateway.chat.sensedeal.vip/v1
LLM_API_KEY=974fd8d1c155aa3d04b17bf253176b5e

# Processing Settings
PROCESSING_MODE=merge
MAX_WORKERS=5

# Web Server
WEB_HOST=0.0.0.0
WEB_PORT=8000
```

## 🆘 Need Help?

1. **Check the logs** - The application shows detailed error messages
2. **Try the simple server** - `python simple_server.py`
3. **Reinstall dependencies** - `python quick_fix.py`
4. **Check Python version** - Requires Python 3.8+

## 🎉 Success!

Once everything is working, you should see:
- Web interface at http://localhost:8000
- Upload page for documents
- Processing progress tracking
- Interactive knowledge graph visualization
- Entity search functionality

Happy knowledge graph building! 🚀
