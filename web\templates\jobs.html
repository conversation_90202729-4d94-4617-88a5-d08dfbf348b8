{% extends "base.html" %}

{% block title %}Processing Jobs - Knowledge Graph Builder{% endblock %}

{% block page_title %}Processing Jobs{% endblock %}

{% block header_actions %}
<div class="btn-group" role="group">
    <a href="/" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i>
        Back to Dashboard
    </a>
    <a href="/upload" class="btn btn-primary">
        <i class="fas fa-upload me-1"></i>
        New Job
    </a>
</div>
{% endblock %}

{% block content %}
<!-- Jobs Overview -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                <h5 class="card-title" id="pending-jobs">0</h5>
                <p class="card-text text-muted">Pending</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-cogs fa-2x text-primary mb-2"></i>
                <h5 class="card-title" id="running-jobs">0</h5>
                <p class="card-text text-muted">Running</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                <h5 class="card-title" id="completed-jobs">0</h5>
                <p class="card-text text-muted">Completed</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-exclamation-triangle fa-2x text-danger mb-2"></i>
                <h5 class="card-title" id="failed-jobs">0</h5>
                <p class="card-text text-muted">Failed</p>
            </div>
        </div>
    </div>
</div>

<!-- Jobs Filter and Controls -->
<div class="row mb-3">
    <div class="col-md-6">
        <div class="input-group">
            <input type="text" class="form-control" id="search-jobs" placeholder="Search jobs...">
            <button class="btn btn-outline-secondary" type="button" id="search-btn">
                <i class="fas fa-search"></i>
            </button>
        </div>
    </div>
    <div class="col-md-3">
        <select class="form-select" id="status-filter">
            <option value="">All Statuses</option>
            <option value="queued">Queued</option>
            <option value="processing">Processing</option>
            <option value="completed">Completed</option>
            <option value="failed">Failed</option>
        </select>
    </div>
    <div class="col-md-3">
        <div class="btn-group w-100" role="group">
            <button class="btn btn-outline-primary" id="refresh-btn">
                <i class="fas fa-sync-alt"></i>
                Refresh
            </button>
            <button class="btn btn-outline-danger" id="clear-completed-btn">
                <i class="fas fa-trash"></i>
                Clear Completed
            </button>
        </div>
    </div>
</div>

<!-- Jobs List -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h6 class="mb-0">
            <i class="fas fa-list me-2"></i>
            Processing Jobs
        </h6>
        <span id="jobs-count" class="badge bg-secondary">0 jobs</span>
    </div>
    <div class="card-body p-0">
        <div id="jobs-list">
            <!-- Jobs will be loaded here -->
        </div>
        
        <!-- Loading indicator -->
        <div id="jobs-loading" class="text-center py-4">
            <div class="loading-spinner me-2"></div>
            Loading jobs...
        </div>
        
        <!-- Empty state -->
        <div id="jobs-empty" class="text-center py-5" style="display: none;">
            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
            <h5>No Jobs Found</h5>
            <p class="text-muted">No processing jobs match your current filters.</p>
            <a href="/upload" class="btn btn-primary">
                <i class="fas fa-upload me-1"></i>
                Start New Job
            </a>
        </div>
    </div>
</div>

<!-- Job Details Modal -->
<div class="modal fade" id="job-modal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="job-modal-title">Job Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="job-modal-body">
                <!-- Job details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-danger" id="delete-job-btn" style="display: none;">
                    <i class="fas fa-trash me-1"></i>
                    Delete Job
                </button>
                <button type="button" class="btn btn-primary" id="view-results-btn" style="display: none;">
                    <i class="fas fa-eye me-1"></i>
                    View Results
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let allJobs = [];
let filteredJobs = [];
let refreshInterval = null;

document.addEventListener('DOMContentLoaded', function() {
    initializeJobsPage();
    loadJobs();
    
    // Auto-refresh every 5 seconds
    refreshInterval = setInterval(loadJobs, 5000);
});

function initializeJobsPage() {
    // Search functionality
    document.getElementById('search-jobs').addEventListener('input', applyFilters);
    document.getElementById('search-btn').addEventListener('click', applyFilters);
    
    // Status filter
    document.getElementById('status-filter').addEventListener('change', applyFilters);
    
    // Control buttons
    document.getElementById('refresh-btn').addEventListener('click', loadJobs);
    document.getElementById('clear-completed-btn').addEventListener('click', clearCompletedJobs);
    
    // Modal buttons
    document.getElementById('delete-job-btn').addEventListener('click', deleteCurrentJob);
}

async function loadJobs() {
    try {
        showLoading(true);
        
        // TODO: Replace with actual API endpoint
        // For now, use mock data
        allJobs = generateMockJobs();
        
        applyFilters();
        updateJobsOverview();
        
    } catch (error) {
        showAlert(`Failed to load jobs: ${error.message}`, 'danger');
    } finally {
        showLoading(false);
    }
}

function generateMockJobs() {
    // Generate mock jobs for demonstration
    return [
        {
            job_id: 'job-001',
            status: 'completed',
            progress: 1.0,
            message: 'Processing completed successfully',
            created_at: new Date(Date.now() - 3600000).toISOString(),
            files: ['document1.pdf', 'document2.txt'],
            request: { mode: 'merge', output_formats: ['html', 'json'] },
            result: {
                summary: {
                    documents_processed: 2,
                    entities_created: 45,
                    relations_created: 23,
                    execution_time: 120.5
                }
            }
        },
        {
            job_id: 'job-002',
            status: 'processing',
            progress: 0.65,
            message: 'Processing document segments...',
            created_at: new Date(Date.now() - 1800000).toISOString(),
            files: ['large_document.pdf'],
            request: { mode: 'batch', output_formats: ['html'] }
        },
        {
            job_id: 'job-003',
            status: 'failed',
            progress: 0.3,
            message: 'Processing failed',
            error: 'LLM API rate limit exceeded',
            created_at: new Date(Date.now() - 7200000).toISOString(),
            files: ['document3.docx'],
            request: { mode: 'merge', output_formats: ['json'] }
        }
    ];
}

function applyFilters() {
    const searchTerm = document.getElementById('search-jobs').value.toLowerCase();
    const statusFilter = document.getElementById('status-filter').value;
    
    filteredJobs = allJobs.filter(job => {
        const matchesSearch = !searchTerm || 
            job.job_id.toLowerCase().includes(searchTerm) ||
            job.files.some(file => file.toLowerCase().includes(searchTerm)) ||
            job.message.toLowerCase().includes(searchTerm);
        
        const matchesStatus = !statusFilter || job.status === statusFilter;
        
        return matchesSearch && matchesStatus;
    });
    
    displayJobs();
}

function displayJobs() {
    const container = document.getElementById('jobs-list');
    const countBadge = document.getElementById('jobs-count');
    const emptyState = document.getElementById('jobs-empty');
    
    countBadge.textContent = `${filteredJobs.length} jobs`;
    
    if (filteredJobs.length === 0) {
        container.innerHTML = '';
        emptyState.style.display = 'block';
        return;
    }
    
    emptyState.style.display = 'none';
    
    container.innerHTML = filteredJobs.map(job => createJobCard(job)).join('');
}

function createJobCard(job) {
    const statusClass = getStatusClass(job.status);
    const statusIcon = getStatusIcon(job.status);
    const createdAt = new Date(job.created_at).toLocaleString();
    
    return `
        <div class="border-bottom p-3 job-item" onclick="showJobDetails('${job.job_id}')">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <i class="fas ${statusIcon} ${statusClass} me-3"></i>
                        <div>
                            <h6 class="mb-1">${job.job_id}</h6>
                            <small class="text-muted">
                                ${job.files.length} file${job.files.length !== 1 ? 's' : ''} • 
                                ${job.request.mode} mode • 
                                Created ${createdAt}
                            </small>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="mb-1">
                        <span class="badge ${statusClass}">${job.status}</span>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar ${getProgressBarClass(job.status)}" 
                             style="width: ${job.progress * 100}%"></div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="text-end">
                        <div class="mb-1">
                            <small class="text-muted">${Math.round(job.progress * 100)}%</small>
                        </div>
                        <small class="text-muted">${job.message}</small>
                        ${job.error ? `<div class="text-danger small">${job.error}</div>` : ''}
                    </div>
                </div>
            </div>
        </div>
    `;
}

function getStatusClass(status) {
    const classes = {
        'queued': 'text-secondary',
        'processing': 'text-primary',
        'completed': 'text-success',
        'failed': 'text-danger'
    };
    return classes[status] || 'text-muted';
}

function getStatusIcon(status) {
    const icons = {
        'queued': 'fa-clock',
        'processing': 'fa-cogs',
        'completed': 'fa-check-circle',
        'failed': 'fa-exclamation-triangle'
    };
    return icons[status] || 'fa-question-circle';
}

function getProgressBarClass(status) {
    const classes = {
        'queued': 'bg-secondary',
        'processing': 'bg-primary',
        'completed': 'bg-success',
        'failed': 'bg-danger'
    };
    return classes[status] || 'bg-secondary';
}

function updateJobsOverview() {
    const statusCounts = {
        queued: 0,
        processing: 0,
        completed: 0,
        failed: 0
    };
    
    allJobs.forEach(job => {
        if (job.status === 'queued') statusCounts.queued++;
        else if (job.status === 'processing') statusCounts.processing++;
        else if (job.status === 'completed') statusCounts.completed++;
        else if (job.status === 'failed') statusCounts.failed++;
    });
    
    document.getElementById('pending-jobs').textContent = statusCounts.queued;
    document.getElementById('running-jobs').textContent = statusCounts.processing;
    document.getElementById('completed-jobs').textContent = statusCounts.completed;
    document.getElementById('failed-jobs').textContent = statusCounts.failed;
}

function showJobDetails(jobId) {
    const job = allJobs.find(j => j.job_id === jobId);
    if (!job) return;
    
    const modal = document.getElementById('job-modal');
    const title = document.getElementById('job-modal-title');
    const body = document.getElementById('job-modal-body');
    const deleteBtn = document.getElementById('delete-job-btn');
    const viewResultsBtn = document.getElementById('view-results-btn');
    
    title.textContent = `Job: ${job.job_id}`;
    
    body.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>Job Information</h6>
                <table class="table table-sm">
                    <tr>
                        <td><strong>Status:</strong></td>
                        <td><span class="badge ${getStatusClass(job.status)}">${job.status}</span></td>
                    </tr>
                    <tr>
                        <td><strong>Progress:</strong></td>
                        <td>${Math.round(job.progress * 100)}%</td>
                    </tr>
                    <tr>
                        <td><strong>Created:</strong></td>
                        <td>${new Date(job.created_at).toLocaleString()}</td>
                    </tr>
                    <tr>
                        <td><strong>Mode:</strong></td>
                        <td>${job.request.mode}</td>
                    </tr>
                    <tr>
                        <td><strong>Formats:</strong></td>
                        <td>${job.request.output_formats.join(', ')}</td>
                    </tr>
                </table>
                
                <h6>Files</h6>
                <ul class="list-group list-group-flush">
                    ${job.files.map(file => `
                        <li class="list-group-item px-0">${file}</li>
                    `).join('')}
                </ul>
            </div>
            
            <div class="col-md-6">
                <h6>Status</h6>
                <p>${job.message}</p>
                ${job.error ? `<div class="alert alert-danger">${job.error}</div>` : ''}
                
                ${job.result ? `
                    <h6>Results</h6>
                    <table class="table table-sm">
                        <tr>
                            <td>Documents:</td>
                            <td>${job.result.summary.documents_processed || 0}</td>
                        </tr>
                        <tr>
                            <td>Entities:</td>
                            <td>${job.result.summary.entities_created || 0}</td>
                        </tr>
                        <tr>
                            <td>Relations:</td>
                            <td>${job.result.summary.relations_created || 0}</td>
                        </tr>
                        <tr>
                            <td>Time:</td>
                            <td>${formatDuration(job.result.summary.execution_time || 0)}</td>
                        </tr>
                    </table>
                ` : ''}
            </div>
        </div>
    `;
    
    // Show/hide buttons based on job status
    deleteBtn.style.display = job.status === 'completed' || job.status === 'failed' ? 'inline-block' : 'none';
    viewResultsBtn.style.display = job.status === 'completed' ? 'inline-block' : 'none';
    
    deleteBtn.onclick = () => deleteJob(job.job_id);
    viewResultsBtn.onclick = () => viewJobResults(job);
    
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
}

async function deleteJob(jobId) {
    if (!confirm('Are you sure you want to delete this job?')) return;
    
    try {
        // TODO: Implement actual API call
        allJobs = allJobs.filter(job => job.job_id !== jobId);
        applyFilters();
        updateJobsOverview();
        
        const modal = bootstrap.Modal.getInstance(document.getElementById('job-modal'));
        modal.hide();
        
        showAlert('Job deleted successfully', 'success');
        
    } catch (error) {
        showAlert(`Failed to delete job: ${error.message}`, 'danger');
    }
}

function viewJobResults(job) {
    if (job.result && job.result.wiki_path) {
        window.open(job.result.wiki_path + '/index.html', '_blank');
    } else {
        window.location.href = '/search';
    }
}

async function clearCompletedJobs() {
    if (!confirm('Are you sure you want to clear all completed jobs?')) return;
    
    try {
        const completedJobs = allJobs.filter(job => job.status === 'completed');
        
        // TODO: Implement actual API calls to delete jobs
        allJobs = allJobs.filter(job => job.status !== 'completed');
        
        applyFilters();
        updateJobsOverview();
        
        showAlert(`Cleared ${completedJobs.length} completed jobs`, 'success');
        
    } catch (error) {
        showAlert(`Failed to clear completed jobs: ${error.message}`, 'danger');
    }
}

function showLoading(show) {
    const loading = document.getElementById('jobs-loading');
    loading.style.display = show ? 'block' : 'none';
}

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
});
</script>
{% endblock %}
