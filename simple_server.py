#!/usr/bin/env python3
"""Simple server for Knowledge Graph Builder when full dependencies are not available."""

import sys
import os
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

try:
    import uvicorn
    from fastapi import FastAP<PERSON>, Request
    from fastapi.responses import HTMLResponse
    from fastapi.staticfiles import StaticFiles
    from fastapi.templating import Jinja2Templates
except ImportError as e:
    print(f"❌ Missing web dependencies: {e}")
    print("Please run: python quick_fix.py")
    sys.exit(1)

# Create simple FastAPI app
app = FastAPI(title="Knowledge Graph Builder", version="0.1.0")

# Try to set up templates
try:
    templates = Jinja2Templates(directory="web/templates")
except Exception:
    templates = None

@app.get("/", response_class=HTMLResponse)
async def index(request: Request):
    """Simple index page."""
    if templates:
        try:
            return templates.TemplateResponse("index.html", {"request": request})
        except Exception:
            pass
    
    # Fallback HTML
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Knowledge Graph Builder</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .container { max-width: 800px; margin: 0 auto; }
            .alert { padding: 15px; margin: 20px 0; border-radius: 5px; }
            .alert-info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
            .alert-warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🚀 Knowledge Graph Builder</h1>
            <div class="alert alert-info">
                <strong>Welcome!</strong> This is a simplified version of the Knowledge Graph Builder.
            </div>
            <div class="alert alert-warning">
                <strong>Setup Required:</strong> Some dependencies are missing. Please run the setup:
                <br><br>
                <code>python quick_fix.py</code>
                <br><br>
                Then restart with: <code>python main.py serve</code>
            </div>
            <h2>Features</h2>
            <ul>
                <li>📄 Multi-format document processing (TXT, DOCX, PDF)</li>
                <li>🤖 LLM-powered entity and relation extraction</li>
                <li>🔗 Knowledge graph construction</li>
                <li>🌐 Interactive web interface</li>
                <li>🔍 Entity search and visualization</li>
            </ul>
            <h2>Quick Start</h2>
            <ol>
                <li>Run <code>python quick_fix.py</code> to install dependencies</li>
                <li>Edit <code>.env</code> file with your LLM API configuration</li>
                <li>Start the full application: <code>python main.py serve</code></li>
                <li>Upload documents and build your knowledge graph!</li>
            </ol>
        </div>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

@app.get("/health")
async def health():
    """Health check endpoint."""
    return {"status": "ok", "message": "Simple server running"}

@app.get("/api/status")
async def status():
    """Status endpoint."""
    return {
        "server": "simple",
        "dependencies_complete": False,
        "message": "Please run setup to enable full functionality"
    }

def main():
    """Run the simple server."""
    print("🌐 Starting simple Knowledge Graph Builder server...")
    print("📝 Note: This is a simplified version. Run 'python quick_fix.py' for full functionality.")
    print("🔗 Open http://localhost:8000 in your browser")
    
    try:
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 Server stopped")
    except Exception as e:
        print(f"❌ Server error: {e}")

if __name__ == "__main__":
    main()
