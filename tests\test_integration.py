"""Integration tests for the complete knowledge graph building pipeline."""

import pytest
import asyncio
import tempfile
import json
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock

from flows.main_flow import MainFlow
from config.settings import Settings
from utils.graph_ops import GraphOperations


@pytest.fixture
def temp_documents():
    """Create temporary documents for testing."""
    documents = []
    
    # Create sample text files
    texts = [
        "<PERSON> is a software engineer at Acme Corporation. He specializes in machine learning and artificial intelligence.",
        "<PERSON> works as a data scientist at Tech Innovations Inc. She has expertise in natural language processing.",
        "Acme Corporation is a technology company founded in 2010. It focuses on AI and machine learning solutions."
    ]
    
    for i, text in enumerate(texts):
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(text)
            documents.append(f.name)
    
    yield documents
    
    # Cleanup
    for doc_path in documents:
        Path(doc_path).unlink()


@pytest.fixture
def temp_output_dir():
    """Create temporary output directory."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield temp_dir


@pytest.fixture
def mock_llm_client():
    """Mock LLM client for testing."""
    with patch('utils.llm_client.LLMClient') as mock_client_class:
        mock_client = Mock()
        mock_client_class.return_value.__aenter__.return_value = mock_client
        
        # Mock entity and relation extraction
        mock_client.extract_entities_and_relations = AsyncMock(return_value={
            "entities": [
                {"name": "John Doe", "type": "person", "description": "Software engineer"},
                {"name": "Acme Corporation", "type": "organization", "description": "Technology company"}
            ],
            "relations": [
                {"source": "John Doe", "target": "Acme Corporation", "relation": "works_at", "description": "Employment"}
            ]
        })
        
        # Mock entity description enhancement
        mock_client.enhance_entity_description = AsyncMock(return_value="Enhanced description")
        
        # Mock general LLM calls
        mock_response = Mock()
        mock_response.content = "tag1, tag2, tag3"
        mock_client.call_llm = AsyncMock(return_value=mock_response)
        
        yield mock_client


class TestMainFlowIntegration:
    """Integration tests for the main flow."""
    
    @pytest.mark.asyncio
    async def test_complete_pipeline_merge_mode(self, temp_documents, temp_output_dir, mock_llm_client):
        """Test complete pipeline in merge mode."""
        # Setup shared data
        shared = {
            "context": {
                "input_path": str(Path(temp_documents[0]).parent),
                "output_path": temp_output_dir,
                "processing_mode": "merge",
                "output_formats": ["html", "json"],
                "supported_formats": ["txt", "docx", "pdf"],
                "model_router": lambda x: "qwen2.5-32b-instruct-int4"
            },
            "documents": [],
            "knowledge_graph": {
                "nodes": {},
                "edges": [],
                "index_ready": False
            },
            "outputs": {
                "wiki_pages": {},
                "json_graph": "",
                "search_index": ""
            },
            "task_state": {
                "completed_files": set(),
                "failed_segments": {},
                "retry_count": 0
            }
        }
        
        # Run main flow
        main_flow = MainFlow()
        
        with patch('utils.security_filters.SecurityFilters.filter_content') as mock_filter:
            mock_filter.return_value = {
                "is_safe": True,
                "filtered_text": "Test content",
                "warnings": [],
                "pii_detection": Mock(has_pii=False)
            }
            
            result = await main_flow.run_async(shared)
        
        # Verify results
        assert "execution_summary" in shared
        summary = shared["execution_summary"]
        
        assert summary["success"] is True
        assert summary["documents_processed"] > 0
        assert summary["entities_created"] >= 0
        assert summary["relations_created"] >= 0
        assert summary["execution_time"] > 0
    
    @pytest.mark.asyncio
    async def test_complete_pipeline_batch_mode(self, temp_documents, temp_output_dir, mock_llm_client):
        """Test complete pipeline in batch mode."""
        # Setup shared data for batch mode
        shared = {
            "context": {
                "input_path": temp_documents[0],  # Single file
                "output_path": temp_output_dir,
                "processing_mode": "batch",
                "output_formats": ["html"],
                "supported_formats": ["txt"],
                "model_router": lambda x: "qwen2.5-32b-instruct-int4"
            },
            "documents": [],
            "knowledge_graph": {"nodes": {}, "edges": [], "index_ready": False},
            "outputs": {"wiki_pages": {}, "json_graph": "", "search_index": ""},
            "task_state": {"completed_files": set(), "failed_segments": {}, "retry_count": 0}
        }
        
        # Run main flow
        main_flow = MainFlow()
        
        with patch('utils.security_filters.SecurityFilters.filter_content') as mock_filter:
            mock_filter.return_value = {
                "is_safe": True,
                "filtered_text": "Test content",
                "warnings": [],
                "pii_detection": Mock(has_pii=False)
            }
            
            result = await main_flow.run_async(shared)
        
        # Verify batch mode results
        assert "individual_graphs" in shared or "knowledge_graph" in shared
        assert "execution_summary" in shared
    
    @pytest.mark.asyncio
    async def test_pipeline_with_errors(self, temp_documents, temp_output_dir):
        """Test pipeline behavior with errors."""
        shared = {
            "context": {
                "input_path": temp_documents[0],
                "output_path": temp_output_dir,
                "processing_mode": "merge",
                "output_formats": ["html"],
                "supported_formats": ["txt"],
                "model_router": lambda x: "qwen2.5-32b-instruct-int4"
            },
            "documents": [],
            "knowledge_graph": {"nodes": {}, "edges": [], "index_ready": False},
            "outputs": {"wiki_pages": {}, "json_graph": "", "search_index": ""},
            "task_state": {"completed_files": set(), "failed_segments": {}, "retry_count": 0}
        }
        
        # Mock LLM client to raise errors
        with patch('utils.llm_client.LLMClient') as mock_client_class:
            mock_client = Mock()
            mock_client_class.return_value.__aenter__.return_value = mock_client
            mock_client.extract_entities_and_relations = AsyncMock(
                side_effect=Exception("LLM API error")
            )
            
            with patch('utils.security_filters.SecurityFilters.filter_content') as mock_filter:
                mock_filter.return_value = {
                    "is_safe": True,
                    "filtered_text": "Test content",
                    "warnings": [],
                    "pii_detection": Mock(has_pii=False)
                }
                
                # Should handle errors gracefully
                main_flow = MainFlow()
                result = await main_flow.run_async(shared)
        
        # Should complete even with errors
        assert "execution_summary" in shared
        
        # Check for failed segments
        task_state = shared.get("task_state", {})
        failed_segments = task_state.get("failed_segments", {})
        # May have failed segments due to LLM errors


class TestEndToEndScenarios:
    """End-to-end scenario tests."""
    
    @pytest.mark.asyncio
    async def test_single_document_processing(self, temp_output_dir, mock_llm_client):
        """Test processing a single document end-to-end."""
        # Create a single document
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("""
            Dr. Alice Johnson is a renowned researcher at Stanford University. 
            She specializes in artificial intelligence and machine learning.
            Her recent work on neural networks has been published in Nature.
            She collaborates with Prof. Bob Smith from MIT on quantum computing projects.
            """)
            doc_path = f.name
        
        try:
            shared = {
                "context": {
                    "input_path": doc_path,
                    "output_path": temp_output_dir,
                    "processing_mode": "merge",
                    "output_formats": ["html", "json"],
                    "supported_formats": ["txt"],
                    "model_router": lambda x: "qwen2.5-32b-instruct-int4"
                },
                "documents": [],
                "knowledge_graph": {"nodes": {}, "edges": [], "index_ready": False},
                "outputs": {"wiki_pages": {}, "json_graph": "", "search_index": ""},
                "task_state": {"completed_files": set(), "failed_segments": {}, "retry_count": 0}
            }
            
            # Enhanced mock for this scenario
            mock_llm_client.extract_entities_and_relations.return_value = {
                "entities": [
                    {"name": "Alice Johnson", "type": "person", "description": "Researcher at Stanford"},
                    {"name": "Stanford University", "type": "organization", "description": "University"},
                    {"name": "Bob Smith", "type": "person", "description": "Professor at MIT"},
                    {"name": "MIT", "type": "organization", "description": "University"},
                    {"name": "artificial intelligence", "type": "concept", "description": "AI field"},
                    {"name": "Nature", "type": "organization", "description": "Scientific journal"}
                ],
                "relations": [
                    {"source": "Alice Johnson", "target": "Stanford University", "relation": "works_at"},
                    {"source": "Bob Smith", "target": "MIT", "relation": "works_at"},
                    {"source": "Alice Johnson", "target": "Bob Smith", "relation": "collaborates_with"},
                    {"source": "Alice Johnson", "target": "artificial intelligence", "relation": "specializes_in"}
                ]
            }
            
            with patch('utils.security_filters.SecurityFilters.filter_content') as mock_filter:
                mock_filter.return_value = {
                    "is_safe": True,
                    "filtered_text": f.read() if hasattr(f, 'read') else "Test content",
                    "warnings": [],
                    "pii_detection": Mock(has_pii=False)
                }
                
                main_flow = MainFlow()
                result = await main_flow.run_async(shared)
            
            # Verify comprehensive results
            assert "execution_summary" in shared
            summary = shared["execution_summary"]
            
            assert summary["documents_processed"] == 1
            assert summary["entities_created"] >= 4  # Should have multiple entities
            assert summary["relations_created"] >= 3  # Should have multiple relations
            
            # Check knowledge graph structure
            kg = shared.get("knowledge_graph", {})
            assert len(kg.get("nodes", {})) >= 4
            assert len(kg.get("edges", [])) >= 3
            
        finally:
            Path(doc_path).unlink()
    
    @pytest.mark.asyncio
    async def test_multiple_documents_processing(self, temp_documents, temp_output_dir, mock_llm_client):
        """Test processing multiple documents with entity merging."""
        # Use the temp_documents fixture which has 3 documents
        shared = {
            "context": {
                "input_path": str(Path(temp_documents[0]).parent),
                "output_path": temp_output_dir,
                "processing_mode": "merge",
                "output_formats": ["html", "json"],
                "supported_formats": ["txt"],
                "model_router": lambda x: "qwen2.5-32b-instruct-int4"
            },
            "documents": [],
            "knowledge_graph": {"nodes": {}, "edges": [], "index_ready": False},
            "outputs": {"wiki_pages": {}, "json_graph": "", "search_index": ""},
            "task_state": {"completed_files": set(), "failed_segments": {}, "retry_count": 0}
        }
        
        # Mock different responses for different documents
        responses = [
            {
                "entities": [
                    {"name": "John Doe", "type": "person", "description": "Software engineer"},
                    {"name": "Acme Corporation", "type": "organization", "description": "Tech company"}
                ],
                "relations": [
                    {"source": "John Doe", "target": "Acme Corporation", "relation": "works_at"}
                ]
            },
            {
                "entities": [
                    {"name": "Jane Smith", "type": "person", "description": "Data scientist"},
                    {"name": "Tech Innovations Inc", "type": "organization", "description": "Tech company"}
                ],
                "relations": [
                    {"source": "Jane Smith", "target": "Tech Innovations Inc", "relation": "works_at"}
                ]
            },
            {
                "entities": [
                    {"name": "Acme Corporation", "type": "organization", "description": "Technology company"},  # Duplicate
                    {"name": "AI Solutions", "type": "concept", "description": "AI services"}
                ],
                "relations": [
                    {"source": "Acme Corporation", "target": "AI Solutions", "relation": "provides"}
                ]
            }
        ]
        
        mock_llm_client.extract_entities_and_relations.side_effect = responses
        
        with patch('utils.security_filters.SecurityFilters.filter_content') as mock_filter:
            mock_filter.return_value = {
                "is_safe": True,
                "filtered_text": "Test content",
                "warnings": [],
                "pii_detection": Mock(has_pii=False)
            }
            
            main_flow = MainFlow()
            result = await main_flow.run_async(shared)
        
        # Verify entity merging occurred
        summary = shared["execution_summary"]
        assert summary["documents_processed"] >= 3
        
        # Should have merged duplicate "Acme Corporation" entities
        kg = shared.get("knowledge_graph", {})
        entity_names = [node_data.get("canonical_name", "") for node_data in kg.get("nodes", {}).values()]
        acme_count = sum(1 for name in entity_names if "Acme" in name)
        assert acme_count == 1  # Should be merged into one entity
    
    @pytest.mark.asyncio
    async def test_output_generation(self, temp_documents, temp_output_dir, mock_llm_client):
        """Test that all requested outputs are generated."""
        shared = {
            "context": {
                "input_path": temp_documents[0],
                "output_path": temp_output_dir,
                "processing_mode": "merge",
                "output_formats": ["html", "json", "rdf"],
                "supported_formats": ["txt"],
                "model_router": lambda x: "qwen2.5-32b-instruct-int4"
            },
            "documents": [],
            "knowledge_graph": {"nodes": {}, "edges": [], "index_ready": False},
            "outputs": {"wiki_pages": {}, "json_graph": "", "search_index": ""},
            "task_state": {"completed_files": set(), "failed_segments": {}, "retry_count": 0}
        }
        
        with patch('utils.security_filters.SecurityFilters.filter_content') as mock_filter:
            mock_filter.return_value = {
                "is_safe": True,
                "filtered_text": "Test content",
                "warnings": [],
                "pii_detection": Mock(has_pii=False)
            }
            
            # Mock output generation
            with patch('utils.wiki_renderer.WikiRenderer.generate_wiki_site') as mock_wiki, \
                 patch('utils.index_connector.IndexConnector.export_to_json') as mock_json, \
                 patch('utils.index_connector.IndexConnector.export_to_rdf') as mock_rdf:
                
                mock_wiki.return_value = {"index": "/path/to/index.html"}
                mock_json.return_value = '{"nodes": [], "edges": []}'
                mock_rdf.return_value = '@prefix : <http://example.org/> .'
                
                main_flow = MainFlow()
                result = await main_flow.run_async(shared)
        
        # Verify outputs were generated
        outputs = shared.get("outputs", {})
        
        # Should have attempted to generate all requested formats
        # (Actual generation depends on mocked components)
        assert "wiki_pages" in outputs
        assert "json_graph" in outputs


class TestPerformanceAndScaling:
    """Performance and scaling tests."""
    
    @pytest.mark.asyncio
    async def test_large_document_processing(self, temp_output_dir, mock_llm_client):
        """Test processing a large document."""
        # Create a large document
        large_content = "This is a sentence about entity processing. " * 1000
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(large_content)
            doc_path = f.name
        
        try:
            shared = {
                "context": {
                    "input_path": doc_path,
                    "output_path": temp_output_dir,
                    "processing_mode": "merge",
                    "output_formats": ["json"],
                    "supported_formats": ["txt"],
                    "model_router": lambda x: "qwen2.5-32b-instruct-int4"
                },
                "documents": [],
                "knowledge_graph": {"nodes": {}, "edges": [], "index_ready": False},
                "outputs": {"wiki_pages": {}, "json_graph": "", "search_index": ""},
                "task_state": {"completed_files": set(), "failed_segments": {}, "retry_count": 0}
            }
            
            with patch('utils.security_filters.SecurityFilters.filter_content') as mock_filter:
                mock_filter.return_value = {
                    "is_safe": True,
                    "filtered_text": large_content,
                    "warnings": [],
                    "pii_detection": Mock(has_pii=False)
                }
                
                main_flow = MainFlow()
                result = await main_flow.run_async(shared)
            
            # Should complete successfully even with large content
            assert "execution_summary" in shared
            summary = shared["execution_summary"]
            assert summary["success"] is True
            
            # Should have created multiple segments
            documents = shared.get("documents", [])
            if documents:
                segments = documents[0].get("content_segments", [])
                assert len(segments) > 1  # Large content should be split
            
        finally:
            Path(doc_path).unlink()
    
    @pytest.mark.asyncio
    async def test_concurrent_processing_simulation(self, temp_documents, temp_output_dir, mock_llm_client):
        """Test simulation of concurrent processing."""
        # This test simulates what would happen with multiple concurrent segments
        shared = {
            "context": {
                "input_path": str(Path(temp_documents[0]).parent),
                "output_path": temp_output_dir,
                "processing_mode": "merge",
                "output_formats": ["json"],
                "supported_formats": ["txt"],
                "model_router": lambda x: "qwen2.5-32b-instruct-int4"
            },
            "documents": [],
            "knowledge_graph": {"nodes": {}, "edges": [], "index_ready": False},
            "outputs": {"wiki_pages": {}, "json_graph": "", "search_index": ""},
            "task_state": {"completed_files": set(), "failed_segments": {}, "retry_count": 0}
        }
        
        # Mock multiple async calls
        call_count = 0
        async def mock_extract(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            # Simulate some processing time
            await asyncio.sleep(0.01)
            return {
                "entities": [{"name": f"Entity_{call_count}", "type": "concept", "description": "Test entity"}],
                "relations": []
            }
        
        mock_llm_client.extract_entities_and_relations.side_effect = mock_extract
        
        with patch('utils.security_filters.SecurityFilters.filter_content') as mock_filter:
            mock_filter.return_value = {
                "is_safe": True,
                "filtered_text": "Test content",
                "warnings": [],
                "pii_detection": Mock(has_pii=False)
            }
            
            main_flow = MainFlow()
            result = await main_flow.run_async(shared)
        
        # Should handle concurrent processing
        assert "execution_summary" in shared
        assert call_count > 0  # Should have made LLM calls
