#!/usr/bin/env python3
"""测试上传功能的完整流程"""

import requests
import sys
import os

def test_upload_functionality():
    """测试完整的上传和处理功能"""
    print("🔍 测试上传功能完整流程...")
    
    base_url = "http://localhost:8001"
    
    # 1. 测试上传页面
    print("\n1. 测试上传页面访问...")
    try:
        response = requests.get(f"{base_url}/upload", timeout=10)
        if response.status_code == 200:
            print("✅ 上传页面访问正常")
        else:
            print(f"❌ 上传页面访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 上传页面访问异常: {e}")
        return False
    
    # 2. 测试文件上传和处理
    print("\n2. 测试文件上传和处理...")
    
    # 检查测试文件是否存在
    test_file = "test_documents/中文示例.txt"
    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        return False
    
    try:
        # 准备文件上传
        with open(test_file, 'rb') as f:
            files = {'files': (os.path.basename(test_file), f, 'text/plain')}
            data = {'mode': 'merge'}
            
            print(f"📤 上传文件: {test_file}")
            response = requests.post(f"{base_url}/api/process", files=files, data=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 文件处理成功")
                
                # 检查处理结果
                if 'processing_stats' in result:
                    stats = result['processing_stats']
                    print(f"   📊 处理统计:")
                    print(f"      - 处理文档数: {stats.get('documents_processed', 0)}")
                    print(f"      - 提取实体数: {stats.get('entities_extracted', 0)}")
                    print(f"      - 发现关系数: {stats.get('relations_extracted', 0)}")
                    print(f"      - 处理时间: {stats.get('processing_time', 0):.2f}秒")
                
                if 'statistics' in result:
                    graph_stats = result['statistics']
                    print(f"   📈 图谱统计:")
                    print(f"      - 总实体数: {graph_stats.get('total_entities', 0)}")
                    print(f"      - 总关系数: {graph_stats.get('total_relations', 0)}")
                    
                    if 'entity_types' in graph_stats:
                        print(f"      - 实体类型分布:")
                        for entity_type, count in graph_stats['entity_types'].items():
                            type_names = {
                                'person': '人员',
                                'organization': '组织',
                                'location': '地点',
                                'concept': '概念',
                                'event': '事件',
                                'product': '产品'
                            }
                            print(f"        * {type_names.get(entity_type, entity_type)}: {count}")
                
                return True
            else:
                error_detail = response.json().get('detail', '未知错误') if response.headers.get('content-type', '').startswith('application/json') else response.text
                print(f"❌ 文件处理失败: {error_detail}")
                return False
                
    except Exception as e:
        print(f"❌ 文件上传异常: {e}")
        return False

def test_search_functionality():
    """测试搜索功能"""
    print("\n3. 测试搜索功能...")
    
    base_url = "http://localhost:8001"
    
    try:
        # 测试搜索
        search_data = {"query": "张三", "entity_type": ""}
        response = requests.post(f"{base_url}/api/search", json=search_data, timeout=10)
        
        if response.status_code == 200:
            results = response.json()
            print(f"✅ 搜索功能正常，找到 {len(results)} 个结果")
            
            if results:
                print("   🔍 搜索结果:")
                for i, result in enumerate(results[:3], 1):  # 只显示前3个结果
                    entity = result.get('entity', {})
                    score = result.get('score', 0)
                    print(f"      {i}. {entity.get('name', '未知')} (类型: {entity.get('type', '未知')}, 相关度: {score:.2f})")
            
            return True
        else:
            error_detail = response.json().get('detail', '未知错误') if response.headers.get('content-type', '').startswith('application/json') else response.text
            print(f"❌ 搜索功能失败: {error_detail}")
            return False
            
    except Exception as e:
        print(f"❌ 搜索功能异常: {e}")
        return False

def test_statistics_api():
    """测试统计 API"""
    print("\n4. 测试统计 API...")
    
    base_url = "http://localhost:8001"
    
    try:
        response = requests.get(f"{base_url}/api/statistics", timeout=10)
        
        if response.status_code == 200:
            stats = response.json()
            print("✅ 统计 API 正常")
            print(f"   📊 当前图谱统计:")
            print(f"      - 总实体数: {stats.get('total_entities', 0)}")
            print(f"      - 总关系数: {stats.get('total_relations', 0)}")
            return True
        else:
            print(f"❌ 统计 API 失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 统计 API 异常: {e}")
        return False

def main():
    """主函数"""
    print("🧪 知识图谱构建器 - 上传功能完整测试")
    print("=" * 60)
    
    tests = [
        test_upload_functionality,
        test_search_functionality,
        test_statistics_api
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有功能测试通过！上传按钮功能完全正常！")
        return 0
    else:
        print("⚠️ 部分功能存在问题")
        return 1

if __name__ == "__main__":
    sys.exit(main())
