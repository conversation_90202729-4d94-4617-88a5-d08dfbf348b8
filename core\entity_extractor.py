"""Entity extraction module using LLM."""

import asyncio
import json
import re
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

from loguru import logger


@dataclass
class Entity:
    """Entity data class."""
    name: str
    type: str
    description: str = ""
    confidence: float = 1.0


@dataclass
class Relation:
    """Relation data class."""
    source: str
    target: str
    relation_type: str
    description: str = ""
    confidence: float = 1.0


class EntityExtractor:
    """Extract entities and relations from text using LLM."""
    
    def __init__(self, llm_client=None):
        self.llm_client = llm_client
        self.entity_types = [
            "person", "organization", "location", 
            "concept", "event", "product", "technology"
        ]
    
    async def extract_from_text(self, text: str) -> Dict[str, Any]:
        """Extract entities and relations from text."""
        if not self.llm_client:
            # Fallback to simple pattern-based extraction
            return self._simple_extraction(text)
        
        try:
            # Use LLM for extraction
            return await self._llm_extraction(text)
        except Exception as e:
            logger.warning(f"LLM extraction failed, using fallback: {e}")
            return self._simple_extraction(text)
    
    def _simple_extraction(self, text: str) -> Dict[str, Any]:
        """Simple pattern-based entity extraction as fallback."""
        entities = []
        relations = []
        
        # Simple patterns for demonstration
        # In practice, you'd use more sophisticated NLP
        
        # Extract potential person names (capitalized words)
        person_pattern = r'\b[A-Z][a-z]+ [A-Z][a-z]+\b'
        persons = re.findall(person_pattern, text)
        
        for person in set(persons):
            entities.append(Entity(
                name=person,
                type="person",
                description=f"Person mentioned in text",
                confidence=0.7
            ))
        
        # Extract potential organizations (words ending with Corp, Inc, etc.)
        org_pattern = r'\b[A-Z][a-zA-Z\s]+(Corp|Inc|Company|Organization|University)\b'
        orgs = re.findall(org_pattern, text)
        
        for org in set(orgs):
            entities.append(Entity(
                name=org,
                type="organization", 
                description=f"Organization mentioned in text",
                confidence=0.6
            ))
        
        # Simple relation extraction (person works at organization)
        for person in persons:
            for org in orgs:
                if person in text and org in text:
                    # Check if they appear close to each other
                    person_pos = text.find(person)
                    org_pos = text.find(org)
                    if abs(person_pos - org_pos) < 200:  # Within 200 characters
                        relations.append(Relation(
                            source=person,
                            target=org,
                            relation_type="works_at",
                            description="Inferred from text proximity",
                            confidence=0.5
                        ))
        
        return {
            "entities": [self._entity_to_dict(e) for e in entities],
            "relations": [self._relation_to_dict(r) for r in relations]
        }
    
    async def _llm_extraction(self, text: str) -> Dict[str, Any]:
        """Extract entities using LLM."""
        prompt = self._create_extraction_prompt(text)
        
        try:
            response = await self.llm_client.generate(prompt)
            return self._parse_llm_response(response)
        except Exception as e:
            logger.error(f"LLM extraction failed: {e}")
            raise
    
    def _create_extraction_prompt(self, text: str) -> str:
        """Create prompt for LLM entity extraction."""
        return f"""
Extract entities and relationships from the following text. Return the result as JSON.

Text: {text}

Please identify:
1. Entities with their types (person, organization, location, concept, event, product, technology)
2. Relationships between entities

Return format:
{{
    "entities": [
        {{"name": "Entity Name", "type": "entity_type", "description": "brief description"}}
    ],
    "relations": [
        {{"source": "Entity1", "target": "Entity2", "relation_type": "relationship", "description": "brief description"}}
    ]
}}

JSON:
"""
    
    def _parse_llm_response(self, response: str) -> Dict[str, Any]:
        """Parse LLM response to extract entities and relations."""
        try:
            # Try to extract JSON from response
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                data = json.loads(json_str)
                return data
            else:
                logger.warning("No JSON found in LLM response")
                return {"entities": [], "relations": []}
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse LLM response as JSON: {e}")
            return {"entities": [], "relations": []}
    
    def _entity_to_dict(self, entity: Entity) -> Dict[str, Any]:
        """Convert Entity to dictionary."""
        return {
            "name": entity.name,
            "type": entity.type,
            "description": entity.description,
            "confidence": entity.confidence
        }
    
    def _relation_to_dict(self, relation: Relation) -> Dict[str, Any]:
        """Convert Relation to dictionary."""
        return {
            "source": relation.source,
            "target": relation.target,
            "relation_type": relation.relation_type,
            "description": relation.description,
            "confidence": relation.confidence
        }
    
    async def process_segments(self, segments: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Process multiple text segments."""
        tasks = []
        for segment in segments:
            task = self.extract_from_text(segment["text"])
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        processed_segments = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Failed to process segment {i}: {result}")
                processed_segments.append({
                    **segments[i],
                    "entities": [],
                    "relations": []
                })
            else:
                processed_segments.append({
                    **segments[i],
                    **result
                })
        
        return processed_segments
