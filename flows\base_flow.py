"""
Base flow classes for the knowledge graph builder
Provides the foundation for async flow processing
"""

import asyncio
import time
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Callable, Union
from dataclasses import dataclass
from enum import Enum
from loguru import logger


class FlowStatus(Enum):
    """Flow execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class FlowResult:
    """Flow execution result"""
    status: FlowStatus
    result: Any = None
    error: Optional[Exception] = None
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
    
    @property
    def duration(self) -> Optional[float]:
        """Get flow duration in seconds"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return None
    
    @property
    def is_success(self) -> bool:
        """Check if flow completed successfully"""
        return self.status == FlowStatus.COMPLETED


class BaseFlow(ABC):
    """Base class for all flows"""
    
    def __init__(self, shared_context: Dict[str, Any], 
                 flow_id: Optional[str] = None,
                 timeout: Optional[float] = None):
        self.shared_context = shared_context
        self.flow_id = flow_id or f"{self.__class__.__name__}_{id(self)}"
        self.timeout = timeout
        
        # Flow state
        self.status = FlowStatus.PENDING
        self.result = None
        self.error = None
        self.start_time = None
        self.end_time = None
        
        # Child flows
        self.child_flows: List['BaseFlow'] = []
        
        # Cancellation
        self._cancelled = False
        self._cancel_event = asyncio.Event()
    
    @abstractmethod
    async def execute(self) -> Any:
        """Execute the flow logic"""
        pass
    
    async def run(self) -> FlowResult:
        """Run the flow with error handling and timing"""
        
        logger.info(f"Starting flow: {self.flow_id}")
        
        self.status = FlowStatus.RUNNING
        self.start_time = time.time()
        
        try:
            if self.timeout:
                self.result = await asyncio.wait_for(
                    self.execute(),
                    timeout=self.timeout
                )
            else:
                self.result = await self.execute()
            
            if self._cancelled:
                self.status = FlowStatus.CANCELLED
                logger.info(f"Flow cancelled: {self.flow_id}")
            else:
                self.status = FlowStatus.COMPLETED
                logger.info(f"Flow completed: {self.flow_id} in {self.duration:.2f}s")
            
        except asyncio.CancelledError:
            self.status = FlowStatus.CANCELLED
            logger.info(f"Flow cancelled: {self.flow_id}")
            
        except asyncio.TimeoutError:
            self.status = FlowStatus.FAILED
            self.error = TimeoutError(f"Flow {self.flow_id} timed out after {self.timeout}s")
            logger.error(f"Flow timeout: {self.flow_id}")
            
        except Exception as e:
            self.status = FlowStatus.FAILED
            self.error = e
            logger.error(f"Flow failed: {self.flow_id} - {e}")
            
        finally:
            self.end_time = time.time()
        
        return FlowResult(
            status=self.status,
            result=self.result,
            error=self.error,
            start_time=self.start_time,
            end_time=self.end_time
        )
    
    async def cancel(self):
        """Cancel the flow and all child flows"""
        self._cancelled = True
        self._cancel_event.set()
        
        # Cancel child flows
        for child_flow in self.child_flows:
            await child_flow.cancel()
        
        logger.info(f"Flow cancellation requested: {self.flow_id}")
    
    def is_cancelled(self) -> bool:
        """Check if flow is cancelled"""
        return self._cancelled
    
    @property
    def duration(self) -> Optional[float]:
        """Get flow duration"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return None


class SequentialFlow(BaseFlow):
    """Flow that executes child flows sequentially"""
    
    def __init__(self, shared_context: Dict[str, Any], 
                 flows: List[BaseFlow],
                 flow_id: Optional[str] = None,
                 timeout: Optional[float] = None,
                 stop_on_error: bool = True):
        super().__init__(shared_context, flow_id, timeout)
        self.flows = flows
        self.stop_on_error = stop_on_error
        self.child_flows = flows
    
    async def execute(self) -> List[FlowResult]:
        """Execute flows sequentially"""
        
        results = []
        
        for i, flow in enumerate(self.flows):
            if self.is_cancelled():
                break
            
            logger.debug(f"Executing flow {i+1}/{len(self.flows)}: {flow.flow_id}")
            
            result = await flow.run()
            results.append(result)
            
            if not result.is_success and self.stop_on_error:
                logger.error(f"Flow failed, stopping sequence: {flow.flow_id}")
                break
        
        return results


class ParallelFlow(BaseFlow):
    """Flow that executes child flows in parallel"""
    
    def __init__(self, shared_context: Dict[str, Any], 
                 flows: List[BaseFlow],
                 flow_id: Optional[str] = None,
                 timeout: Optional[float] = None,
                 max_concurrent: Optional[int] = None):
        super().__init__(shared_context, flow_id, timeout)
        self.flows = flows
        self.max_concurrent = max_concurrent
        self.child_flows = flows
    
    async def execute(self) -> List[FlowResult]:
        """Execute flows in parallel"""
        
        if self.max_concurrent:
            # Use semaphore to limit concurrency
            semaphore = asyncio.Semaphore(self.max_concurrent)
            
            async def run_with_semaphore(flow):
                async with semaphore:
                    return await flow.run()
            
            tasks = [run_with_semaphore(flow) for flow in self.flows]
        else:
            tasks = [flow.run() for flow in self.flows]
        
        # Wait for all tasks to complete
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Convert exceptions to failed results
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append(FlowResult(
                    status=FlowStatus.FAILED,
                    error=result
                ))
            else:
                processed_results.append(result)
        
        return processed_results


class BatchFlow(BaseFlow):
    """Flow that processes items in batches"""
    
    def __init__(self, shared_context: Dict[str, Any],
                 items: List[Any],
                 batch_processor: Callable,
                 batch_size: int = 10,
                 flow_id: Optional[str] = None,
                 timeout: Optional[float] = None,
                 max_concurrent: Optional[int] = None):
        super().__init__(shared_context, flow_id, timeout)
        self.items = items
        self.batch_processor = batch_processor
        self.batch_size = batch_size
        self.max_concurrent = max_concurrent
    
    async def execute(self) -> List[Any]:
        """Execute batch processing"""
        
        # Split items into batches
        batches = [
            self.items[i:i + self.batch_size]
            for i in range(0, len(self.items), self.batch_size)
        ]
        
        logger.info(f"Processing {len(self.items)} items in {len(batches)} batches")
        
        # Process batches
        if self.max_concurrent:
            semaphore = asyncio.Semaphore(self.max_concurrent)
            
            async def process_batch_with_semaphore(batch):
                async with semaphore:
                    return await self.batch_processor(batch, self.shared_context)
            
            tasks = [process_batch_with_semaphore(batch) for batch in batches]
        else:
            tasks = [self.batch_processor(batch, self.shared_context) for batch in batches]
        
        # Wait for all batches to complete
        batch_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Flatten results
        all_results = []
        for batch_result in batch_results:
            if isinstance(batch_result, Exception):
                logger.error(f"Batch processing failed: {batch_result}")
                continue
            
            if isinstance(batch_result, list):
                all_results.extend(batch_result)
            else:
                all_results.append(batch_result)
        
        return all_results


class ConditionalFlow(BaseFlow):
    """Flow that executes different flows based on conditions"""
    
    def __init__(self, shared_context: Dict[str, Any],
                 condition: Callable[[Dict[str, Any]], bool],
                 true_flow: BaseFlow,
                 false_flow: Optional[BaseFlow] = None,
                 flow_id: Optional[str] = None,
                 timeout: Optional[float] = None):
        super().__init__(shared_context, flow_id, timeout)
        self.condition = condition
        self.true_flow = true_flow
        self.false_flow = false_flow
        
        # Add child flows
        self.child_flows = [true_flow]
        if false_flow:
            self.child_flows.append(false_flow)
    
    async def execute(self) -> FlowResult:
        """Execute conditional flow"""
        
        # Evaluate condition
        condition_result = self.condition(self.shared_context)
        
        if condition_result:
            logger.debug(f"Condition true, executing true flow: {self.true_flow.flow_id}")
            return await self.true_flow.run()
        elif self.false_flow:
            logger.debug(f"Condition false, executing false flow: {self.false_flow.flow_id}")
            return await self.false_flow.run()
        else:
            logger.debug("Condition false, no false flow defined")
            return FlowResult(status=FlowStatus.COMPLETED, result=None)


class RetryFlow(BaseFlow):
    """Flow that retries execution on failure"""
    
    def __init__(self, shared_context: Dict[str, Any],
                 target_flow: BaseFlow,
                 max_retries: int = 3,
                 retry_delay: float = 1.0,
                 exponential_backoff: bool = True,
                 flow_id: Optional[str] = None,
                 timeout: Optional[float] = None):
        super().__init__(shared_context, flow_id, timeout)
        self.target_flow = target_flow
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.exponential_backoff = exponential_backoff
        self.child_flows = [target_flow]
    
    async def execute(self) -> FlowResult:
        """Execute with retry logic"""
        
        last_result = None
        
        for attempt in range(self.max_retries + 1):
            if self.is_cancelled():
                break
            
            logger.debug(f"Attempt {attempt + 1}/{self.max_retries + 1} for flow: {self.target_flow.flow_id}")
            
            result = await self.target_flow.run()
            
            if result.is_success:
                return result
            
            last_result = result
            
            # Don't wait after the last attempt
            if attempt < self.max_retries:
                delay = self.retry_delay
                if self.exponential_backoff:
                    delay *= (2 ** attempt)
                
                logger.warning(f"Flow failed, retrying in {delay}s: {self.target_flow.flow_id}")
                await asyncio.sleep(delay)
        
        logger.error(f"Flow failed after {self.max_retries + 1} attempts: {self.target_flow.flow_id}")
        return last_result or FlowResult(status=FlowStatus.FAILED)
