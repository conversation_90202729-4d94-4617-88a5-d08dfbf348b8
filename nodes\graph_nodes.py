"""Graph building nodes for creating and managing knowledge graphs."""

from typing import Dict, List, Any, Optional

import networkx as nx
from pocketflow import Batch<PERSON><PERSON>, AsyncNode
from loguru import logger

from utils.graph_ops import GraphOperations, Entity, Relation
from config.settings import Settings


class BuildGraphBatch(BatchNode):
    """Create isolated knowledge graphs per document (Batch mode)."""
    
    def __init__(self):
        super().__init__()
        self.graph_ops = GraphOperations()
    
    def prep(self, shared: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Prepare documents for graph building.
        
        Args:
            shared: Shared data structure
            
        Returns:
            List of documents to process
        """
        documents = shared.get("documents", [])
        
        if not documents:
            raise ValueError("No documents found for graph building")
        
        return documents
    
    def exec(self, document: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create knowledge graph for a single document.
        
        Args:
            document: Document with entities and relations
            
        Returns:
            Document with knowledge graph
        """
        # Get entities and relations
        normalized_entities = document.get("normalized_entities", [])
        relations_data = document.get("relations", [])
        
        if not normalized_entities:
            logger.warning(f"No entities found for document {document['file_path']}")
            document["knowledge_graph"] = None
            return document
        
        # Convert to Entity objects
        entities = []
        for entity_data in normalized_entities:
            entity = Entity(
                canonical_name=entity_data["canonical_name"],
                entity_type=entity_data.get("entity_type", "unknown"),
                description=entity_data.get("description", ""),
                alternatives=entity_data.get("alternatives", []),
                tags=entity_data.get("tags", []),
                source_weight=entity_data.get("source_weight", 1),
                metadata=entity_data.get("metadata", {})
            )
            entities.append(entity)
        
        # Convert to Relation objects
        relations = []
        for relation_data in relations_data:
            relation = Relation(
                source=relation_data["source"],
                target=relation_data["target"],
                relation_type=relation_data["relation"],
                description=relation_data.get("description", ""),
                confidence=1.0,  # Default confidence
                source_weight=document.get("source_weight", 1),
                metadata={
                    "source_file": relation_data.get("source_file", document["file_path"]),
                    "source_segment": relation_data.get("source_segment", 0)
                }
            )
            relations.append(relation)
        
        # Create graph
        graph = self.graph_ops.create_graph_from_entities_relations(entities, relations)
        
        # Convert to dictionary format for storage
        graph_dict = self.graph_ops.export_to_dict(graph)
        
        document["knowledge_graph"] = graph_dict
        
        logger.info(f"Created graph for {document['file_path']}: "
                   f"{graph.number_of_nodes()} nodes, {graph.number_of_edges()} edges")
        
        return document
    
    def post(
        self,
        shared: Dict[str, Any],
        prep_res: List[Dict[str, Any]],
        exec_res: List[Dict[str, Any]]
    ) -> str:
        """
        Post-process graph building results.
        
        Args:
            shared: Shared data structure
            prep_res: Preparation results
            exec_res: Execution results
            
        Returns:
            Next action
        """
        # Update documents in shared data
        shared["documents"] = exec_res
        
        # Create list of individual graphs for batch mode
        individual_graphs = []
        for document in exec_res:
            if document.get("knowledge_graph"):
                individual_graphs.append({
                    "file_path": document["file_path"],
                    "graph": document["knowledge_graph"]
                })
        
        shared["individual_graphs"] = individual_graphs
        
        logger.info(f"Batch graph building completed: {len(individual_graphs)} graphs created")
        
        return "default"


class UpdateGlobalGraph(AsyncNode):
    """Merge document results into global knowledge graph (Merge mode)."""
    
    def __init__(self):
        super().__init__(max_retries=2, wait=5)
        self.graph_ops = GraphOperations()
        self.settings = Settings()
    
    async def prep_async(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare for global graph update.
        
        Args:
            shared: Shared data structure
            
        Returns:
            Preparation data
        """
        documents = shared.get("documents", [])
        
        if not documents:
            raise ValueError("No documents found for global graph update")
        
        # Get existing global graph if any
        existing_graph_dict = shared.get("knowledge_graph", {})
        existing_graph = None
        
        if existing_graph_dict.get("nodes"):
            existing_graph = self.graph_ops.import_from_dict(existing_graph_dict)
        
        return {
            "documents": documents,
            "existing_graph": existing_graph
        }
    
    async def exec_async(self, prep_res: Dict[str, Any]) -> nx.MultiDiGraph:
        """
        Execute global graph update.
        
        Args:
            prep_res: Preparation results
            
        Returns:
            Updated global graph
        """
        documents = prep_res["documents"]
        existing_graph = prep_res["existing_graph"]
        
        # Collect all entities and relations from all documents
        all_entities = []
        all_relations = []
        
        for document in documents:
            # Get entities
            normalized_entities = document.get("normalized_entities", [])
            for entity_data in normalized_entities:
                entity = Entity(
                    canonical_name=entity_data["canonical_name"],
                    entity_type=entity_data.get("entity_type", "unknown"),
                    description=entity_data.get("description", ""),
                    alternatives=entity_data.get("alternatives", []),
                    tags=entity_data.get("tags", []),
                    source_weight=entity_data.get("source_weight", 1),
                    metadata=entity_data.get("metadata", {})
                )
                all_entities.append(entity)
            
            # Get relations
            relations_data = document.get("relations", [])
            for relation_data in relations_data:
                relation = Relation(
                    source=relation_data["source"],
                    target=relation_data["target"],
                    relation_type=relation_data["relation"],
                    description=relation_data.get("description", ""),
                    confidence=1.0,
                    source_weight=document.get("source_weight", 1),
                    metadata={
                        "source_file": relation_data.get("source_file", document["file_path"]),
                        "source_segment": relation_data.get("source_segment", 0)
                    }
                )
                all_relations.append(relation)
        
        # Create new graph from all data
        new_graph = self.graph_ops.create_graph_from_entities_relations(all_entities, all_relations)
        
        # Merge with existing graph if present
        if existing_graph:
            merged_graph = self.graph_ops.merge_graphs(existing_graph, new_graph)
        else:
            merged_graph = new_graph
        
        logger.info(f"Global graph updated: {merged_graph.number_of_nodes()} nodes, "
                   f"{merged_graph.number_of_edges()} edges")
        
        return merged_graph
    
    async def post_async(
        self,
        shared: Dict[str, Any],
        prep_res: Dict[str, Any],
        exec_res: nx.MultiDiGraph
    ) -> str:
        """
        Post-process global graph update.
        
        Args:
            shared: Shared data structure
            prep_res: Preparation results
            exec_res: Execution results
            
        Returns:
            Next action
        """
        global_graph = exec_res
        
        # Convert to dictionary format and store
        graph_dict = self.graph_ops.export_to_dict(global_graph)
        shared["knowledge_graph"] = graph_dict
        shared["knowledge_graph"]["index_ready"] = True
        
        # Store the NetworkX graph object for further processing
        shared["_networkx_graph"] = global_graph
        
        logger.info("Global knowledge graph updated successfully")
        
        return "default"


class GraphModeSelector(AsyncNode):
    """Select between batch and merge mode for graph building."""
    
    def __init__(self):
        super().__init__()
        self.settings = Settings()
    
    async def prep_async(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare for mode selection.
        
        Args:
            shared: Shared data structure
            
        Returns:
            Preparation data
        """
        context = shared.get("context", {})
        processing_mode = context.get("processing_mode", self.settings.processing_mode)
        
        return {
            "processing_mode": processing_mode,
            "documents": shared.get("documents", [])
        }
    
    async def exec_async(self, prep_res: Dict[str, Any]) -> str:
        """
        Execute mode selection.
        
        Args:
            prep_res: Preparation results
            
        Returns:
            Selected mode
        """
        processing_mode = prep_res["processing_mode"]
        documents = prep_res["documents"]
        
        if processing_mode not in ["batch", "merge"]:
            logger.warning(f"Unknown processing mode '{processing_mode}', defaulting to 'merge'")
            processing_mode = "merge"
        
        logger.info(f"Selected graph building mode: {processing_mode}")
        
        return processing_mode
    
    async def post_async(
        self,
        shared: Dict[str, Any],
        prep_res: Dict[str, Any],
        exec_res: str
    ) -> str:
        """
        Post-process mode selection.
        
        Args:
            shared: Shared data structure
            prep_res: Preparation results
            exec_res: Execution results
            
        Returns:
            Next action based on selected mode
        """
        selected_mode = exec_res
        
        # Store selected mode in context
        context = shared.setdefault("context", {})
        context["selected_graph_mode"] = selected_mode
        
        # Return appropriate action
        if selected_mode == "batch":
            return "batch_mode"
        else:
            return "merge_mode"
