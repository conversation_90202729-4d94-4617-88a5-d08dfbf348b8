# Knowledge Graph Builder Configuration

# LLM Configuration
llm:
  base_url: "https://gateway.chat.sensedeal.vip/v1"
  api_key: "974fd8d1c155aa3d04b17bf253176b5e"
  models:
    primary: "qwen2.5-32b-instruct-int4"  # 本地模型，成本低
    secondary: "doubao-seed-1.6"          # 备用模型，性能好
    fallback: "gpt-3.5-turbo"             # 备用模型
  
  # Request settings
  max_tokens: 4000
  temperature: 0.1
  timeout: 300
  max_retries: 3
  retry_delay: 2

# Processing Configuration
processing:
  mode: "merge"  # "batch" or "merge"
  max_concurrent_files: 3
  max_concurrent_segments: 5
  chunk_size: 2000
  chunk_overlap: 200
  
  # Entity extraction
  entity_similarity_threshold: 0.85
  min_entity_confidence: 0.7
  max_entities_per_segment: 50

# File Processing
files:
  supported_formats: ["txt", "docx", "pdf"]
  max_file_size_mb: 100
  encoding: "utf-8"
  
  # Content filtering
  min_content_length: 100
  max_content_length: 1000000

# Graph Configuration
graph:
  node_id_strategy: "canonical_name"
  edge_weight_strategy: "frequency"
  max_nodes: 10000
  max_edges: 50000
  
  # Conflict resolution
  merge_strategy: "weighted_priority"
  source_weights:
    pdf: 3
    docx: 2
    txt: 1
    chat: 1

# Output Configuration
output:
  formats: ["json", "html", "rdf"]
  wiki:
    template_dir: "web/templates"
    static_dir: "web/static"
    output_dir: "output/wiki"
  
  # Search indexing
  search:
    enabled: true
    backend: "elasticsearch"  # "elasticsearch" or "cubeflow"
    index_name: "knowledge_graph"

# Storage Configuration
storage:
  neo4j:
    enabled: false
    uri: "bolt://localhost:7687"
    username: "neo4j"
    password: "password"
  
  elasticsearch:
    enabled: true
    hosts: ["localhost:9200"]
    index_settings:
      number_of_shards: 1
      number_of_replicas: 0

# Security Configuration
security:
  enable_pii_detection: true
  enable_xss_protection: true
  max_input_size: 10485760  # 10MB
  
  # PII patterns to detect
  pii_patterns:
    - "phone_number"
    - "email"
    - "id_card"
    - "credit_card"

# Logging Configuration
logging:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  file: "logs/app.log"
  rotation: "1 day"
  retention: "30 days"

# Development Configuration
development:
  debug: true
  hot_reload: true
  profiling: false
  
# Web Interface Configuration
web:
  host: "0.0.0.0"
  port: 8000
  cors_origins: ["*"]
  static_files: true
