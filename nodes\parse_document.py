"""
Document parsing node
Handles reading and validating input files of various formats
"""

from pathlib import Path
from typing import Dict, Any, Optional
from loguru import logger

from .base_node import AsyncNode
from utils.file_utils import FileProcessor


class ParseDocumentNode(AsyncNode):
    """Node for parsing documents of various formats"""
    
    def __init__(self, node_id: Optional[str] = None,
                 max_file_size_mb: int = 100,
                 encoding: str = 'utf-8',
                 max_retries: int = 3,
                 timeout: float = 300):
        super().__init__(
            node_id=node_id or "ParseDocument",
            max_retries=max_retries,
            timeout=timeout
        )
        
        self.file_processor = FileProcessor(
            max_file_size_mb=max_file_size_mb,
            encoding=encoding
        )
    
    def prep(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare file path for parsing"""
        
        # Get file path from shared context
        file_path = shared.get('current_file_path')
        
        if not file_path:
            raise ValueError("No file path provided in shared context")
        
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        logger.debug(f"Preparing to parse file: {file_path}")
        
        return {
            'file_path': file_path,
            'parser': self.file_processor
        }
    
    async def exec_async(self, prep_result: Dict[str, Any]) -> Dict[str, Any]:
        """Parse the document asynchronously"""
        
        file_path = prep_result['file_path']
        parser = prep_result['parser']
        
        logger.info(f"Parsing document: {file_path}")
        
        try:
            # Parse the file
            parsed_data = await parser.parse_file(file_path)
            
            # Validate content
            content = parsed_data['content']
            
            if not content or len(content.strip()) == 0:
                raise ValueError(f"Empty content in file: {file_path}")
            
            # Add file path to metadata
            parsed_data['metadata']['file_path'] = str(file_path)
            parsed_data['metadata']['file_name'] = file_path.name
            parsed_data['metadata']['file_stem'] = file_path.stem
            
            logger.info(f"Successfully parsed {file_path}: {len(content)} characters")
            
            return parsed_data
            
        except Exception as e:
            logger.error(f"Failed to parse document {file_path}: {e}")
            raise
    
    def post(self, shared: Dict[str, Any], prep_result: Dict[str, Any], 
             exec_result: Dict[str, Any]) -> str:
        """Store parsed content in shared context"""
        
        # Store parsed content
        shared['parsed_content'] = exec_result['content']
        shared['content_metadata'] = exec_result['metadata']
        
        # Determine next step based on content length
        content_length = len(exec_result['content'])
        
        if content_length > 5000:  # Large content needs splitting
            return "split_content"
        else:
            return "process_segments"
    
    def exec_fallback(self, prep_result: Dict[str, Any], exception: Exception) -> Dict[str, Any]:
        """Fallback when parsing fails"""
        
        file_path = prep_result['file_path']
        
        logger.warning(f"Using fallback for {file_path}: {exception}")
        
        # Try to read as plain text
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            if content:
                return {
                    'content': content,
                    'metadata': {
                        'file_type': 'txt',
                        'file_path': str(file_path),
                        'file_name': file_path.name,
                        'file_stem': file_path.stem,
                        'fallback_used': True,
                        'original_error': str(exception)
                    }
                }
        except Exception as fallback_error:
            logger.error(f"Fallback also failed for {file_path}: {fallback_error}")
        
        # Return empty content as last resort
        return {
            'content': '',
            'metadata': {
                'file_type': 'unknown',
                'file_path': str(file_path),
                'file_name': file_path.name,
                'file_stem': file_path.stem,
                'parsing_failed': True,
                'error': str(exception)
            }
        }


class ValidateDocumentNode(AsyncNode):
    """Node for validating parsed document content"""
    
    def __init__(self, node_id: Optional[str] = None,
                 min_content_length: int = 100,
                 max_content_length: int = 1000000):
        super().__init__(node_id=node_id or "ValidateDocument")
        self.min_content_length = min_content_length
        self.max_content_length = max_content_length
    
    def prep(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare content for validation"""
        
        content = shared.get('parsed_content', '')
        metadata = shared.get('content_metadata', {})
        
        return {
            'content': content,
            'metadata': metadata
        }
    
    async def exec_async(self, prep_result: Dict[str, Any]) -> Dict[str, Any]:
        """Validate document content"""
        
        content = prep_result['content']
        metadata = prep_result['metadata']
        
        validation_result = {
            'is_valid': True,
            'issues': [],
            'content': content,
            'metadata': metadata
        }
        
        # Check content length
        content_length = len(content)
        
        if content_length < self.min_content_length:
            validation_result['is_valid'] = False
            validation_result['issues'].append(f"Content too short: {content_length} characters")
        
        if content_length > self.max_content_length:
            validation_result['issues'].append(f"Content very long: {content_length} characters")
            # Truncate content
            content = content[:self.max_content_length]
            validation_result['content'] = content
            validation_result['metadata']['truncated'] = True
        
        # Check for empty content
        if not content.strip():
            validation_result['is_valid'] = False
            validation_result['issues'].append("Content is empty or whitespace only")
        
        # Check for binary content (basic check)
        try:
            content.encode('utf-8')
        except UnicodeEncodeError:
            validation_result['is_valid'] = False
            validation_result['issues'].append("Content contains invalid characters")
        
        # Log validation results
        if validation_result['is_valid']:
            logger.debug(f"Document validation passed: {metadata.get('file_name', 'unknown')}")
        else:
            logger.warning(f"Document validation issues: {validation_result['issues']}")
        
        return validation_result
    
    def post(self, shared: Dict[str, Any], prep_result: Dict[str, Any], 
             exec_result: Dict[str, Any]) -> str:
        """Update shared context with validated content"""
        
        # Update shared context
        shared['parsed_content'] = exec_result['content']
        shared['content_metadata'] = exec_result['metadata']
        shared['validation_result'] = exec_result
        
        if exec_result['is_valid']:
            return "default"
        else:
            # Still continue processing even with validation issues
            logger.warning("Continuing with invalid content")
            return "default"


class ParseMultipleDocumentsNode(AsyncNode):
    """Node for parsing multiple documents in batch"""
    
    def __init__(self, node_id: Optional[str] = None,
                 max_concurrent: int = 3,
                 max_file_size_mb: int = 100,
                 encoding: str = 'utf-8'):
        super().__init__(node_id=node_id or "ParseMultipleDocuments")
        self.max_concurrent = max_concurrent
        self.file_processor = FileProcessor(
            max_file_size_mb=max_file_size_mb,
            encoding=encoding
        )
    
    def prep(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare list of files for parsing"""
        
        file_paths = shared.get('input_files', [])
        
        if not file_paths:
            raise ValueError("No input files provided")
        
        # Convert to Path objects
        file_paths = [Path(fp) for fp in file_paths]
        
        # Validate files exist
        valid_files = []
        for file_path in file_paths:
            if file_path.exists():
                valid_files.append(file_path)
            else:
                logger.warning(f"File not found, skipping: {file_path}")
        
        if not valid_files:
            raise ValueError("No valid input files found")
        
        logger.info(f"Preparing to parse {len(valid_files)} files")
        
        return {
            'file_paths': valid_files,
            'parser': self.file_processor
        }
    
    async def exec_async(self, prep_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Parse multiple documents concurrently"""
        
        import asyncio
        
        file_paths = prep_result['file_paths']
        parser = prep_result['parser']
        
        # Create semaphore for concurrency control
        semaphore = asyncio.Semaphore(self.max_concurrent)
        
        async def parse_single_file(file_path):
            async with semaphore:
                try:
                    logger.debug(f"Parsing file: {file_path}")
                    result = await parser.parse_file(file_path)
                    
                    # Add file info to metadata
                    result['metadata']['file_path'] = str(file_path)
                    result['metadata']['file_name'] = file_path.name
                    result['metadata']['file_stem'] = file_path.stem
                    result['metadata']['parsing_successful'] = True
                    
                    return result
                    
                except Exception as e:
                    logger.error(f"Failed to parse {file_path}: {e}")
                    return {
                        'content': '',
                        'metadata': {
                            'file_path': str(file_path),
                            'file_name': file_path.name,
                            'file_stem': file_path.stem,
                            'parsing_successful': False,
                            'error': str(e)
                        }
                    }
        
        # Parse all files concurrently
        results = await asyncio.gather(
            *[parse_single_file(fp) for fp in file_paths],
            return_exceptions=True
        )
        
        # Process results
        successful_results = []
        failed_count = 0
        
        for result in results:
            if isinstance(result, Exception):
                failed_count += 1
                logger.error(f"File parsing exception: {result}")
            elif result['metadata'].get('parsing_successful', False):
                successful_results.append(result)
            else:
                failed_count += 1
        
        logger.info(f"Parsed {len(successful_results)} files successfully, {failed_count} failed")
        
        return successful_results
    
    def post(self, shared: Dict[str, Any], prep_result: Dict[str, Any], 
             exec_result: List[Dict[str, Any]]) -> str:
        """Store all parsed documents in shared context"""
        
        # Store parsed documents
        shared['parsed_documents'] = exec_result
        
        # Calculate statistics
        total_content_length = sum(len(doc['content']) for doc in exec_result)
        shared['parsing_stats'] = {
            'total_documents': len(exec_result),
            'total_content_length': total_content_length,
            'average_content_length': total_content_length / len(exec_result) if exec_result else 0
        }
        
        logger.info(f"Stored {len(exec_result)} parsed documents")
        
        return "default"
