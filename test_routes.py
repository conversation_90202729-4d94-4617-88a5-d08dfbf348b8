#!/usr/bin/env python3
"""测试所有 Web 路由的脚本"""

import requests
import sys

def test_route(url, description, expected_status=200):
    """测试单个路由"""
    try:
        response = requests.get(url, timeout=5)
        if response.status_code == expected_status:
            print(f"✅ {description}: OK (HTTP {response.status_code})")
            return True
        else:
            print(f"❌ {description}: HTTP {response.status_code} (期望 {expected_status})")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ {description}: 连接失败 ({e})")
        return False

def main():
    """测试所有路由"""
    print("🔍 测试知识图谱构建器 Web 路由")
    print("=" * 50)
    
    base_url = "http://localhost:8001"
    
    # 定义要测试的路由 (路径, 描述, 期望状态码)
    routes = [
        ("/", "首页", 200),
        ("/upload", "上传页面", 200),
        ("/search", "搜索页面", 200),
        ("/graph", "图谱可视化页面", 200),
        ("/jobs", "作业管理页面", 200),
        ("/health", "健康检查", 200),
        ("/api/statistics", "统计 API", 200),
        ("/nonexistent", "404 错误页面", 404)  # 测试 404 处理
    ]

    success_count = 0
    total_count = len(routes)

    for path, description, expected_status in routes:
        url = base_url + path
        if test_route(url, description, expected_status):
            success_count += 1
    
    print("\n" + "=" * 50)
    print(f"测试结果: {success_count}/{total_count} 个路由正常")
    
    if success_count == total_count:
        print("🎉 所有路由测试通过！")
        return 0
    else:
        print("⚠️ 部分路由存在问题")
        return 1

if __name__ == "__main__":
    sys.exit(main())
