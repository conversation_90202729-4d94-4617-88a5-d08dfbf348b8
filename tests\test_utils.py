"""Tests for utility functions."""

import pytest
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch

from utils.file_utils import FileUtils, ContentSegment
from utils.security_filters import SecurityFilters, PIIDetectionResult
from utils.graph_ops import GraphOperations, Entity, Relation


class TestFileUtils:
    """Test FileUtils class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.file_utils = FileUtils()
    
    def test_estimate_optimal_chunk_size(self):
        """Test chunk size estimation."""
        # Short text
        short_text = "This is a short text."
        chunk_size = self.file_utils.estimate_optimal_chunk_size(short_text)
        assert chunk_size >= 1000  # Minimum chunk size
        
        # Long text
        long_text = "This is a sentence. " * 1000
        chunk_size = self.file_utils.estimate_optimal_chunk_size(long_text)
        assert 1000 <= chunk_size <= 8000  # Reasonable range
    
    def test_split_by_semantic_boundaries(self):
        """Test semantic text splitting."""
        text = "First paragraph.\n\nSecond paragraph. This has multiple sentences.\n\nThird paragraph."
        segments = self.file_utils.split_by_semantic_boundaries(text, chunk_size=50)
        
        assert len(segments) > 0
        assert all(isinstance(seg, ContentSegment) for seg in segments)
        
        # Check that segments cover the entire text
        total_length = sum(seg.end_pos - seg.start_pos for seg in segments)
        assert total_length == len(text)
    
    def test_parse_file_txt(self):
        """Test parsing TXT files."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("Test content\nSecond line")
            temp_path = f.name
        
        try:
            file_type, content = self.file_utils.parse_file(temp_path)
            assert file_type == "txt"
            assert "Test content" in content
            assert "Second line" in content
        finally:
            Path(temp_path).unlink()
    
    def test_parse_file_unsupported(self):
        """Test parsing unsupported file types."""
        with tempfile.NamedTemporaryFile(suffix='.xyz', delete=False) as f:
            temp_path = f.name
        
        try:
            with pytest.raises(ValueError, match="Unsupported file type"):
                self.file_utils.parse_file(temp_path)
        finally:
            Path(temp_path).unlink()
    
    def test_parse_file_nonexistent(self):
        """Test parsing non-existent files."""
        with pytest.raises(FileNotFoundError):
            self.file_utils.parse_file("nonexistent_file.txt")


class TestSecurityFilters:
    """Test SecurityFilters class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.security_filters = SecurityFilters()
    
    def test_detect_pii_email(self):
        """Test email PII detection."""
        text = "Contact <NAME_EMAIL> for more info."
        result = self.security_filters.detect_pii(text)
        
        assert result.has_pii
        assert "email" in result.detected_types
        assert "EMAIL_REDACTED" in result.redacted_text
        assert "<EMAIL>" not in result.redacted_text
    
    def test_detect_pii_phone(self):
        """Test phone number PII detection."""
        text = "Call me at (************* or ************."
        result = self.security_filters.detect_pii(text)
        
        assert result.has_pii
        assert "phone" in result.detected_types
        assert "PHONE_REDACTED" in result.redacted_text
    
    def test_detect_pii_chinese_id(self):
        """Test Chinese ID PII detection."""
        text = "身份证号码：123456789012345678"
        result = self.security_filters.detect_pii(text)
        
        assert result.has_pii
        assert "chinese_id" in result.detected_types
        assert "ID_REDACTED" in result.redacted_text
    
    def test_detect_pii_no_pii(self):
        """Test text without PII."""
        text = "This is a normal text without any personal information."
        result = self.security_filters.detect_pii(text)
        
        assert not result.has_pii
        assert len(result.detected_types) == 0
        assert result.redacted_text == text
    
    def test_sanitize_html_basic(self):
        """Test basic HTML sanitization."""
        html = '<p>Safe content</p><script>alert("xss")</script>'
        sanitized = self.security_filters.sanitize_html(html)
        
        assert "<script>" not in sanitized
        assert "alert" not in sanitized
        assert "Safe content" in sanitized
    
    def test_sanitize_html_allow_formatting(self):
        """Test HTML sanitization with allowed formatting."""
        html = '<p>Safe <strong>content</strong></p><script>alert("xss")</script>'
        sanitized = self.security_filters.sanitize_html(html, allow_basic_formatting=True)
        
        assert "<script>" not in sanitized
        assert "<p>" in sanitized or "Safe" in sanitized
        assert "<strong>" in sanitized or "content" in sanitized
    
    def test_validate_input_valid(self):
        """Test input validation with valid input."""
        text = "This is a valid input text."
        is_valid, error = self.security_filters.validate_input(text)
        
        assert is_valid
        assert error == ""
    
    def test_validate_input_too_long(self):
        """Test input validation with too long input."""
        text = "x" * 20000  # Exceeds default max length
        is_valid, error = self.security_filters.validate_input(text)
        
        assert not is_valid
        assert "too long" in error.lower()
    
    def test_validate_input_too_short(self):
        """Test input validation with too short input."""
        text = ""
        is_valid, error = self.security_filters.validate_input(text, min_length=1)
        
        assert not is_valid
        assert "too short" in error.lower()
    
    def test_clean_entity_name(self):
        """Test entity name cleaning."""
        dirty_name = "Entity/Name<>With:Bad*Characters?"
        clean_name = self.security_filters.clean_entity_name(dirty_name)
        
        assert "/" not in clean_name
        assert "<" not in clean_name
        assert ">" not in clean_name
        assert ":" not in clean_name
        assert "*" not in clean_name
        assert "?" not in clean_name
    
    def test_create_safe_filename(self):
        """Test safe filename creation."""
        unsafe_filename = "file<>name:with*bad?chars.txt"
        safe_filename = self.security_filters.create_safe_filename(unsafe_filename)
        
        assert "<" not in safe_filename
        assert ">" not in safe_filename
        assert ":" not in safe_filename
        assert "*" not in safe_filename
        assert "?" not in safe_filename
        assert safe_filename.endswith(".txt")
    
    def test_filter_content_comprehensive(self):
        """Test comprehensive content filtering."""
        text = "Contact <EMAIL> for <script>alert('xss')</script> info."
        result = self.security_filters.filter_content(
            text,
            redact_pii=True,
            sanitize_html=True,
            validate_input=True
        )
        
        assert result["is_safe"]
        assert "EMAIL_REDACTED" in result["filtered_text"]
        assert "<script>" not in result["filtered_text"]
        assert len(result["warnings"]) > 0


class TestGraphOperations:
    """Test GraphOperations class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.graph_ops = GraphOperations()
    
    def test_generate_stable_id(self):
        """Test stable ID generation."""
        name1 = "Test Entity"
        name2 = "test entity"  # Different case
        name3 = "  Test Entity  "  # With whitespace
        
        id1 = self.graph_ops.generate_stable_id(name1)
        id2 = self.graph_ops.generate_stable_id(name2)
        id3 = self.graph_ops.generate_stable_id(name3)
        
        # Should generate same ID for normalized names
        assert id1 == id2 == id3
        assert len(id1) == 12  # Expected length
    
    def test_calculate_similarity(self):
        """Test similarity calculation."""
        # Identical names
        sim1 = self.graph_ops.calculate_similarity("John Doe", "John Doe")
        assert sim1 == 1.0
        
        # Similar names
        sim2 = self.graph_ops.calculate_similarity("John Doe", "Jon Doe")
        assert 0.8 < sim2 < 1.0
        
        # Different names
        sim3 = self.graph_ops.calculate_similarity("John Doe", "Jane Smith")
        assert sim3 < 0.5
    
    def test_merge_entities(self):
        """Test entity merging."""
        entity1 = Entity(
            canonical_name="John Doe",
            entity_type="person",
            description="Software engineer",
            alternatives=["J. Doe"],
            source_weight=1
        )
        
        entity2 = Entity(
            canonical_name="Jon Doe",
            entity_type="person",
            description="Senior software engineer",
            alternatives=["Johnny"],
            source_weight=2
        )
        
        merged = self.graph_ops.merge_entities(entity1, entity2)
        
        # Should prefer higher weight entity's name
        assert merged.canonical_name == "Jon Doe"
        assert merged.source_weight == 2
        
        # Should merge alternatives
        assert "J. Doe" in merged.alternatives
        assert "Johnny" in merged.alternatives
        assert "John Doe" in merged.alternatives  # Original name becomes alternative
    
    def test_normalize_entities(self):
        """Test entity normalization."""
        entities = [
            Entity(canonical_name="John Doe", entity_type="person"),
            Entity(canonical_name="Jon Doe", entity_type="person"),  # Similar
            Entity(canonical_name="Jane Smith", entity_type="person"),  # Different
            Entity(canonical_name="JOHN DOE", entity_type="person"),  # Case difference
        ]
        
        normalized = self.graph_ops.normalize_entities(entities)
        
        # Should reduce similar entities
        assert len(normalized) < len(entities)
        
        # Should preserve different entities
        names = [entity.canonical_name for entity in normalized]
        assert any("Jane Smith" in name for name in names)
    
    def test_create_graph_from_entities_relations(self):
        """Test graph creation."""
        entities = [
            Entity(canonical_name="John Doe", entity_type="person"),
            Entity(canonical_name="Acme Corp", entity_type="organization")
        ]
        
        relations = [
            Relation(source="John Doe", target="Acme Corp", relation_type="works_at")
        ]
        
        graph = self.graph_ops.create_graph_from_entities_relations(entities, relations)
        
        assert graph.number_of_nodes() == 2
        assert graph.number_of_edges() == 1
        
        # Check node data
        nodes = list(graph.nodes(data=True))
        assert len(nodes) == 2
        
        # Check edge data
        edges = list(graph.edges(data=True))
        assert len(edges) == 1
        assert edges[0][2]["relation_type"] == "works_at"


@pytest.fixture
def sample_entities():
    """Fixture providing sample entities for testing."""
    return [
        Entity(canonical_name="John Doe", entity_type="person", description="Software engineer"),
        Entity(canonical_name="Jane Smith", entity_type="person", description="Data scientist"),
        Entity(canonical_name="Acme Corp", entity_type="organization", description="Technology company"),
    ]


@pytest.fixture
def sample_relations():
    """Fixture providing sample relations for testing."""
    return [
        Relation(source="John Doe", target="Acme Corp", relation_type="works_at"),
        Relation(source="Jane Smith", target="Acme Corp", relation_type="works_at"),
        Relation(source="John Doe", target="Jane Smith", relation_type="colleague"),
    ]


def test_integration_graph_creation(sample_entities, sample_relations):
    """Integration test for graph creation."""
    graph_ops = GraphOperations()
    
    # Create graph
    graph = graph_ops.create_graph_from_entities_relations(sample_entities, sample_relations)
    
    # Verify graph structure
    assert graph.number_of_nodes() == 3
    assert graph.number_of_edges() == 3
    
    # Export and import
    graph_dict = graph_ops.export_to_dict(graph)
    imported_graph = graph_ops.import_from_dict(graph_dict)
    
    # Should be equivalent
    assert imported_graph.number_of_nodes() == graph.number_of_nodes()
    assert imported_graph.number_of_edges() == graph.number_of_edges()
