"""
Segment processing nodes for LLM-based entity extraction
Handles parallel processing of document segments using multiple LLM models
"""

import asyncio
from typing import Dict, Any, List, Optional
from loguru import logger

from .base_node import AsyncNode, AsyncBatchNode
from utils.llm_client import LLMClient
from utils.task_queue import AsyncTaskManager


class ProcessSegmentNode(AsyncNode):
    """Node for processing a single segment with LLM"""
    
    def __init__(self, node_id: Optional[str] = None,
                 llm_settings: Optional[Dict[str, Any]] = None,
                 min_entity_confidence: float = 0.7,
                 max_entities_per_segment: int = 50):
        super().__init__(
            node_id=node_id or "ProcessSegment",
            max_retries=3,
            timeout=300
        )
        
        self.llm_settings = llm_settings or {}
        self.min_entity_confidence = min_entity_confidence
        self.max_entities_per_segment = max_entities_per_segment
    
    def prep(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare segment for LLM processing"""
        
        # Get current segment
        segment = shared.get('current_segment')
        if not segment:
            raise ValueError("No segment provided for processing")
        
        # Get segment index for model routing
        segment_index = segment.get('chunk_id', 0)
        
        # Get model router from shared context
        model_router = shared.get('model_router')
        if not model_router:
            raise ValueError("No model router provided")
        
        # Select model for this segment
        model_name = model_router(segment_index)
        
        return {
            'segment': segment,
            'segment_index': segment_index,
            'model_name': model_name,
            'llm_settings': self.llm_settings
        }
    
    async def exec_async(self, prep_result: Dict[str, Any]) -> Dict[str, Any]:
        """Process segment with LLM"""
        
        segment = prep_result['segment']
        model_name = prep_result['model_name']
        segment_index = prep_result['segment_index']
        
        text = segment['text']
        
        # Skip very short segments
        if len(text.strip()) < 50:
            logger.debug(f"Skipping short segment {segment_index}: {len(text)} chars")
            return {
                "entities": [],
                "relations": [],
                "segment_index": segment_index,
                "processing_successful": True,
                "skipped": True
            }
        
        logger.debug(f"Processing segment {segment_index} with model {model_name}")
        
        try:
            # Create LLM client
            async with LLMClient(
                base_url=self.llm_settings.get('base_url', ''),
                api_key=self.llm_settings.get('api_key', ''),
                models=self.llm_settings.get('models', {}),
                max_tokens=self.llm_settings.get('max_tokens', 4000),
                temperature=self.llm_settings.get('temperature', 0.1),
                timeout=self.llm_settings.get('timeout', 300),
                max_retries=self.llm_settings.get('max_retries', 3)
            ) as client:
                
                # Extract entities and relations
                result = await client.extract_entities_and_relations(text, model_name)
                
                # Filter results by confidence
                filtered_entities = [
                    entity for entity in result.get('entities', [])
                    if entity.get('confidence', 0) >= self.min_entity_confidence
                ]
                
                # Limit number of entities per segment
                if len(filtered_entities) > self.max_entities_per_segment:
                    # Sort by confidence and take top entities
                    filtered_entities.sort(key=lambda x: x.get('confidence', 0), reverse=True)
                    filtered_entities = filtered_entities[:self.max_entities_per_segment]
                
                # Add segment metadata to entities
                for entity in filtered_entities:
                    entity['segment_index'] = segment_index
                    entity['source_file'] = segment.get('metadata', {}).get('file_path', '')
                
                # Add segment metadata to relations
                filtered_relations = result.get('relations', [])
                for relation in filtered_relations:
                    relation['segment_index'] = segment_index
                    relation['source_file'] = segment.get('metadata', {}).get('file_path', '')
                
                logger.debug(f"Segment {segment_index} processed: "
                           f"{len(filtered_entities)} entities, {len(filtered_relations)} relations")
                
                return {
                    "entities": filtered_entities,
                    "relations": filtered_relations,
                    "segment_index": segment_index,
                    "model_used": model_name,
                    "processing_successful": True
                }
                
        except Exception as e:
            logger.error(f"Segment processing failed {segment_index}: {e}")
            return {
                "entities": [],
                "relations": [],
                "segment_index": segment_index,
                "processing_successful": False,
                "error": str(e)
            }
    
    def post(self, shared: Dict[str, Any], prep_result: Dict[str, Any], 
             exec_result: Dict[str, Any]) -> str:
        """Store segment processing result"""
        
        # Store result in shared context
        if 'segment_results' not in shared:
            shared['segment_results'] = []
        
        shared['segment_results'].append(exec_result)
        
        return "default"


class ProcessSegmentBatchNode(AsyncBatchNode):
    """Node for processing multiple segments in parallel"""
    
    def __init__(self, node_id: Optional[str] = None,
                 llm_settings: Optional[Dict[str, Any]] = None,
                 batch_size: int = 5,
                 max_concurrent: int = 3,
                 min_entity_confidence: float = 0.7,
                 max_entities_per_segment: int = 50):
        super().__init__(
            node_id=node_id or "ProcessSegmentBatch",
            batch_size=batch_size,
            max_concurrent=max_concurrent,
            max_retries=2,
            timeout=600
        )
        
        self.llm_settings = llm_settings or {}
        self.min_entity_confidence = min_entity_confidence
        self.max_entities_per_segment = max_entities_per_segment
    
    def prep(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare segments for batch processing"""
        
        segments = shared.get('content_segments', [])
        if not segments:
            raise ValueError("No segments provided for processing")
        
        # Get model router
        model_router = shared.get('model_router')
        if not model_router:
            raise ValueError("No model router provided")
        
        return {
            'items': segments,
            'model_router': model_router,
            'llm_settings': self.llm_settings
        }
    
    async def exec_batch_async(self, batch: List[Any], prep_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Process a batch of segments"""
        
        model_router = prep_result['model_router']
        llm_settings = prep_result['llm_settings']
        
        results = []
        
        # Create single LLM client for the batch
        async with LLMClient(
            base_url=llm_settings.get('base_url', ''),
            api_key=llm_settings.get('api_key', ''),
            models=llm_settings.get('models', {}),
            max_tokens=llm_settings.get('max_tokens', 4000),
            temperature=llm_settings.get('temperature', 0.1),
            timeout=llm_settings.get('timeout', 300),
            max_retries=llm_settings.get('max_retries', 3)
        ) as client:
            
            # Process each segment in the batch
            for segment in batch:
                try:
                    segment_index = segment.get('chunk_id', 0)
                    text = segment['text']
                    
                    # Skip very short segments
                    if len(text.strip()) < 50:
                        logger.debug(f"Skipping short segment {segment_index}")
                        results.append({
                            "entities": [],
                            "relations": [],
                            "segment_index": segment_index,
                            "processing_successful": True,
                            "skipped": True
                        })
                        continue
                    
                    # Select model for this segment
                    model_name = model_router(segment_index)
                    
                    logger.debug(f"Processing segment {segment_index} with {model_name}")
                    
                    # Extract entities and relations
                    result = await client.extract_entities_and_relations(text, model_name)
                    
                    # Filter and process results
                    filtered_entities = [
                        entity for entity in result.get('entities', [])
                        if entity.get('confidence', 0) >= self.min_entity_confidence
                    ]
                    
                    # Limit entities per segment
                    if len(filtered_entities) > self.max_entities_per_segment:
                        filtered_entities.sort(key=lambda x: x.get('confidence', 0), reverse=True)
                        filtered_entities = filtered_entities[:self.max_entities_per_segment]
                    
                    # Add metadata
                    for entity in filtered_entities:
                        entity['segment_index'] = segment_index
                        entity['source_file'] = segment.get('metadata', {}).get('file_path', '')
                    
                    filtered_relations = result.get('relations', [])
                    for relation in filtered_relations:
                        relation['segment_index'] = segment_index
                        relation['source_file'] = segment.get('metadata', {}).get('file_path', '')
                    
                    results.append({
                        "entities": filtered_entities,
                        "relations": filtered_relations,
                        "segment_index": segment_index,
                        "model_used": model_name,
                        "processing_successful": True
                    })
                    
                except Exception as e:
                    logger.error(f"Segment {segment.get('chunk_id', 0)} processing failed: {e}")
                    results.append({
                        "entities": [],
                        "relations": [],
                        "segment_index": segment.get('chunk_id', 0),
                        "processing_successful": False,
                        "error": str(e)
                    })
        
        return results
    
    def post(self, shared: Dict[str, Any], prep_result: Dict[str, Any], 
             exec_result: List[Dict[str, Any]]) -> str:
        """Store batch processing results"""
        
        # Flatten results
        all_results = []
        for batch_result in exec_result:
            if isinstance(batch_result, list):
                all_results.extend(batch_result)
            else:
                all_results.append(batch_result)
        
        # Store results
        shared['segment_results'] = all_results
        
        # Calculate statistics
        successful_segments = sum(1 for r in all_results if r.get('processing_successful', False))
        total_entities = sum(len(r.get('entities', [])) for r in all_results)
        total_relations = sum(len(r.get('relations', [])) for r in all_results)
        
        shared['processing_stats'] = {
            'total_segments': len(all_results),
            'successful_segments': successful_segments,
            'failed_segments': len(all_results) - successful_segments,
            'total_entities': total_entities,
            'total_relations': total_relations
        }
        
        logger.info(f"Batch processing completed: {successful_segments}/{len(all_results)} successful, "
                   f"{total_entities} entities, {total_relations} relations")
        
        return "default"


class ProcessSegmentWithTaskManagerNode(AsyncNode):
    """Node for processing segments using AsyncTaskManager"""
    
    def __init__(self, node_id: Optional[str] = None,
                 llm_settings: Optional[Dict[str, Any]] = None,
                 max_concurrent: int = 5,
                 min_entity_confidence: float = 0.7):
        super().__init__(
            node_id=node_id or "ProcessSegmentWithTaskManager",
            timeout=1800  # 30 minutes total timeout
        )
        
        self.llm_settings = llm_settings or {}
        self.max_concurrent = max_concurrent
        self.min_entity_confidence = min_entity_confidence
    
    def prep(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare segments for task manager processing"""
        
        segments = shared.get('content_segments', [])
        if not segments:
            raise ValueError("No segments provided for processing")
        
        model_router = shared.get('model_router')
        if not model_router:
            raise ValueError("No model router provided")
        
        return {
            'segments': segments,
            'model_router': model_router,
            'llm_settings': self.llm_settings
        }
    
    async def exec_async(self, prep_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Process segments using task manager"""
        
        segments = prep_result['segments']
        model_router = prep_result['model_router']
        llm_settings = prep_result['llm_settings']
        
        # Create task manager
        task_manager = AsyncTaskManager(
            max_concurrent=self.max_concurrent,
            rate_limit=None  # LLM client handles rate limiting
        )
        
        # Add segment processing tasks
        task_ids = []
        for i, segment in enumerate(segments):
            task_id = task_manager.add_task(
                task_id=f"segment_{i}",
                func=self._process_single_segment,
                segment,
                model_router(i),
                llm_settings,
                priority=0,
                max_retries=2,
                timeout=300
            )
            task_ids.append(task_id)
        
        # Start task manager
        manager_task = asyncio.create_task(task_manager.start())
        
        try:
            # Wait for all tasks to complete
            await task_manager.wait_for_completion(timeout=1500)  # 25 minutes
            
            # Collect results
            results = []
            for task_id in task_ids:
                task_result = task_manager.get_task_result(task_id)
                if task_result and task_result.result:
                    results.append(task_result.result)
                else:
                    # Failed task
                    segment_index = int(task_id.split('_')[1])
                    results.append({
                        "entities": [],
                        "relations": [],
                        "segment_index": segment_index,
                        "processing_successful": False,
                        "error": "Task execution failed"
                    })
            
            # Sort results by segment index
            results.sort(key=lambda x: x.get('segment_index', 0))
            
            return results
            
        finally:
            # Stop task manager
            await task_manager.stop()
            manager_task.cancel()
            try:
                await manager_task
            except asyncio.CancelledError:
                pass
    
    async def _process_single_segment(self, segment: Dict[str, Any], 
                                    model_name: str, 
                                    llm_settings: Dict[str, Any]) -> Dict[str, Any]:
        """Process a single segment"""
        
        segment_index = segment.get('chunk_id', 0)
        text = segment['text']
        
        # Skip short segments
        if len(text.strip()) < 50:
            return {
                "entities": [],
                "relations": [],
                "segment_index": segment_index,
                "processing_successful": True,
                "skipped": True
            }
        
        try:
            async with LLMClient(
                base_url=llm_settings.get('base_url', ''),
                api_key=llm_settings.get('api_key', ''),
                models=llm_settings.get('models', {}),
                max_tokens=llm_settings.get('max_tokens', 4000),
                temperature=llm_settings.get('temperature', 0.1)
            ) as client:
                
                result = await client.extract_entities_and_relations(text, model_name)
                
                # Filter entities by confidence
                filtered_entities = [
                    entity for entity in result.get('entities', [])
                    if entity.get('confidence', 0) >= self.min_entity_confidence
                ]
                
                # Add metadata
                for entity in filtered_entities:
                    entity['segment_index'] = segment_index
                    entity['source_file'] = segment.get('metadata', {}).get('file_path', '')
                
                filtered_relations = result.get('relations', [])
                for relation in filtered_relations:
                    relation['segment_index'] = segment_index
                    relation['source_file'] = segment.get('metadata', {}).get('file_path', '')
                
                return {
                    "entities": filtered_entities,
                    "relations": filtered_relations,
                    "segment_index": segment_index,
                    "model_used": model_name,
                    "processing_successful": True
                }
                
        except Exception as e:
            logger.error(f"Segment {segment_index} processing failed: {e}")
            return {
                "entities": [],
                "relations": [],
                "segment_index": segment_index,
                "processing_successful": False,
                "error": str(e)
            }
    
    def post(self, shared: Dict[str, Any], prep_result: Dict[str, Any], 
             exec_result: List[Dict[str, Any]]) -> str:
        """Store task manager results"""
        
        shared['segment_results'] = exec_result
        
        # Statistics
        successful = sum(1 for r in exec_result if r.get('processing_successful', False))
        total_entities = sum(len(r.get('entities', [])) for r in exec_result)
        total_relations = sum(len(r.get('relations', [])) for r in exec_result)
        
        logger.info(f"Task manager processing: {successful}/{len(exec_result)} successful, "
                   f"{total_entities} entities, {total_relations} relations")
        
        return "default"
