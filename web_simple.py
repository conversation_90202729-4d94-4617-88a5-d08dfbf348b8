#!/usr/bin/env python3
"""Simple web interface for Knowledge Graph Builder."""

import asyncio
import json
import tempfile
import shutil
from pathlib import Path
from typing import Dict, Any, List

try:
    from fastapi import FastAPI, Request, UploadFile, File, Form, HTTPException
    from fastapi.responses import HTMLResponse, JSONResponse
    from fastapi.templating import Jinja2Templates
    from fastapi.staticfiles import StaticFiles
    import uvicorn
except ImportError as e:
    print(f"❌ Missing web dependencies: {e}")
    print("Please run: python quick_fix.py")
    exit(1)

from core.pipeline import Pipeline
from config.settings import Settings

# Initialize FastAPI app
app = FastAPI(title="Knowledge Graph Builder", version="0.1.0")

# Try to set up templates
try:
    templates = Jinja2Templates(directory="web/templates")
except Exception:
    templates = None

# Global variables
settings = Settings()
current_pipeline = None
processing_jobs = {}


@app.get("/", response_class=HTMLResponse)
async def index(request: Request):
    """主页面"""
    return await get_main_page(request)

@app.get("/upload", response_class=HTMLResponse)
async def upload_page(request: Request):
    """文档上传页面"""
    return await get_main_page(request)

@app.get("/search", response_class=HTMLResponse)
async def search_page(request: Request):
    """实体搜索页面"""
    return await get_search_page(request)

@app.get("/graph", response_class=HTMLResponse)
async def graph_page(request: Request):
    """图谱可视化页面"""
    return await get_graph_page(request)

@app.get("/jobs", response_class=HTMLResponse)
async def jobs_page(request: Request):
    """作业管理页面"""
    return await get_jobs_page(request)

async def get_main_page(request: Request):
    """获取主页面内容"""
    if templates:
        try:
            return templates.TemplateResponse("index.html", {"request": request})
        except Exception:
            pass

async def get_search_page(request: Request):
    """获取搜索页面内容"""
    html_content = """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <title>实体搜索 - 知识图谱构建器</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/">
                    <i class="fas fa-sitemap me-2"></i>
                    知识图谱构建器
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/">
                        <i class="fas fa-home me-1"></i>
                        首页
                    </a>
                    <a class="nav-link" href="/upload">
                        <i class="fas fa-upload me-1"></i>
                        上传
                    </a>
                    <a class="nav-link active" href="/search">
                        <i class="fas fa-search me-1"></i>
                        搜索
                    </a>
                    <a class="nav-link" href="#" onclick="showHelp()">
                        <i class="fas fa-question-circle me-1"></i>
                        帮助
                    </a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <div class="row">
                <div class="col-md-8 mx-auto">
                    <div class="card shadow">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-search me-2"></i>
                                实体搜索
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="search-query" class="form-label fw-bold">搜索关键词</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="search-query" placeholder="输入实体名称或关键词...">
                                    <button class="btn btn-primary" onclick="performSearch()">
                                        <i class="fas fa-search me-1"></i>
                                        搜索
                                    </button>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="entity-type" class="form-label fw-bold">实体类型（可选）</label>
                                <select class="form-select" id="entity-type">
                                    <option value="">所有类型</option>
                                    <option value="person">人员</option>
                                    <option value="organization">组织</option>
                                    <option value="location">地点</option>
                                    <option value="concept">概念</option>
                                    <option value="event">事件</option>
                                    <option value="product">产品</option>
                                </select>
                            </div>

                            <div id="search-results"></div>
                        </div>
                    </div>

                    <div class="alert alert-info mt-4">
                        <h6><i class="fas fa-info-circle me-2"></i>使用说明</h6>
                        <ul class="mb-0">
                            <li>请先在首页处理文档以构建知识图谱</li>
                            <li>输入实体名称或相关关键词进行搜索</li>
                            <li>可以选择特定的实体类型来缩小搜索范围</li>
                            <li>搜索结果将显示实体的详细信息和相关度</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
        <script>
            document.getElementById('search-query').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });

            async function performSearch() {
                const query = document.getElementById('search-query').value.trim();
                const entityType = document.getElementById('entity-type').value;
                const resultsDiv = document.getElementById('search-results');

                if (!query) {
                    alert('请输入搜索关键词');
                    return;
                }

                resultsDiv.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">搜索中...</p></div>';

                try {
                    const response = await fetch('/api/search', {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify({query: query, entity_type: entityType})
                    });

                    const results = await response.json();

                    if (response.ok) {
                        displaySearchResults(results, query);
                    } else {
                        resultsDiv.innerHTML = `<div class="alert alert-warning">${results.detail}</div>`;
                    }
                } catch (error) {
                    resultsDiv.innerHTML = `<div class="alert alert-danger">搜索失败：${error.message}</div>`;
                }
            }

            function displaySearchResults(results, query) {
                const resultsDiv = document.getElementById('search-results');

                if (results.length === 0) {
                    resultsDiv.innerHTML = `
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            未找到与 "${query}" 相关的实体，请尝试其他关键词。
                        </div>
                    `;
                    return;
                }

                let html = `<h6 class="mt-3">搜索结果 (${results.length} 个)</h6>`;
                html += '<div class="list-group">';

                results.forEach(result => {
                    const typeNames = {
                        'person': '人员',
                        'organization': '组织',
                        'location': '地点',
                        'concept': '概念',
                        'event': '事件',
                        'product': '产品'
                    };

                    html += `
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">${result.entity.name}</h6>
                                <small class="text-muted">相关度: ${result.score.toFixed(2)}</small>
                            </div>
                            <p class="mb-1">${result.entity.description || '暂无描述'}</p>
                            <small class="text-muted">
                                <i class="fas fa-tag me-1"></i>
                                类型: ${typeNames[result.entity.type] || result.entity.type}
                            </small>
                        </div>
                    `;
                });

                html += '</div>';
                resultsDiv.innerHTML = html;
            }

            function showHelp() {
                alert('实体搜索功能使用说明：\\n\\n1. 请先在首页处理文档以构建知识图谱\\n2. 输入实体名称或相关关键词\\n3. 可选择特定实体类型进行过滤\\n4. 按回车键或点击搜索按钮开始搜索\\n5. 查看搜索结果和相关度评分');
            }
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

async def get_graph_page(request: Request):
    """获取图谱可视化页面内容"""
    html_content = """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <title>图谱可视化 - 知识图谱构建器</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/">
                    <i class="fas fa-sitemap me-2"></i>
                    知识图谱构建器
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/">
                        <i class="fas fa-home me-1"></i>
                        首页
                    </a>
                    <a class="nav-link" href="/upload">
                        <i class="fas fa-upload me-1"></i>
                        上传
                    </a>
                    <a class="nav-link" href="/search">
                        <i class="fas fa-search me-1"></i>
                        搜索
                    </a>
                    <a class="nav-link active" href="/graph">
                        <i class="fas fa-project-diagram me-1"></i>
                        图谱
                    </a>
                </div>
            </div>
        </nav>

        <div class="container-fluid mt-4">
            <div class="row">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-project-diagram me-2"></i>
                                知识图谱可视化
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>图谱可视化功能</h6>
                                <p class="mb-0">此功能正在开发中，将提供交互式的知识图谱可视化界面。</p>
                                <p class="mb-0">您可以在首页处理文档并下载 JSON 数据，然后使用其他图谱可视化工具进行展示。</p>
                            </div>

                            <div class="text-center py-5">
                                <i class="fas fa-project-diagram fa-5x text-muted mb-3"></i>
                                <h4>图谱可视化</h4>
                                <p class="text-muted">敬请期待...</p>
                                <a href="/" class="btn btn-primary">
                                    <i class="fas fa-arrow-left me-1"></i>
                                    返回首页处理文档
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

async def get_jobs_page(request: Request):
    """获取作业管理页面内容"""
    html_content = """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <title>作业管理 - 知识图谱构建器</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/">
                    <i class="fas fa-sitemap me-2"></i>
                    知识图谱构建器
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/">
                        <i class="fas fa-home me-1"></i>
                        首页
                    </a>
                    <a class="nav-link" href="/upload">
                        <i class="fas fa-upload me-1"></i>
                        上传
                    </a>
                    <a class="nav-link" href="/search">
                        <i class="fas fa-search me-1"></i>
                        搜索
                    </a>
                    <a class="nav-link active" href="/jobs">
                        <i class="fas fa-tasks me-1"></i>
                        作业
                    </a>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <div class="row">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0">
                                <i class="fas fa-tasks me-2"></i>
                                处理作业管理
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>作业管理功能</h6>
                                <p class="mb-0">此功能正在开发中，将提供处理作业的状态监控和管理功能。</p>
                                <p class="mb-0">目前所有文档处理都是实时完成的，您可以在首页直接查看处理结果。</p>
                            </div>

                            <div class="text-center py-5">
                                <i class="fas fa-tasks fa-5x text-muted mb-3"></i>
                                <h4>作业管理</h4>
                                <p class="text-muted">敬请期待...</p>
                                <a href="/" class="btn btn-primary">
                                    <i class="fas fa-arrow-left me-1"></i>
                                    返回首页处理文档
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)
    
    # Fallback HTML
    html_content = """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <title>知识图谱构建器</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            .upload-area {
                border: 2px dashed #dee2e6;
                border-radius: 0.5rem;
                padding: 2rem;
                text-align: center;
                transition: all 0.3s ease;
            }
            .upload-area:hover {
                border-color: #0d6efd;
                background-color: #f8f9fa;
            }
            .upload-area.dragover {
                border-color: #0d6efd;
                background-color: #e7f3ff;
            }
        </style>
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/">
                    <i class="fas fa-sitemap me-2"></i>
                    知识图谱构建器
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link active" href="/">
                        <i class="fas fa-home me-1"></i>
                        首页
                    </a>
                    <a class="nav-link" href="/upload">
                        <i class="fas fa-upload me-1"></i>
                        上传
                    </a>
                    <a class="nav-link" href="/search">
                        <i class="fas fa-search me-1"></i>
                        搜索
                    </a>
                    <a class="nav-link" href="/graph">
                        <i class="fas fa-project-diagram me-1"></i>
                        图谱
                    </a>
                    <a class="nav-link" href="/jobs">
                        <i class="fas fa-tasks me-1"></i>
                        作业
                    </a>
                    <a class="nav-link" href="#" onclick="showHelp()">
                        <i class="fas fa-question-circle me-1"></i>
                        帮助
                    </a>
                </div>
            </div>
        </nav>
        
        <div class="container mt-4">
            <!-- 欢迎信息 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="alert alert-info">
                        <h4 class="alert-heading">
                            <i class="fas fa-rocket me-2"></i>
                            欢迎使用知识图谱构建器！
                        </h4>
                        <p class="mb-0">上传文档，自动提取实体和关系，构建知识图谱。支持 TXT、MD 格式文件。</p>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8 mx-auto">
                    <div class="card shadow">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-upload me-2"></i>
                                文档上传与处理
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="upload-form" enctype="multipart/form-data">
                                <div class="mb-4">
                                    <label for="files" class="form-label fw-bold">选择文件</label>
                                    <div class="upload-area" id="upload-area">
                                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                        <p class="mb-2">点击选择文件或拖拽文件到此处</p>
                                        <input type="file" class="form-control" id="files" name="files" multiple accept=".txt,.md" style="display: none;">
                                        <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('files').click()">
                                            <i class="fas fa-folder-open me-1"></i>
                                            浏览文件
                                        </button>
                                    </div>
                                    <div class="form-text">
                                        <i class="fas fa-info-circle me-1"></i>
                                        支持格式：TXT、MD 文件，可同时上传多个文件
                                    </div>
                                    <div id="file-list" class="mt-2"></div>
                                </div>

                                <div class="mb-4">
                                    <label for="mode" class="form-label fw-bold">处理模式</label>
                                    <select class="form-select" id="mode" name="mode">
                                        <option value="merge">合并模式 - 构建统一知识图谱</option>
                                        <option value="batch">批处理模式 - 每个文档独立图谱</option>
                                    </select>
                                    <div class="form-text">
                                        合并模式将所有文档的实体关系合并为一个图谱，批处理模式为每个文档创建独立图谱
                                    </div>
                                </div>

                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-lg" id="process-btn">
                                        <i class="fas fa-cogs me-2"></i>
                                        开始处理
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <!-- 处理进度 -->
                    <div id="processing" class="mt-4" style="display: none;">
                        <div class="card">
                            <div class="card-body text-center">
                                <div class="spinner-border text-primary mb-3" role="status">
                                    <span class="visually-hidden">处理中...</span>
                                </div>
                                <h5>正在处理文档...</h5>
                                <p class="text-muted">请稍候，正在提取实体和关系</p>
                            </div>
                        </div>
                    </div>

                    <!-- 处理结果 -->
                    <div id="results" class="mt-4" style="display: none;">
                        <div class="card shadow">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-bar me-2"></i>
                                    处理结果
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="results-content"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 功能说明 -->
            <div class="row mt-5">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-lightbulb me-2"></i>
                                功能说明
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <h6><i class="fas fa-file-text text-primary me-2"></i>文档处理</h6>
                                    <p class="small text-muted">支持 TXT、MD 格式文件，自动解析文档内容并进行语义分割</p>
                                </div>
                                <div class="col-md-4">
                                    <h6><i class="fas fa-users text-success me-2"></i>实体提取</h6>
                                    <p class="small text-muted">智能识别人员、组织、地点等实体，并进行分类标注</p>
                                </div>
                                <div class="col-md-4">
                                    <h6><i class="fas fa-project-diagram text-info me-2"></i>关系发现</h6>
                                    <p class="small text-muted">分析实体间的关系，构建完整的知识图谱网络</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
        <script>
            // 文件拖拽功能
            const uploadArea = document.getElementById('upload-area');
            const fileInput = document.getElementById('files');

            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                fileInput.files = e.dataTransfer.files;
                updateFileList();
            });

            fileInput.addEventListener('change', updateFileList);

            function updateFileList() {
                const fileList = document.getElementById('file-list');
                const files = fileInput.files;

                if (files.length === 0) {
                    fileList.innerHTML = '';
                    return;
                }

                let html = '<div class="mt-2"><strong>已选择的文件：</strong><ul class="list-group list-group-flush">';
                for (let file of files) {
                    html += `<li class="list-group-item d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-file-text me-2"></i>${file.name}</span>
                        <span class="badge bg-secondary">${(file.size / 1024).toFixed(1)} KB</span>
                    </li>`;
                }
                html += '</ul></div>';
                fileList.innerHTML = html;
            }

            document.getElementById('upload-form').addEventListener('submit', async function(e) {
                e.preventDefault();

                const formData = new FormData();
                const files = document.getElementById('files').files;
                const mode = document.getElementById('mode').value;
                const processBtn = document.getElementById('process-btn');
                const processing = document.getElementById('processing');
                const results = document.getElementById('results');

                if (files.length === 0) {
                    alert('请至少选择一个文件');
                    return;
                }

                // 显示处理中状态
                processBtn.disabled = true;
                processBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>处理中...';
                processing.style.display = 'block';
                results.style.display = 'none';

                for (let file of files) {
                    formData.append('files', file);
                }
                formData.append('mode', mode);

                try {
                    const response = await fetch('/api/process', {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();

                    if (response.ok) {
                        displayResults(result);
                    } else {
                        alert('处理失败：' + result.detail);
                    }
                } catch (error) {
                    alert('错误：' + error.message);
                } finally {
                    // 恢复按钮状态
                    processBtn.disabled = false;
                    processBtn.innerHTML = '<i class="fas fa-cogs me-2"></i>开始处理';
                    processing.style.display = 'none';
                }
            });
            
            function displayResults(result) {
                const resultsDiv = document.getElementById('results');
                const contentDiv = document.getElementById('results-content');

                let html = '<div class="row">';

                // 处理统计
                html += '<div class="col-md-6">';
                html += '<h6><i class="fas fa-chart-line me-2"></i>处理统计</h6>';
                html += '<div class="table-responsive">';
                html += '<table class="table table-sm">';
                html += `<tr><td>处理文档数</td><td><span class="badge bg-primary">${result.processing_stats?.documents_processed || 0}</span></td></tr>`;
                html += `<tr><td>提取实体数</td><td><span class="badge bg-success">${result.processing_stats?.entities_extracted || 0}</span></td></tr>`;
                html += `<tr><td>发现关系数</td><td><span class="badge bg-info">${result.processing_stats?.relations_extracted || 0}</span></td></tr>`;
                html += `<tr><td>处理时间</td><td><span class="badge bg-secondary">${(result.processing_stats?.processing_time || 0).toFixed(2)}秒</span></td></tr>`;
                html += '</table>';
                html += '</div>';
                html += '</div>';

                // 图谱统计
                if (result.statistics) {
                    html += '<div class="col-md-6">';
                    html += '<h6><i class="fas fa-project-diagram me-2"></i>知识图谱统计</h6>';
                    html += '<div class="table-responsive">';
                    html += '<table class="table table-sm">';
                    html += `<tr><td>总实体数</td><td><span class="badge bg-primary">${result.statistics.total_entities || 0}</span></td></tr>`;
                    html += `<tr><td>总关系数</td><td><span class="badge bg-success">${result.statistics.total_relations || 0}</span></td></tr>`;

                    // 实体类型分布
                    if (result.statistics.entity_types) {
                        html += '<tr><td colspan="2"><strong>实体类型分布：</strong></td></tr>';
                        for (const [type, count] of Object.entries(result.statistics.entity_types)) {
                            const typeNames = {
                                'person': '人员',
                                'organization': '组织',
                                'location': '地点',
                                'concept': '概念',
                                'event': '事件',
                                'product': '产品'
                            };
                            html += `<tr><td>&nbsp;&nbsp;${typeNames[type] || type}</td><td><span class="badge bg-info">${count}</span></td></tr>`;
                        }
                    }

                    html += '</table>';
                    html += '</div>';
                    html += '</div>';
                }

                html += '</div>';

                // 操作按钮
                html += '<div class="mt-4 text-center">';
                html += '<button class="btn btn-success me-2" onclick="downloadResults()">';
                html += '<i class="fas fa-download me-1"></i>下载 JSON 数据</button>';
                html += '<button class="btn btn-info me-2" onclick="searchEntities()">';
                html += '<i class="fas fa-search me-1"></i>搜索实体</button>';
                html += '<button class="btn btn-secondary" onclick="resetForm()">';
                html += '<i class="fas fa-redo me-1"></i>重新处理</button>';
                html += '</div>';

                contentDiv.innerHTML = html;
                resultsDiv.style.display = 'block';

                // Store result for download
                window.lastResult = result;
            }
            
            function downloadResults() {
                if (window.lastResult) {
                    const dataStr = JSON.stringify(window.lastResult, null, 2);
                    const dataBlob = new Blob([dataStr], {type: 'application/json'});
                    const url = URL.createObjectURL(dataBlob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = 'knowledge_graph.json';
                    link.click();
                    URL.revokeObjectURL(url);

                    // 显示下载成功提示
                    const toast = document.createElement('div');
                    toast.className = 'toast align-items-center text-white bg-success border-0';
                    toast.innerHTML = `
                        <div class="d-flex">
                            <div class="toast-body">
                                <i class="fas fa-check-circle me-2"></i>
                                知识图谱数据已下载
                            </div>
                        </div>
                    `;
                    document.body.appendChild(toast);
                    const bsToast = new bootstrap.Toast(toast);
                    bsToast.show();
                    setTimeout(() => document.body.removeChild(toast), 3000);
                }
            }

            function searchEntities() {
                const query = prompt('请输入搜索关键词：');
                if (query) {
                    fetch('/api/search', {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify({query: query})
                    })
                    .then(response => response.json())
                    .then(results => {
                        let html = '<div class="mt-3">';
                        html += `<h6><i class="fas fa-search me-2"></i>搜索结果："${query}"</h6>`;

                        if (results.length > 0) {
                            html += '<div class="list-group">';
                            results.forEach(result => {
                                const typeNames = {
                                    'person': '人员',
                                    'organization': '组织',
                                    'location': '地点',
                                    'concept': '概念',
                                    'event': '事件',
                                    'product': '产品'
                                };
                                html += `<div class="list-group-item">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">${result.entity.name}</h6>
                                        <small class="text-muted">相关度: ${result.score.toFixed(2)}</small>
                                    </div>
                                    <p class="mb-1">${result.entity.description || '暂无描述'}</p>
                                    <small class="text-muted">类型: ${typeNames[result.entity.type] || result.entity.type}</small>
                                </div>`;
                            });
                            html += '</div>';
                        } else {
                            html += '<div class="alert alert-info">';
                            html += '<i class="fas fa-info-circle me-2"></i>未找到相关实体，请尝试其他关键词。';
                            html += '</div>';
                        }

                        html += '<div class="mt-3">';
                        html += '<button class="btn btn-primary btn-sm" onclick="displayResults(window.lastResult)">';
                        html += '<i class="fas fa-arrow-left me-1"></i>返回结果</button>';
                        html += '</div>';
                        html += '</div>';

                        document.getElementById('results-content').innerHTML = html;
                    })
                    .catch(error => alert('搜索失败：' + error.message));
                }
            }

            function resetForm() {
                document.getElementById('upload-form').reset();
                document.getElementById('file-list').innerHTML = '';
                document.getElementById('results').style.display = 'none';
                document.getElementById('processing').style.display = 'none';
            }

            function showHelp() {
                alert(`知识图谱构建器使用说明：

1. 选择文件：支持 TXT、MD 格式的文本文件
2. 选择模式：
   - 合并模式：将所有文档合并为一个知识图谱
   - 批处理模式：为每个文档创建独立的知识图谱
3. 开始处理：系统会自动提取实体和关系
4. 查看结果：可以下载 JSON 数据或搜索特定实体

支持的实体类型：人员、组织、地点、概念、事件、产品等`);
            }
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)


@app.post("/api/process")
async def process_documents(files: List[UploadFile] = File(...), mode: str = Form("merge")):
    """处理上传的文档"""
    global current_pipeline

    if not files:
        raise HTTPException(status_code=400, detail="未上传任何文件")

    # Create temporary directory
    temp_dir = Path(tempfile.mkdtemp())

    try:
        # Save uploaded files
        for file in files:
            if not file.filename.endswith(('.txt', '.md')):
                raise HTTPException(status_code=400, detail=f"不支持的文件格式：{file.filename}")

            file_path = temp_dir / file.filename
            with open(file_path, 'wb') as f:
                content = await file.read()
                f.write(content)

        # Process documents
        pipeline = Pipeline()
        result = await pipeline.process(str(temp_dir), mode=mode)

        # Store pipeline for later queries
        current_pipeline = pipeline

        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理失败：{str(e)}")

    finally:
        # Cleanup temporary directory
        shutil.rmtree(temp_dir, ignore_errors=True)


@app.post("/api/search")
async def search_entities(request: Dict[str, Any]):
    """在当前图谱中搜索实体"""
    global current_pipeline

    if not current_pipeline:
        raise HTTPException(status_code=400, detail="暂无可用图谱，请先处理文档")

    query = request.get("query", "")
    entity_type = request.get("entity_type")

    if not query:
        raise HTTPException(status_code=400, detail="搜索关键词不能为空")

    try:
        results = current_pipeline.search_entities(query, entity_type)
        return results
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"搜索失败：{str(e)}")


@app.get("/api/entity/{entity_name}")
async def get_entity(entity_name: str):
    """获取实体详情和邻居节点"""
    global current_pipeline

    if not current_pipeline:
        raise HTTPException(status_code=400, detail="暂无可用图谱")

    try:
        entity = current_pipeline.get_graph().get_entity_by_name(entity_name)
        if not entity:
            raise HTTPException(status_code=404, detail="未找到该实体")

        neighbors = current_pipeline.get_entity_neighbors(entity_name)

        return {
            "entity": entity,
            "neighbors": neighbors
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取实体信息失败：{str(e)}")


@app.get("/api/statistics")
async def get_statistics():
    """获取当前图谱统计信息"""
    global current_pipeline

    if not current_pipeline:
        return {"message": "暂无可用图谱"}

    try:
        stats = current_pipeline.get_statistics()
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败：{str(e)}")


@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy", "version": "0.1.0", "message": "知识图谱构建器运行正常"}


@app.exception_handler(404)
async def not_found_handler(request: Request, exc):
    """404 错误处理器"""
    html_content = """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <title>页面未找到 - 知识图谱构建器</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/">
                    <i class="fas fa-sitemap me-2"></i>
                    知识图谱构建器
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/">
                        <i class="fas fa-home me-1"></i>
                        首页
                    </a>
                    <a class="nav-link" href="/upload">
                        <i class="fas fa-upload me-1"></i>
                        上传
                    </a>
                    <a class="nav-link" href="/search">
                        <i class="fas fa-search me-1"></i>
                        搜索
                    </a>
                    <a class="nav-link" href="/graph">
                        <i class="fas fa-project-diagram me-1"></i>
                        图谱
                    </a>
                    <a class="nav-link" href="/jobs">
                        <i class="fas fa-tasks me-1"></i>
                        作业
                    </a>
                </div>
            </div>
        </nav>

        <div class="container mt-5">
            <div class="row">
                <div class="col-md-6 mx-auto text-center">
                    <div class="card shadow">
                        <div class="card-body py-5">
                            <i class="fas fa-exclamation-triangle fa-5x text-warning mb-4"></i>
                            <h1 class="display-4">404</h1>
                            <h4 class="mb-3">页面未找到</h4>
                            <p class="text-muted mb-4">
                                抱歉，您访问的页面不存在。可能是链接错误或页面已被移动。
                            </p>
                            <div class="d-grid gap-2">
                                <a href="/" class="btn btn-primary btn-lg">
                                    <i class="fas fa-home me-2"></i>
                                    返回首页
                                </a>
                                <a href="/upload" class="btn btn-outline-primary">
                                    <i class="fas fa-upload me-2"></i>
                                    文档上传
                                </a>
                                <a href="/search" class="btn btn-outline-info">
                                    <i class="fas fa-search me-2"></i>
                                    实体搜索
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content, status_code=404)


def main():
    """启动 Web 服务器"""
    print("🌐 正在启动知识图谱构建器 Web 界面...")
    print("📝 注意：这是一个简化版本，提供基础功能")
    print("🔗 请在浏览器中打开：http://localhost:8001")
    
    try:
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8001,  # 使用不同端口避免冲突
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器错误：{e}")


if __name__ == "__main__":
    main()
