#!/usr/bin/env python3
"""Simple web interface for Knowledge Graph Builder."""

import asyncio
import json
import tempfile
import shutil
from pathlib import Path
from typing import Dict, Any, List

try:
    from fastapi import FastAPI, Request, UploadFile, File, Form, HTTPException
    from fastapi.responses import HTMLResponse, JSONResponse
    from fastapi.templating import Jinja2Templates
    from fastapi.staticfiles import StaticFiles
    import uvicorn
except ImportError as e:
    print(f"❌ Missing web dependencies: {e}")
    print("Please run: python quick_fix.py")
    exit(1)

from core.pipeline import Pipeline
from config.settings import Settings

# Initialize FastAPI app
app = FastAPI(title="Knowledge Graph Builder", version="0.1.0")

# Try to set up templates
try:
    templates = Jinja2Templates(directory="web/templates")
except Exception:
    templates = None

# Global variables
settings = Settings()
current_pipeline = None
processing_jobs = {}


@app.get("/", response_class=HTMLResponse)
async def index(request: Request):
    """Main dashboard page."""
    if templates:
        try:
            return templates.TemplateResponse("index.html", {"request": request})
        except Exception:
            pass
    
    # Fallback HTML
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Knowledge Graph Builder</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/">
                    <i class="fas fa-sitemap me-2"></i>
                    Knowledge Graph Builder
                </a>
            </div>
        </nav>
        
        <div class="container mt-4">
            <div class="row">
                <div class="col-md-8 mx-auto">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-upload me-2"></i>Upload Documents</h5>
                        </div>
                        <div class="card-body">
                            <form id="upload-form" enctype="multipart/form-data">
                                <div class="mb-3">
                                    <label for="files" class="form-label">Select Files</label>
                                    <input type="file" class="form-control" id="files" name="files" multiple accept=".txt,.md">
                                    <div class="form-text">Supported formats: TXT, MD</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="mode" class="form-label">Processing Mode</label>
                                    <select class="form-select" id="mode" name="mode">
                                        <option value="merge">Merge - Single unified graph</option>
                                        <option value="batch">Batch - Separate graphs per document</option>
                                    </select>
                                </div>
                                
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-cogs me-2"></i>
                                    Start Processing
                                </button>
                            </form>
                        </div>
                    </div>
                    
                    <div id="results" class="mt-4" style="display: none;">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-bar me-2"></i>Processing Results</h5>
                            </div>
                            <div class="card-body">
                                <div id="results-content"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
        <script>
            document.getElementById('upload-form').addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const formData = new FormData();
                const files = document.getElementById('files').files;
                const mode = document.getElementById('mode').value;
                
                if (files.length === 0) {
                    alert('Please select at least one file');
                    return;
                }
                
                for (let file of files) {
                    formData.append('files', file);
                }
                formData.append('mode', mode);
                
                try {
                    const response = await fetch('/api/process', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const result = await response.json();
                    
                    if (response.ok) {
                        displayResults(result);
                    } else {
                        alert('Processing failed: ' + result.detail);
                    }
                } catch (error) {
                    alert('Error: ' + error.message);
                }
            });
            
            function displayResults(result) {
                const resultsDiv = document.getElementById('results');
                const contentDiv = document.getElementById('results-content');
                
                let html = '<h6>Processing Summary</h6>';
                html += '<ul>';
                html += `<li>Documents processed: ${result.processing_stats?.documents_processed || 0}</li>`;
                html += `<li>Entities extracted: ${result.processing_stats?.entities_extracted || 0}</li>`;
                html += `<li>Relations extracted: ${result.processing_stats?.relations_extracted || 0}</li>`;
                html += `<li>Processing time: ${(result.processing_stats?.processing_time || 0).toFixed(2)}s</li>`;
                html += '</ul>';
                
                if (result.statistics) {
                    html += '<h6>Graph Statistics</h6>';
                    html += '<ul>';
                    html += `<li>Total entities: ${result.statistics.total_entities || 0}</li>`;
                    html += `<li>Total relations: ${result.statistics.total_relations || 0}</li>`;
                    html += '</ul>';
                }
                
                html += '<div class="mt-3">';
                html += '<button class="btn btn-success me-2" onclick="downloadResults()">Download JSON</button>';
                html += '<button class="btn btn-info" onclick="searchEntities()">Search Entities</button>';
                html += '</div>';
                
                contentDiv.innerHTML = html;
                resultsDiv.style.display = 'block';
                
                // Store result for download
                window.lastResult = result;
            }
            
            function downloadResults() {
                if (window.lastResult) {
                    const dataStr = JSON.stringify(window.lastResult, null, 2);
                    const dataBlob = new Blob([dataStr], {type: 'application/json'});
                    const url = URL.createObjectURL(dataBlob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = 'knowledge_graph.json';
                    link.click();
                    URL.revokeObjectURL(url);
                }
            }
            
            function searchEntities() {
                const query = prompt('Enter search query:');
                if (query) {
                    fetch('/api/search', {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify({query: query})
                    })
                    .then(response => response.json())
                    .then(results => {
                        let html = '<h6>Search Results</h6>';
                        if (results.length > 0) {
                            html += '<ul>';
                            results.forEach(result => {
                                html += `<li><strong>${result.entity.name}</strong> (${result.entity.type}) - Score: ${result.score.toFixed(2)}</li>`;
                            });
                            html += '</ul>';
                        } else {
                            html += '<p>No results found.</p>';
                        }
                        
                        document.getElementById('results-content').innerHTML = html;
                    })
                    .catch(error => alert('Search failed: ' + error.message));
                }
            }
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)


@app.post("/api/process")
async def process_documents(files: List[UploadFile] = File(...), mode: str = Form("merge")):
    """Process uploaded documents."""
    global current_pipeline
    
    if not files:
        raise HTTPException(status_code=400, detail="No files uploaded")
    
    # Create temporary directory
    temp_dir = Path(tempfile.mkdtemp())
    
    try:
        # Save uploaded files
        for file in files:
            if not file.filename.endswith(('.txt', '.md')):
                raise HTTPException(status_code=400, detail=f"Unsupported file format: {file.filename}")
            
            file_path = temp_dir / file.filename
            with open(file_path, 'wb') as f:
                content = await file.read()
                f.write(content)
        
        # Process documents
        pipeline = Pipeline()
        result = await pipeline.process(str(temp_dir), mode=mode)
        
        # Store pipeline for later queries
        current_pipeline = pipeline
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
    finally:
        # Cleanup temporary directory
        shutil.rmtree(temp_dir, ignore_errors=True)


@app.post("/api/search")
async def search_entities(request: Dict[str, Any]):
    """Search entities in the current graph."""
    global current_pipeline
    
    if not current_pipeline:
        raise HTTPException(status_code=400, detail="No graph available. Please process documents first.")
    
    query = request.get("query", "")
    entity_type = request.get("entity_type")
    
    if not query:
        raise HTTPException(status_code=400, detail="Query is required")
    
    try:
        results = current_pipeline.search_entities(query, entity_type)
        return results
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/entity/{entity_name}")
async def get_entity(entity_name: str):
    """Get entity details and neighbors."""
    global current_pipeline
    
    if not current_pipeline:
        raise HTTPException(status_code=400, detail="No graph available")
    
    try:
        entity = current_pipeline.get_graph().get_entity_by_name(entity_name)
        if not entity:
            raise HTTPException(status_code=404, detail="Entity not found")
        
        neighbors = current_pipeline.get_entity_neighbors(entity_name)
        
        return {
            "entity": entity,
            "neighbors": neighbors
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/statistics")
async def get_statistics():
    """Get current graph statistics."""
    global current_pipeline
    
    if not current_pipeline:
        return {"message": "No graph available"}
    
    try:
        stats = current_pipeline.get_statistics()
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "version": "0.1.0"}


def main():
    """Run the web server."""
    print("🌐 Starting Knowledge Graph Builder Web Interface...")
    print("📝 Note: This is a simplified version with basic functionality.")
    print("🔗 Open http://localhost:8001 in your browser")
    
    try:
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8001,  # Use different port to avoid conflicts
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 Server stopped")
    except Exception as e:
        print(f"❌ Server error: {e}")


if __name__ == "__main__":
    main()
