# Knowledge Graph Builder from Text Documents

## Project Requirements

This system builds knowledge graphs from text documents and chat records. It supports document parsing, semantic segmentation, parallel LLM processing, entity/relation extraction, de-duplication, graph merging, HTML wiki generation, and persistent storage with optional search index integration.

Key features:
- **Input Support**: TXT/DOCX/PDF files and chat logs (e.g., WeChat)
- **Parallel Processing**: Segments long texts and distributes to multiple LLMs (`qwen2.5-32b-instruct-int4`, `doubao-seed-1.6`)
- **Graph Management**:
  - Independent per-file graphs (`Batch` mode)
  - Merged unified graph (`Merge` mode)
- **Quality Enhancement**: Entity normalization, deduplication, description/tag enrichment
- **Output Options**:
  - Static HTML Wiki pages
  - JSON/RDF graph formats
  - Neo4j + Cubeflow/Elasticsearch indexed storage
- **Web Interface**: Searchable, filterable, interactive graph visualization

## Utility Functions

1. **File Utilities** (`utils/file_utils.py`)
   - Format detection and parsing (.txt/.docx/.pdf)
   - File content splitting by semantic boundaries

2. **LLM Communication** (`utils/llm_client.py`)
   - Model request formatting
   - API rate limiting & retry handling
   - Response validation and parsing

3. **Knowledge Graph Operations** (`utils/graph_ops.py`)
   - Entity relation parsing
   - Merge logic for nodes/relationships
   - Conflict resolution strategies

4. **Wiki Generation** (`utils/wiki_renderer.py`)
   - Template-based HTML rendering
   - CSS/JS injection support
   - SEO optimization filters

5. **Search Index Integration** (`utils/index_connector.py`)
   - Cubeflow data formatting
   - Elasticsearch schema mapping
   - Batch indexing interface

6. **Security Filters** (`utils/security_filters.py`)
   - PII detection and redaction
   - XSS sanitization
   - Input validation middleware

7. **Async Task Manager** (`utils/task_queue.py`)
   - Rate-limited async executor
   - Task retry mechanism
   - Progress tracking interface

## Flow Design

The architecture is organized as a nested asynchronous flow with batch processing capabilities:

1. **MainFlow**: Top-level async controller managing file list processing
2. **FileProcessingFlow**: Async flow per input file with segment batching
3. **SegmentProcessingBatchFlow**: Parallel LLM task execution for document segments
4. **EntityNormalizationFlow**: Async node chain for entity quality enhancement
5. **GraphBuildFlow**: Mode-switching flow for graph creation strategy
6. **PostProcessingFlow**: Final steps including cleanup and output generation

### Flow Diagram

```mermaid
flowchart TD
    main[MainFlow] -->|for each file| fileProcess[FileProcessingFlow]
    
    subgraph FileProcessingFlow
        parseFile[ParseDocument]
        parseFile --> splitContent[SplitLongContent]
        splitContent --> segmentBatch[SegmentProcessingBatchFlow]
        segmentBatch --> mergeResults[MergeSegmentResults]
        mergeResults --> normalizeEntities[EntityNormalizationFlow]
    end
    
    fileProcess -->|based on mode| graphBuilder[GraphBuildFlow]
    
    subgraph GraphBuildFlow
        decision{Processing Mode}
        decision -->|"Batch"| batchMode[CreateIndependentGraph]
        decision -->|"Merge"| mergeMode[UpdateGlobalGraph]
    end
    
    graphBuilder --> addTags[AddNodeTagsAndDescriptions]
    addTags --> generateWiki[GenerateStaticWikiPage]
    generateWiki --> persistGraph[PersistKnowledgeGraph]
    persistGraph --> cleanTemp[CleanTemporaryFiles]
    
    main --> asyncQueue[AsyncTaskManager]
```

#### Key Execution Strategy Details

- MainFlow orchestrates parallel FileProcessingFlows using `AsyncParallelBatchFlow`
- SegmentProcessingBatchFlow utilizes alternating model routing:
  ```python
  def route_model(index):
      return "qwen" if index % 2 == 0 else "doubao"
  ```
- EntityNormalizationFlow implements priority-weighted merging:
  ```python
  def resolve_conflict(entity_a, entity_b):
      return entity_a if entity_a.source_weight >= entity_b.source_weight else entity_b
  ```
- GraphBuildFlow dynamically selects between two flows based on user configuration:
  ```mermaid
  flowchart LR
      A[BatchMode] --> B[IsolatedSubgraphCreation]
      C[MergeMode] --> D[GlobalGraphUpdateWithConflictResolution]
  ```

## Data Structure

Shared memory structure design:

```python
shared = {
    # Input context metadata
    "context": {
        "input_path": str,
        "processing_mode": str,  # "batch" or "merge"
        "supported_formats": List[str],
        "model_router": Callable,  # LLM selection strategy
    },
    
    # Document-specific data
    "documents": [
        {
            "file_path": str,
            "file_type": str,        # txt/pdf/docx/chat
            "source_weight": int,    # conflict resolution priority
            "content_segments": List[{
                "text": str,
                "start_pos": int,
                "end_pos": int
            }],
            "raw_entities": List[Dict],     # unprocessed entities
            "relations": List[Dict],        # extracted relationships
            "normalized_entities": List[{  # after dedup/merge
                "canonical_name": str,
                "alternatives": List[str],
                "type": str,
                "description": str,
                "tags": List[str]
            }]
        }
    ],
    
    # Graph state
    "knowledge_graph": {
        "nodes": Dict[str, Dict],   # key: canonical name
        "edges": List[Dict],        # relationship definitions
        "index_ready": bool         # controls search indexing trigger
    },
    
    # Output artifacts
    "outputs": {
        "wiki_pages": Dict[str, str],  # {entity_name: html}
        "json_graph": str,             # serialized JSON
        "search_index": str            # Cubeflow/ES-ready format
    },
    
    # Task management
    "task_state": {
        "completed_files": Set[str],
        "failed_segments": Dict[str, List[int]],
        "retry_count": int
    }
}
```

## Node Designs

### 1. ParseDocument
- **Purpose**: Read and validate input files of various formats
- **Design**: AsyncNode(max_retries=3, wait=10s)
- **Data Access**:
  - Read: file_path from shared.context
  - Write: file_type, raw content to shared.documents[]
- **Methods**:
  ```python
  def prep(shared):
      return {"parser": get_parser_for_file(shared["file_path"])}

  def exec_async(prep_res):
      return await prep_res["parser"].parse()  # returns full text

  def post(shared, prep_res, exec_res):
      shared["content"] = exec_res["text"]
      return "default"
  ```

### 2. SplitLongContent
- **Purpose**: Semantic-aware text segmentation for large documents
- **Design**: Node
- **Data Access**:
  - Read: content from shared.documents[]
  - Write: content_segments array to shared.documents[]
- **Implementation**:
  ```python
  def prep(shared):
      return {"chunk_size": estimate_optimal_chunk_size(shared["content"])}

  def exec(prep_res):
      return split_by_semantic_boundaries(prep_res["content"], prep_res["chunk_size"])

  def post(shared, prep_res, exec_res):
      shared["segments"] = exec_res
      return len(exec_res) > 1 ? "needs_parallel" : "default"
  ```

### 3. ProcessSegmentBatch
- **Purpose**: Parallel LLM processing of document segments
- **Design**: AsyncParallelBatchNode(max_concurrent=5, timeout=300s)
- **Data Access**:
  - Read: content_segments from shared.documents[]
  - Write: entities/relations arrays to shared.documents[]
- **Execution Pattern**:
  ```python
  def exec_async(prep_res):
      model = select_model(prep_res["segment_idx"])
      return await call_llm_with_retries(model, prep_res["segment_text"])
  ```

### 4. MergeSegmentResults
- **Purpose**: Aggregate and verify results from parallel LLM calls
- **Design**: BatchNode
- **Data Access**:
  - Read: all segment outputs from shared.documents[]
  - Write: merged entities/relations to shared.documents[]
- **Fallback**:
  ```python
  def exec_fallback(prep_res, exc):
      log_error_and_continue(prep_res, exc)
      return extract_from_failed_segments(prep_res["segments"])
  ```

### 5. NormalizeEntities
- **Purpose**: Entity deduplication and canonical form selection
- **Design**: AsyncNode(use_cache=True)
- **Algorithm**:
  ```python
  def similarity_score(name1, name2):
      return jaro_winkler_similarity(name1, name2) * weight_by_source()
  
  def merge_entities(entities):
      groups = cluster_similar_entities(entities, threshold=0.85)
      return [select_canonical_entity(group) for group in groups]
  ```

### 6. BuildGraphBatch
- **Purpose**: Create isolated knowledge graphs per-file
- **Design**: BatchNode
- **Data Schema**:
  ```python
  def create_node(entity):
      return {
          "id": generate_stable_id(entity["canonical_name"]),
          "name": entity["canonical_name"],
          "type": entity["type"],
          "description": entity.get("description", ""),
          "tags": process_tags(entity.get("tags", []))
      }
  ```

### 7. UpdateGlobalGraph
- **Purpose**: Merge document results into global knowledge graph
- **Design**: AsyncNode
- **Conflict Resolution**:
  ```python
  def update_node(existing, new):
      # Keep most detailed description
      if len(new["description"]) > len(existing["description"]):
          return new
      # Preserve existing tags unless new has higher priority
      elif new["source_weight"] > existing["source_weight"]:
          return merge_with_priority(new, existing)
      return existing
  ```

### 8. GenerateWikiPages
- **Purpose**: Create browsable HTML documentation from knowledge graph
- **Design**: BatchNode(template_engine="Jinja2")
- **Template Features**:
  ```html
  <!-- Entity Card -->
  <div class="entity-card">
    <h2>{{ name }}</h2>
    <span class="tag {% if type == 'person' %}blue{% endif %}">
      {{ type }}
    </span>
    <p>{{ description }}</p>
    <div class="relations">
      Related: {% for rel in relationships %}
        <a href="#{{ rel.target }}">{{ rel.label }}</a>
      {% endfor %}
    </div>
  </div>
  ```

### 9. PersistKnowledgeGraph
- **Purpose**: Store final knowledge graph in multiple formats
- **Design**: AsyncParallelBatchNode
- **Storage Options**:
  ```python
  def save_json(graph):
      return json.dumps(compress_graph(graph))

  def save_neo4j(graph):
      return bulk_import_to_neo4j(graph)

  def index_search(graph):
      if config.use_cubeflow:
          return send_to_cubeflow(graph)
      else:
          return send_to_elasticsearch(graph)
  ```

### 10. CleanTemporaryFiles
- **Purpose**: Remove intermediate processing artifacts
- **Design**: Node
- **Safety Check**:
  ```python
  def pre_delete_check():
      if not final_output_saved():
          raise CriticalError("Attempted cleanup before output persistence completed")
      delete_intermediate_files()
  ```